{"name": "scoot-insights-backend", "version": "1.0.0", "description": "Backend API for Scoot Insights application", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "migrate:files": "ts-node src/scripts/runMigration.ts", "test:db": "ts-node src/scripts/testDbConnection.ts"}, "keywords": ["express", "typescript", "sequelize", "postgresql", "api"], "author": "Scoot Insights Team", "license": "MIT", "dependencies": {"@types/sharp": "^0.31.1", "aws-sdk": "^2.1692.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.2", "sharp": "^0.34.3", "uuid": "^9.0.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}