# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_HOST=marketing-demo-db.chth7m5hjcpr.us-east-1.rds.amazonaws.com
DB_PORT=5432
DB_NAME=scoot
DB_USER=root
DB_PASSWORD=hajk_sd-ASLK.JK890,123

# AWS S3 Configuration

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=fKdYL7ygvmf8+CK7eC+vMQBD5WHjxYPs9eBJ1xni
AWS_REGION=us-east-1
AWS_S3_BUCKET=marketing-demo-bt

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# Logging
LOG_LEVEL=info 