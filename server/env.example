# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=scoot_insights
DB_USER=postgres
DB_PASSWORD=your_password

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# CORS Configuration
# For development: CORS_ORIGIN=http://localhost:5173
# For production: CORS_ORIGIN=https://marketing.bloodandtreasure.com,https://marketing-api.bloodandtreasure.com
CORS_ORIGIN=http://localhost:5173

# Logging
LOG_LEVEL=info 