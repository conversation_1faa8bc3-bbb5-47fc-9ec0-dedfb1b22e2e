import AWS from "aws-sdk";
import dotenv from "dotenv";
import sharp from "sharp";

dotenv.config();

// Configure AWS
AWS.config.update({
  accessKeyId: process.env["AWS_ACCESS_KEY_ID"] || "",
  secretAccessKey: process.env["AWS_SECRET_ACCESS_KEY"] || "",
  region: process.env["AWS_REGION"] || "us-east-1",
});

// Create S3 instance
const s3 = new AWS.S3();

// S3 service class
class S3Service {
  private bucketName: string;
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  private readonly ALLOWED_IMAGE_TYPES = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/webp",
  ];

  constructor() {
    this.bucketName = process.env["AWS_S3_BUCKET"] || "";
    if (!this.bucketName) {
      console.warn(
        "⚠️  AWS_S3_BUCKET environment variable is not set. S3 functionality will be disabled."
      );
    }
  }

  /**
   * Test S3 connection and bucket access
   * @returns Promise<boolean> - true if connection is successful
   */
  async testConnection(): Promise<boolean> {
    if (!this.bucketName) {
      console.error("❌ S3 bucket name not configured");
      return false;
    }

    try {
      await s3.headBucket({ Bucket: this.bucketName }).promise();
      console.log("✅ S3 connection successful");
      return true;
    } catch (error) {
      console.error("❌ S3 connection failed:", error);
      return false;
    }
  }

  /**
   * Check if S3 is properly configured
   * @returns boolean - true if S3 is configured
   */
  isConfigured(): boolean {
    return !!(
      this.bucketName &&
      process.env["AWS_ACCESS_KEY_ID"] &&
      process.env["AWS_SECRET_ACCESS_KEY"]
    );
  }

  /**
   * Validate file type and size
   * @param file - File buffer
   * @param mimeType - MIME type of the file
   * @param originalSize - Original file size in bytes
   */
  private validateFile(
    file: Buffer,
    mimeType: string,
    originalSize: number
  ): void {
    // Check if S3 is configured
    if (!this.isConfigured()) {
      throw new Error(
        "S3 is not properly configured. Please check your environment variables."
      );
    }

    // Check file size
    if (originalSize > this.MAX_FILE_SIZE) {
      throw new Error(
        `File size exceeds maximum limit of ${
          this.MAX_FILE_SIZE / (1024 * 1024)
        }MB`
      );
    }

    // Check file type
    if (!this.ALLOWED_IMAGE_TYPES.includes(mimeType)) {
      throw new Error("Only JPEG, JPG, PNG, GIF, and WebP images are allowed");
    }

    // Basic virus scanning - check for suspicious patterns in file header
    this.scanForSuspiciousPatterns(file);
  }

  /**
   * Basic virus scanning by checking file headers for suspicious patterns
   * @param file - File buffer to scan
   */
  private scanForSuspiciousPatterns(file: Buffer): void {
    // Check for executable file signatures
    const executableSignatures = [
      Buffer.from([0x4d, 0x5a]), // MZ header (Windows executables)
      Buffer.from([0x7f, 0x45, 0x4c, 0x46]), // ELF header (Linux executables)
      Buffer.from([0xfe, 0xed, 0xfa, 0xce]), // Mach-O header (macOS executables)
    ];

    for (const signature of executableSignatures) {
      if (file.subarray(0, signature.length).equals(signature)) {
        throw new Error("Executable files are not allowed");
      }
    }

    // Check for script file signatures
    const scriptSignatures = [
      Buffer.from([0x23, 0x21]), // #! (shebang)
      Buffer.from([0x3c, 0x3f, 0x70, 0x68, 0x70]), // <?php
    ];

    for (const signature of scriptSignatures) {
      if (file.subarray(0, signature.length).equals(signature)) {
        throw new Error("Script files are not allowed");
      }
    }
  }

  /**
   * Optimize image using Sharp
   * @param file - Original image buffer
   * @param mimeType - MIME type of the image
   * @returns Optimized image buffer
   */
  private async optimizeImage(file: Buffer, mimeType: string): Promise<Buffer> {
    try {
      let sharpInstance = sharp(file);

      // Resize large images to reasonable dimensions
      const metadata = await sharpInstance.metadata();
      const maxWidth = 1920;
      const maxHeight = 1080;

      if (metadata.width && metadata.height) {
        if (metadata.width > maxWidth || metadata.height > maxHeight) {
          sharpInstance = sharpInstance.resize(maxWidth, maxHeight, {
            fit: "inside",
            withoutEnlargement: true,
          });
        }
      }

      // Optimize based on file type
      switch (mimeType) {
        case "image/jpeg":
        case "image/jpg":
          return await sharpInstance
            .jpeg({ quality: 85, progressive: true })
            .toBuffer();
        case "image/png":
          return await sharpInstance
            .png({ quality: 85, progressive: true })
            .toBuffer();
        case "image/webp":
          return await sharpInstance.webp({ quality: 85 }).toBuffer();
        case "image/gif":
          // For GIFs, we'll keep them as-is to preserve animation
          return file;
        default:
          return file;
      }
    } catch (error) {
      console.warn("Image optimization failed, using original file:", error);
      return file;
    }
  }

  /**
   * Upload file to S3 with validation and optimization
   * @param file - File buffer or stream
   * @param key - S3 object key (file path)
   * @param contentType - MIME type of the file
   * @param originalSize - Original file size in bytes
   * @returns Promise with the uploaded file URL
   */
  async uploadFile(
    file: Buffer,
    key: string,
    contentType: string,
    originalSize: number
  ): Promise<string> {
    try {
      // Validate file
      this.validateFile(file, contentType, originalSize);

      // Optimize image if it's an image file
      let optimizedFile = file;
      if (this.ALLOWED_IMAGE_TYPES.includes(contentType)) {
        optimizedFile = await this.optimizeImage(file, contentType);
      }

      const params: AWS.S3.PutObjectRequest = {
        Bucket: this.bucketName,
        Key: key,
        Body: optimizedFile,
        ContentType: contentType,
        ACL: "public-read", // Make file publicly accessible
        CacheControl: "max-age=31536000", // Cache for 1 year
      };

      const result = await s3.upload(params).promise();
      console.log(`✅ File uploaded successfully to S3: ${key}`);
      return result.Location;
    } catch (error) {
      console.error("❌ Error uploading file to S3:", error);
      if (error instanceof Error) {
        throw new Error(`Failed to upload file to S3: ${error.message}`);
      }
      throw new Error("Failed to upload file to S3");
    }
  }

  /**
   * Delete file from S3
   * @param key - S3 object key (file path)
   * @returns Promise<void>
   */
  async deleteFile(key: string): Promise<void> {
    if (!this.isConfigured()) {
      console.warn("⚠️  S3 not configured, skipping file deletion");
      return;
    }

    const params: AWS.S3.DeleteObjectRequest = {
      Bucket: this.bucketName,
      Key: key,
    };

    try {
      await s3.deleteObject(params).promise();
      console.log(`✅ File deleted successfully from S3: ${key}`);
    } catch (error) {
      console.error("❌ Error deleting file from S3:", error);
      if (error instanceof Error) {
        throw new Error(`Failed to delete file from S3: ${error.message}`);
      }
      throw new Error("Failed to delete file from S3");
    }
  }

  /**
   * Delete file by URL (extracts key from URL)
   * @param url - Full S3 URL
   * @returns Promise<void>
   */
  async deleteFileByUrl(url: string): Promise<void> {
    const key = this.extractKeyFromUrl(url);
    if (key) {
      await this.deleteFile(key);
    } else {
      console.warn("⚠️  Could not extract S3 key from URL:", url);
    }
  }

  /**
   * Replace existing file with new file
   * @param oldKey - Old S3 object key
   * @param newFile - New file buffer
   * @param newKey - New S3 object key
   * @param contentType - MIME type of the new file
   * @param originalSize - Original file size in bytes
   * @returns Promise with the new file URL
   */
  async replaceFile(
    oldKey: string,
    newFile: Buffer,
    newKey: string,
    contentType: string,
    originalSize: number
  ): Promise<string> {
    try {
      // Upload new file first
      const newUrl = await this.uploadFile(
        newFile,
        newKey,
        contentType,
        originalSize
      );

      // Delete old file if it exists and is different
      if (oldKey !== newKey) {
        try {
          await this.deleteFile(oldKey);
        } catch (error) {
          console.warn("⚠️  Could not delete old file:", error);
        }
      }

      return newUrl;
    } catch (error) {
      console.error("❌ Error replacing file:", error);
      throw error;
    }
  }

  /**
   * Generate unique file key for S3
   * @param originalName - Original file name
   * @param folder - Folder path in S3 (optional)
   * @returns Unique file key
   */
  generateFileKey(originalName: string, folder?: string): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = originalName.split(".").pop();
    const fileName = `${timestamp}-${randomString}.${extension}`;

    return folder ? `${folder}/${fileName}` : fileName;
  }

  /**
   * Get file URL from S3 key
   * @param key - S3 object key
   * @returns Full S3 URL
   */
  getFileUrl(key: string): string {
    return `https://${this.bucketName}.s3.${
      process.env["AWS_REGION"] || "us-east-1"
    }.amazonaws.com/${key}`;
  }

  /**
   * Extract S3 key from URL
   * @param url - Full S3 URL
   * @returns S3 object key
   */
  extractKeyFromUrl(url: string): string {
    if (!url || !this.bucketName) return "";

    const bucketName = this.bucketName;
    const region = process.env["AWS_REGION"] || "us-east-1";
    const pattern = new RegExp(
      `https://${bucketName}\\.s3\\.${region}\\.amazonaws\\.com/(.+)`
    );
    const match = url.match(pattern);
    return match ? match[1] || "" : "";
  }

  /**
   * Check if URL is a valid S3 URL
   * @param url - URL to check
   * @returns boolean - true if it's a valid S3 URL
   */
  isValidS3Url(url: string): boolean {
    return !!this.extractKeyFromUrl(url);
  }
}

export default new S3Service();
