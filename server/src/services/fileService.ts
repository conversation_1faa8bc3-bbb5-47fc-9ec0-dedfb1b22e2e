import s3Service from "./s3Service";
import { File } from "../models";

// Interface for file upload result
interface FileUploadResult {
  id: string;
  originalName: string;
  fileName: string;
  fileKey: string;
  fileUrl: string;
  fileSize: number;
  mimeType: string;
  folder: string;
  uploadedBy?: string;
  createdAt: Date;
}

// Interface for file upload request
interface FileUploadRequest {
  file: Buffer;
  originalName: string;
  mimeType: string;
  fileSize: number;
  folder: string;
  uploadedBy?: string;
}

class FileService {
  /**
   * Upload file to S3 and store metadata in database
   * @param request - File upload request
   * @returns Promise with file upload result
   */
  async uploadFile(request: FileUploadRequest): Promise<FileUploadResult> {
    const { file, originalName, mimeType, fileSize, folder, uploadedBy } =
      request;

    // Check if S3 is configured
    if (!s3Service.isConfigured()) {
      throw new Error(
        "S3 is not properly configured. Please check your environment variables."
      );
    }

    // Generate unique file key
    const fileKey = s3Service.generateFileKey(originalName, folder);

    // Extract file extension and generate unique filename
    const extension = originalName.split(".").pop() || "";
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileName = `${timestamp}-${randomString}.${extension}`;

    try {
      // Upload to S3
      const fileUrl = await s3Service.uploadFile(
        file,
        fileKey,
        mimeType,
        fileSize
      );

      // Store metadata in database
      const createData: any = {
        originalName,
        fileName,
        fileKey,
        fileUrl,
        fileSize,
        mimeType,
        folder,
      };

      if (uploadedBy) {
        createData.uploadedBy = uploadedBy;
      }

      const fileRecord = await File.create(createData);

      return {
        id: fileRecord.id,
        originalName: fileRecord.originalName,
        fileName: fileRecord.fileName,
        fileKey: fileRecord.fileKey,
        fileUrl: fileRecord.fileUrl,
        fileSize: fileRecord.fileSize,
        mimeType: fileRecord.mimeType,
        folder: fileRecord.folder,
        uploadedBy: fileRecord.uploadedBy,
        createdAt: fileRecord.createdAt,
      } as FileUploadResult;
    } catch (error) {
      console.error("❌ Error in file upload service:", error);
      throw error;
    }
  }

  /**
   * Delete file from S3 and database
   * @param fileId - File ID in database
   * @returns Promise<void>
   */
  async deleteFile(fileId: string): Promise<void> {
    try {
      // Find file in database
      const fileRecord = await File.findByPk(fileId);
      if (!fileRecord) {
        throw new Error("File not found in database");
      }

      // Delete from S3
      await s3Service.deleteFile(fileRecord.fileKey);

      // Delete from database
      await fileRecord.destroy();
      console.log(`✅ File deleted successfully: ${fileRecord.originalName}`);
    } catch (error) {
      console.error("❌ Error deleting file:", error);
      throw error;
    }
  }

  /**
   * Delete file by S3 key
   * @param fileKey - S3 object key
   * @returns Promise<void>
   */
  async deleteFileByKey(fileKey: string): Promise<void> {
    try {
      // Find file in database
      const fileRecord = await File.findOne({ where: { fileKey } });
      if (!fileRecord) {
        throw new Error("File not found in database");
      }

      // Delete from S3
      await s3Service.deleteFile(fileKey);

      // Delete from database
      await fileRecord.destroy();
      console.log(`✅ File deleted successfully by key: ${fileKey}`);
    } catch (error) {
      console.error("❌ Error deleting file by key:", error);
      throw error;
    }
  }

  /**
   * Delete file by URL (extracts key from URL)
   * @param fileUrl - Full S3 URL
   * @returns Promise<void>
   */
  async deleteFileByUrl(fileUrl: string): Promise<void> {
    try {
      // Find file in database
      const fileRecord = await File.findOne({ where: { fileUrl } });
      if (!fileRecord) {
        console.warn("⚠️  File not found in database for URL:", fileUrl);
        // Still try to delete from S3 if it's a valid S3 URL
        if (s3Service.isValidS3Url(fileUrl)) {
          await s3Service.deleteFileByUrl(fileUrl);
        }
        return;
      }

      // Delete from S3
      await s3Service.deleteFile(fileRecord.fileKey);

      // Delete from database
      await fileRecord.destroy();
      console.log(`✅ File deleted successfully by URL: ${fileUrl}`);
    } catch (error) {
      console.error("❌ Error deleting file by URL:", error);
      throw error;
    }
  }

  /**
   * Get file by ID
   * @param fileId - File ID
   * @returns Promise with file record or null
   */
  async getFileById(fileId: string): Promise<File | null> {
    return await File.findByPk(fileId);
  }

  /**
   * Get file by S3 key
   * @param fileKey - S3 object key
   * @returns Promise with file record or null
   */
  async getFileByKey(fileKey: string): Promise<File | null> {
    return await File.findOne({ where: { fileKey } });
  }

  /**
   * Get file by URL
   * @param fileUrl - Full S3 URL
   * @returns Promise with file record or null
   */
  async getFileByUrl(fileUrl: string): Promise<File | null> {
    return await File.findOne({ where: { fileUrl } });
  }

  /**
   * Get files by folder
   * @param folder - Folder path
   * @returns Promise with array of file records
   */
  async getFilesByFolder(folder: string): Promise<File[]> {
    return await File.findAll({
      where: { folder },
      order: [["createdAt", "DESC"]],
    });
  }

  /**
   * Update file metadata (e.g., when replacing a file)
   * @param fileId - File ID
   * @param updates - Fields to update
   * @returns Promise with updated file record
   */
  async updateFile(fileId: string, updates: Partial<File>): Promise<File> {
    const fileRecord = await File.findByPk(fileId);
    if (!fileRecord) {
      throw new Error("File not found");
    }

    return await fileRecord.update(updates);
  }

  /**
   * Replace existing file with new file
   * @param fileId - Existing file ID
   * @param newFileRequest - New file upload request
   * @returns Promise with updated file record
   */
  async replaceFile(
    fileId: string,
    newFileRequest: FileUploadRequest
  ): Promise<FileUploadResult> {
    try {
      // Get existing file
      const existingFile = await File.findByPk(fileId);
      if (!existingFile) {
        throw new Error("File not found");
      }

      // Delete old file from S3
      await s3Service.deleteFile(existingFile.fileKey);

      // Upload new file
      const newFileResult = await this.uploadFile(newFileRequest);

      // Update database record with new file info
      await existingFile.update({
        originalName: newFileResult.originalName,
        fileName: newFileResult.fileName,
        fileKey: newFileResult.fileKey,
        fileUrl: newFileResult.fileUrl,
        fileSize: newFileResult.fileSize,
        mimeType: newFileResult.mimeType,
      });

      console.log(
        `✅ File replaced successfully: ${existingFile.originalName}`
      );

      return {
        id: existingFile.id,
        originalName: existingFile.originalName,
        fileName: existingFile.fileName,
        fileKey: existingFile.fileKey,
        fileUrl: existingFile.fileUrl,
        fileSize: existingFile.fileSize,
        mimeType: existingFile.mimeType,
        folder: existingFile.folder,
        uploadedBy: existingFile.uploadedBy,
        createdAt: existingFile.createdAt,
      } as FileUploadResult;
    } catch (error) {
      console.error("❌ Error replacing file:", error);
      throw error;
    }
  }

  /**
   * Replace file by URL (useful for logo replacement)
   * @param oldFileUrl - Old file URL
   * @param newFileRequest - New file upload request
   * @returns Promise with updated file record
   */
  async replaceFileByUrl(
    oldFileUrl: string,
    newFileRequest: FileUploadRequest
  ): Promise<FileUploadResult> {
    try {
      // Find existing file by URL
      const existingFile = await File.findOne({
        where: { fileUrl: oldFileUrl },
      });
      if (!existingFile) {
        // If file not found in database, just upload new file
        console.warn(
          "⚠️  Old file not found in database, uploading new file only"
        );
        return await this.uploadFile(newFileRequest);
      }

      // Use the existing replace method
      return await this.replaceFile(existingFile.id, newFileRequest);
    } catch (error) {
      console.error("❌ Error replacing file by URL:", error);
      throw error;
    }
  }

  /**
   * Clean up orphaned files (files in database but not in S3)
   * @returns Promise with cleanup results
   */
  async cleanupOrphanedFiles(): Promise<{ cleaned: number; errors: number }> {
    const cleaned = 0;
    let errors = 0;

    try {
      const files = await File.findAll();

      for (const file of files) {
        try {
          // Check if file exists in S3
          await s3Service.testConnection();
          // If we can't access the file, it might be orphaned
          // For now, we'll just log it
          console.log(`ℹ️  File in database: ${file.fileKey}`);
        } catch (error) {
          console.warn(`⚠️  Potential orphaned file: ${file.fileKey}`);
          // In a real implementation, you might want to delete these
        }
      }
    } catch (error) {
      console.error("❌ Error during cleanup:", error);
      errors++;
    }

    return { cleaned, errors };
  }
}

export default new FileService();
