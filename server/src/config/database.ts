import { Sequelize } from "sequelize";
import dotenv from "dotenv";

dotenv.config();

// Database configuration
const dbConfig = {
  host: process.env["DB_HOST"] || "localhost",
  port: parseInt(process.env["DB_PORT"] || "5432"),
  database: process.env["DB_NAME"] || "scoot_insights",
  username: process.env["DB_USER"] || "postgres",
  password: process.env["DB_PASSWORD"] || "your_password",
  dialect: "postgres" as const,
  logging: process.env["NODE_ENV"] === "development" ? console.log : false,
  pool: {
    max: 5, // Maximum number of connection instances
    min: 0, // Minimum number of connection instances
    acquire: 30000, // Maximum time, in milliseconds, that pool will try to get connection before throwing error
    idle: 10000, // Maximum time, in milliseconds, that a connection can be idle before being released
  },
  define: {
    timestamps: true, // Adds createdAt and updatedAt timestamps
    underscored: true, // Use snake_case for column names
    freezeTableName: true, // Prevent Sequelize from pluralizing table names
  },
  // SSL configuration for AWS RDS
  dialectOptions: {
    ssl: process.env["DB_HOST"]?.includes("amazonaws.com")
      ? {
          require: true,
          rejectUnauthorized: false, // Set to true in production with proper certificates
        }
      : false,
  },
};

// Create Sequelize instance
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    logging: dbConfig.logging,
    pool: dbConfig.pool,
    define: dbConfig.define,
    dialectOptions: dbConfig.dialectOptions,
  }
);

// Test database connection
export const testConnection = async (): Promise<void> => {
  try {
    await sequelize.authenticate();
    console.log("✅ Database connection has been established successfully.");
  } catch (error) {
    console.error("❌ Unable to connect to the database:", error);
    throw error;
  }
};

// Sync database (create tables if they don't exist)
export const syncDatabase = async (): Promise<void> => {
  try {
    // Use alter for development to update existing tables
    await sequelize.sync({ alter: process.env["NODE_ENV"] === "development" });
    console.log("✅ Database synchronized successfully.");
  } catch (error) {
    console.error("❌ Error synchronizing database:", error);
    throw error;
  }
};

export default sequelize;
