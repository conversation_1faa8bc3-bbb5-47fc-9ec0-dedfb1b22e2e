import { Model, DataTypes } from "sequelize";
import sequelize from "../config/database";
import Client from "./Client";

// Brand model interface
export interface IBrand {
  id: string;
  name: string;
  logo: string;
  endpoint: string;
  clientId: string;
  isDefault: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

// Brand model attributes interface
export interface IBrandAttributes extends IBrand {}

// Brand model creation attributes interface
export interface IBrandCreationAttributes
  extends Omit<IBrand, "id" | "createdAt" | "updatedAt"> {}

// Brand model class
class Brand
  extends Model<IBrandAttributes, IBrandCreationAttributes>
  implements IBrand
{
  public id!: string;
  public name!: string;
  public logo!: string;
  public endpoint!: string;
  public clientId!: string;
  public isDefault!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

// Initialize Brand model
Brand.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
      comment: "Unique identifier for the brand",
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 255],
      },
      comment: "Name of the brand",
    },
    logo: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: true,
      },
      comment: "URL or base64 string of the brand logo",
    },
    endpoint: {
      type: DataTypes.STRING(255),
      allowNull: false,
      // unique: true,
      validate: {
        notEmpty: true,
        len: [1, 255],
        is: /^[a-z0-9-]+$/, // Only lowercase letters, numbers, and hyphens
      },
      comment: "URL-friendly endpoint for the brand",
    },
    clientId: {
      field: "client_id",
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: "client",
        key: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "CASCADE",
      comment: "Foreign key reference to the client",
    },
    isDefault: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: "Whether this brand is the default brand",
    },
  },
  {
    sequelize,
    tableName: "brand",
    modelName: "Brand",
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ["endpoint"],
      },
      {
        fields: ["client_id"],
      },
      {
        fields: ["name"],
      },
      {
        fields: ["is_default"],
      },
    ],
  }
);

// Define relationship: One Client has many Brands
Client.hasMany(Brand, {
  foreignKey: "client_id",
  as: "brands",
  onDelete: "CASCADE",
  onUpdate: "CASCADE",
});

Brand.belongsTo(Client, {
  foreignKey: "client_id",
  as: "client",
  onDelete: "CASCADE",
  onUpdate: "CASCADE",
});

export default Brand;
