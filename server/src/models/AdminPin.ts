import { Model, DataTypes } from "sequelize";
import sequelize from "../config/database";

// AdminPin model interface
export interface IAdminPin {
  id: string;
  pin: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// AdminPin model attributes interface
export interface IAdminPinAttributes extends IAdminPin {}

// AdminPin model creation attributes interface
export interface IAdminPinCreationAttributes
  extends Omit<IAdminPin, "id" | "createdAt" | "updatedAt"> {}

// AdminPin model class
class AdminPin
  extends Model<IAdminPinAttributes, IAdminPinCreationAttributes>
  implements IAdminPin
{
  public id!: string;
  public pin!: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

// Initialize AdminPin model
AdminPin.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
      comment: "Unique identifier for the admin PIN",
    },
    pin: {
      type: DataTypes.STRING(6),
      allowNull: false,
      // unique: true,
      validate: {
        notEmpty: true,
        len: [6, 6],
        is: /^[A-Za-z0-9]{6}$/, // 6 characters, alphanumeric only
      },
      comment: "6-character alphanumeric admin PIN (case insensitive)",
    },
  },
  {
    sequelize,
    tableName: "admin_pins",
    modelName: "AdminPin",
    timestamps: true,
    indexes: [
      {
        // unique: true,
        fields: ["pin"],
      },
    ],
  }
);

export default AdminPin;
