import { Model, DataTypes } from "sequelize";
import sequelize from "../config/database";

// Client model interface
export interface IClient {
  id: string;
  name: string;
  firstName?: string;
  logo: string;
  description?: string;
  enabled: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

// Client model attributes interface
export interface IClientAttributes extends IClient {}

// Client model creation attributes interface
export interface IClientCreationAttributes
  extends Omit<IClient, "id" | "createdAt" | "updatedAt"> {}

// Client model class
class Client
  extends Model<IClientAttributes, IClientCreationAttributes>
  implements IClient
{
  public id!: string;
  public name!: string;
  public firstName?: string;
  public logo!: string;
  public description?: string;
  public enabled!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

// Initialize Client model
Client.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
      comment: "Unique identifier for the client",
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      // unique: true,
      validate: {
        notEmpty: true,
        len: [1, 255],
      },
      comment: "Name of the client",
    },
    firstName: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [0, 100],
      },
      comment: "First name for personalized welcome messages",
    },
    logo: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: true,
      },
      comment: "URL or base64 string of the client logo",
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "Optional description of the client",
    },
    enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: "Whether the client is active/enabled",
    },
  },
  {
    sequelize,
    tableName: "client",
    modelName: "Client",
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ["name"],
      },
      {
        fields: ["enabled"],
      },
    ],
  }
);

export default Client;
