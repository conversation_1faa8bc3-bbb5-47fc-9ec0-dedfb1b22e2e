import { Model, DataTypes, Optional } from "sequelize";
import sequelize from "../config/database";

// Interface for File attributes
interface FileAttributes {
  id: string;
  originalName: string; // Original filename uploaded by user
  fileName: string; // Generated unique filename
  fileKey: string; // S3 object key
  fileUrl: string; // Full S3 URL
  fileSize: number; // File size in bytes
  mimeType: string; // MIME type of the file
  folder: string; // Folder path in S3 (e.g., 'logos/client', 'images/personas')
  uploadedBy?: string; // User who uploaded the file (optional for now)
  createdAt: Date;
  updatedAt: Date;
}

// Interface for File creation (without auto-generated fields)
interface FileCreationAttributes
  extends Optional<FileAttributes, "id" | "createdAt" | "updatedAt"> {}

class File
  extends Model<FileAttributes, FileCreationAttributes>
  implements FileAttributes
{
  public id!: string;
  public originalName!: string;
  public fileName!: string;
  public fileKey!: string;
  public fileUrl!: string;
  public fileSize!: number;
  public mimeType!: string;
  public folder!: string;
  public uploadedBy?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

File.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    originalName: {
      field: "original_name",
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: "Original filename uploaded by user",
    },
    fileName: {
      field: "file_name",
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: "Generated unique filename",
    },
    fileKey: {
      field: "file_key",
      type: DataTypes.STRING(500),
      allowNull: false,
      // unique: true,
      comment: "S3 object key for the file",
    },
    fileUrl: {
      field: "file_url",
      type: DataTypes.STRING(1000),
      allowNull: false,
      comment: "Full S3 URL for the file",
    },
    fileSize: {
      field: "file_size",
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: "File size in bytes",
    },
    mimeType: {
      field: "mime_type",
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: "MIME type of the file",
    },
    folder: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: "Folder path in S3 (e.g., 'logos/client', 'images/personas')",
    },
    uploadedBy: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: "User who uploaded the file (optional for now)",
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: "files",
    timestamps: true,
    indexes: [
      {
        fields: ["file_key"],
        // unique: true,
      },
      {
        fields: ["folder"],
      },
      {
        fields: ["mime_type"],
      },
    ],
  }
);

export default File;
