import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import dotenv from "dotenv";
import routes from "./routes";
import { testConnection, syncDatabase } from "./config/database";
import { seedDefaultData } from "./scripts/seedData";
import s3Service from "./services/s3Service";

// Load environment variables
dotenv.config();

// Create Express app
const app = express();
const PORT = process.env["PORT"] || 3000;

// Security middleware
app.use(helmet());

// CORS configuration
const corsOrigins = process.env["CORS_ORIGIN"]
  ? process.env["CORS_ORIGIN"].split(",").map((origin) => origin.trim())
  : [
      "http://localhost:5173",
      "http://localhost:5174",
      "http://localhost:5175",
      "http://localhost:5176",
      "http://localhost:5177",
      "http://localhost:4173",
      "https://marketing.bloodandtreasure.com",
      "https://marketing-api.bloodandtreasure.com",
    ];

// Log CORS configuration for debugging
console.log("🔗 CORS Origins:", corsOrigins);

app.use(
  cors({
    origin: corsOrigins,
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "Accept",
      "Origin",
      "X-Requested-With",
      "Cache-Control",
      "X-File-Name",
      "X-Admin-Pin",
      "x-admin-pin",
    ],
  })
);

// Logging middleware
app.use(morgan(process.env["NODE_ENV"] === "development" ? "dev" : "combined"));

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// API routes
app.use("", routes);

// Root endpoint
app.get("/", (_req, res) => {
  res.json({
    success: true,
    message: "Scoot Insights Backend API",
    version: "1.0.0",
    timestamp: new Date().toISOString(),
  });
});

// 404 handler
app.use("*", (_req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${_req.originalUrl} not found`,
  });
});

// Global error handler
app.use(
  (
    error: any,
    _req: express.Request,
    res: express.Response,
    _next: express.NextFunction
  ) => {
    console.error("Global error handler:", error);

    res.status(error.status || 500).json({
      success: false,
      message: error.message || "Internal server error",
      ...(process.env["NODE_ENV"] === "development" && { stack: error.stack }),
    });
  }
);

// Start server function
const startServer = async () => {
  try {
    // Test database connection
    await testConnection();

    // Sync database (create tables if they don't exist)
    await syncDatabase();

    // Seed default data
    await seedDefaultData();

    // Test S3 connection
    console.log("🔍 Testing S3 connection...");
    const s3Configured = s3Service.isConfigured();
    if (s3Configured) {
      const s3Connected = await s3Service.testConnection();
      if (s3Connected) {
        console.log("✅ S3 is properly configured and connected");
      } else {
        console.warn(
          "⚠️  S3 is configured but connection failed. File uploads may not work."
        );
      }
    } else {
      console.warn("⚠️  S3 is not configured. File uploads will be disabled.");
      console.log("   To enable S3, set the following environment variables:");
      console.log("   - AWS_ACCESS_KEY_ID");
      console.log("   - AWS_SECRET_ACCESS_KEY");
      console.log("   - AWS_S3_BUCKET");
      console.log("   - AWS_REGION (optional, defaults to us-east-1)");
    }

    // Start listening
    app.listen(PORT, () => {
      console.log(`🚀 Server is running on port ${PORT}`);
      console.log(
        `📊 Environment: ${process.env["NODE_ENV"] || "development"}`
      );
      console.log(`🌐 API Base URL: http://localhost:${PORT}`);
      console.log(
        `🔗 CORS Origin: ${
          process.env["CORS_ORIGIN"] || "http://localhost:5173"
        }`
      );
      console.log(
        `📁 S3 Status: ${s3Configured ? "Configured" : "Not Configured"}`
      );
    });
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
  }
};

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("🛑 SIGTERM received, shutting down gracefully");
  process.exit(0);
});

process.on("SIGINT", () => {
  console.log("🛑 SIGINT received, shutting down gracefully");
  process.exit(0);
});

// Start the server
startServer();
