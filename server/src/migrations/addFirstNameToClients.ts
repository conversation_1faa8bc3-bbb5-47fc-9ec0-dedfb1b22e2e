import { QueryInterface, DataTypes } from "sequelize";

export async function up(queryInterface: QueryInterface): Promise<void> {
  await queryInterface.addColumn("client", "first_name", {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: "First name for personalized welcome messages",
  });
}

export async function down(queryInterface: QueryInterface): Promise<void> {
  await queryInterface.removeColumn("client", "first_name");
}
