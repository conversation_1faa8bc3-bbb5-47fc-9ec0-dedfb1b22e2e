import { QueryInterface, DataTypes } from "sequelize";

export async function up(queryInterface: QueryInterface): Promise<void> {
  await queryInterface.createTable("files", {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    original_name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: "Original filename uploaded by user",
    },
    file_name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: "Generated unique filename",
    },
    file_key: {
      type: DataTypes.STRING(500),
      allowNull: false,
      unique: true,
      comment: "S3 object key for the file",
    },
    file_url: {
      type: DataTypes.STRING(1000),
      allowNull: false,
      comment: "Full S3 URL for the file",
    },
    file_size: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: "File size in bytes",
    },
    mime_type: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: "MIME type of the file",
    },
    folder: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: "Folder path in S3 (e.g., 'logos/client', 'images/personas')",
    },
    uploaded_by: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: "User who uploaded the file (optional for now)",
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  });

  // Create indexes
  await queryInterface.addIndex("files", ["file_key"], {
    unique: true,
    name: "files_file_key_unique",
  });

  await queryInterface.addIndex("files", ["folder"], {
    name: "files_folder_idx",
  });

  await queryInterface.addIndex("files", ["mime_type"], {
    name: "files_mime_type_idx",
  });

  await queryInterface.addIndex("files", ["created_at"], {
    name: "files_created_at_idx",
  });
}

export async function down(queryInterface: QueryInterface): Promise<void> {
  await queryInterface.dropTable("files");
}
