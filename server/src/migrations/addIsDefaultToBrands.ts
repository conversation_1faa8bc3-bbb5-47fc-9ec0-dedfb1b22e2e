import { QueryInterface, DataTypes } from "sequelize";

export async function up(queryInterface: QueryInterface): Promise<void> {
  await queryInterface.addColumn("brand", "is_default", {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: "Whether this brand is the default brand",
  });

  // Add index for better query performance
  await queryInterface.addIndex("brand", ["is_default"], {
    name: "idx_brand_is_default",
  });
}

export async function down(queryInterface: QueryInterface): Promise<void> {
  await queryInterface.removeIndex("brand", "idx_brand_is_default");
  await queryInterface.removeColumn("brand", "is_default");
}
