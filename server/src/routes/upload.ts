import { Router, Request, Response } from "express";
import multer from "multer";
import fileService from "../services/fileService";
import s3Service from "../services/s3Service";
import { validateFileUpload } from "../middleware/validation";
import { requireAdminAuth } from "../middleware/auth";

const router = Router();

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (_req, file, cb) => {
    // Check file type
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
    ];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error("Only JPEG, JPG, PNG, GIF, and WebP files are allowed"));
    }
  },
});

// POST /api/upload/logo - Upload logo file to S3
router.post(
  "/logo",
  requireAdminAuth,
  upload.single("file"),
  validateFileUpload,
  async (req: Request, res: Response): Promise<void> => {
    try {
      if (!req.file) {
        res.status(400).json({
          success: false,
          message: "No file uploaded",
        });
        return;
      }

      // Check if S3 is configured
      if (!s3Service.isConfigured()) {
        res.status(500).json({
          success: false,
          message:
            "S3 is not properly configured. Please check your environment variables.",
        });
        return;
      }

      const { file } = req;
      const { type } = req.body; // 'client' or 'brand'

      // Upload file using file service
      const uploadResult = await fileService.uploadFile({
        file: file.buffer,
        originalName: file.originalname,
        mimeType: file.mimetype,
        fileSize: file.size,
        folder: `logos/${type || "general"}`,
        uploadedBy: req.body.uploadedBy, // Optional user identifier
      });

      res.json({
        success: true,
        data: {
          id: uploadResult.id,
          url: uploadResult.fileUrl,
          key: uploadResult.fileKey,
          originalName: uploadResult.originalName,
          fileName: uploadResult.fileName,
          size: uploadResult.fileSize,
          mimetype: uploadResult.mimeType,
          folder: uploadResult.folder,
          uploadedBy: uploadResult.uploadedBy,
          createdAt: uploadResult.createdAt,
        },
        message: "File uploaded successfully",
      });
    } catch (error) {
      console.error("❌ Error uploading file:", error);

      // Provide more specific error messages
      let errorMessage = "Failed to upload file";
      if (error instanceof Error) {
        if (error.message.includes("S3 is not properly configured")) {
          errorMessage =
            "File storage is not configured. Please contact administrator.";
        } else if (error.message.includes("File size exceeds")) {
          errorMessage = error.message;
        } else if (
          error.message.includes("Only JPEG, JPG, PNG, GIF, and WebP")
        ) {
          errorMessage = error.message;
        } else if (error.message.includes("Failed to upload file to S3")) {
          errorMessage = "Failed to upload file to storage. Please try again.";
        }
      }

      res.status(500).json({
        success: false,
        message: errorMessage,
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// PUT /api/upload/logo/replace - Replace existing logo file
router.put(
  "/logo/replace",
  requireAdminAuth,
  upload.single("file"),
  validateFileUpload,
  async (req: Request, res: Response): Promise<void> => {
    try {
      if (!req.file) {
        res.status(400).json({
          success: false,
          message: "No file uploaded",
        });
        return;
      }

      const { oldLogoUrl, type } = req.body;

      if (!oldLogoUrl) {
        res.status(400).json({
          success: false,
          message: "Old logo URL is required",
        });
        return;
      }

      // Check if S3 is configured
      if (!s3Service.isConfigured()) {
        res.status(500).json({
          success: false,
          message:
            "S3 is not properly configured. Please check your environment variables.",
        });
        return;
      }

      const { file } = req;

      // Replace file using file service
      const replaceResult = await fileService.replaceFileByUrl(oldLogoUrl, {
        file: file.buffer,
        originalName: file.originalname,
        mimeType: file.mimetype,
        fileSize: file.size,
        folder: `logos/${type || "general"}`,
        uploadedBy: req.body.uploadedBy,
      });

      res.json({
        success: true,
        data: {
          id: replaceResult.id,
          url: replaceResult.fileUrl,
          key: replaceResult.fileKey,
          originalName: replaceResult.originalName,
          fileName: replaceResult.fileName,
          size: replaceResult.fileSize,
          mimetype: replaceResult.mimeType,
          folder: replaceResult.folder,
          uploadedBy: replaceResult.uploadedBy,
          createdAt: replaceResult.createdAt,
        },
        message: "Logo replaced successfully",
      });
    } catch (error) {
      console.error("❌ Error replacing logo:", error);

      let errorMessage = "Failed to replace logo";
      if (error instanceof Error) {
        if (error.message.includes("S3 is not properly configured")) {
          errorMessage =
            "File storage is not configured. Please contact administrator.";
        } else if (error.message.includes("File not found")) {
          errorMessage =
            "Original logo not found. Please try uploading a new logo.";
        }
      }

      res.status(500).json({
        success: false,
        message: errorMessage,
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// DELETE /api/upload/logo - Delete logo file from S3
router.delete(
  "/logo",
  requireAdminAuth,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { key, url } = req.query;

      if (!key && !url) {
        res.status(400).json({
          success: false,
          message: "Either file key or URL is required",
        });
        return;
      }

      // Check if S3 is configured
      if (!s3Service.isConfigured()) {
        res.status(500).json({
          success: false,
          message:
            "S3 is not properly configured. Please check your environment variables.",
        });
        return;
      }

      // Delete file using file service
      if (url) {
        await fileService.deleteFileByUrl(url as string);
      } else if (key) {
        await fileService.deleteFileByKey(key as string);
      }

      res.json({
        success: true,
        message: "File deleted successfully",
      });
    } catch (error) {
      console.error("❌ Error deleting file:", error);

      let errorMessage = "Failed to delete file";
      if (error instanceof Error) {
        if (error.message.includes("File not found")) {
          errorMessage = "File not found or already deleted";
        } else if (error.message.includes("S3 is not properly configured")) {
          errorMessage =
            "File storage is not configured. Please contact administrator.";
        }
      }

      res.status(500).json({
        success: false,
        message: errorMessage,
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// POST /api/upload/image - Upload general image file
router.post(
  "/image",
  upload.single("file"),
  validateFileUpload,
  async (req: Request, res: Response): Promise<void> => {
    try {
      if (!req.file) {
        res.status(400).json({
          success: false,
          message: "No file uploaded",
        });
        return;
      }

      // Check if S3 is configured
      if (!s3Service.isConfigured()) {
        res.status(500).json({
          success: false,
          message:
            "S3 is not properly configured. Please check your environment variables.",
        });
        return;
      }

      const { file } = req;
      const { category } = req.body; // 'personas', 'general', etc.

      // Upload file using file service
      const uploadResult = await fileService.uploadFile({
        file: file.buffer,
        originalName: file.originalname,
        mimeType: file.mimetype,
        fileSize: file.size,
        folder: `images/${category || "general"}`,
        uploadedBy: req.body.uploadedBy,
      });

      res.json({
        success: true,
        data: {
          id: uploadResult.id,
          url: uploadResult.fileUrl,
          key: uploadResult.fileKey,
          originalName: uploadResult.originalName,
          fileName: uploadResult.fileName,
          size: uploadResult.fileSize,
          mimetype: uploadResult.mimeType,
          folder: uploadResult.folder,
          uploadedBy: uploadResult.uploadedBy,
          createdAt: uploadResult.createdAt,
        },
        message: "Image uploaded successfully",
      });
    } catch (error) {
      console.error("❌ Error uploading image:", error);

      let errorMessage = "Failed to upload image";
      if (error instanceof Error) {
        if (error.message.includes("S3 is not properly configured")) {
          errorMessage =
            "File storage is not configured. Please contact administrator.";
        } else if (error.message.includes("File size exceeds")) {
          errorMessage = error.message;
        } else if (
          error.message.includes("Only JPEG, JPG, PNG, GIF, and WebP")
        ) {
          errorMessage = error.message;
        }
      }

      res.status(500).json({
        success: false,
        message: errorMessage,
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// PUT /api/upload/replace/:fileId - Replace existing file
router.put(
  "/replace/:fileId",
  upload.single("file"),
  validateFileUpload,
  async (req: Request, res: Response): Promise<void> => {
    try {
      if (!req.file) {
        res.status(400).json({
          success: false,
          message: "No file uploaded",
        });
        return;
      }

      const { fileId } = req.params;
      const { file } = req;

      if (!fileId) {
        res.status(400).json({
          success: false,
          message: "File ID is required",
        });
        return;
      }

      // Check if S3 is configured
      if (!s3Service.isConfigured()) {
        res.status(500).json({
          success: false,
          message:
            "S3 is not properly configured. Please check your environment variables.",
        });
        return;
      }

      // Replace file using file service
      const replaceResult = await fileService.replaceFile(fileId, {
        file: file.buffer,
        originalName: file.originalname,
        mimeType: file.mimetype,
        fileSize: file.size,
        folder: req.body.folder || "general",
        uploadedBy: req.body.uploadedBy,
      });

      res.json({
        success: true,
        data: {
          id: replaceResult.id,
          url: replaceResult.fileUrl,
          key: replaceResult.fileKey,
          originalName: replaceResult.originalName,
          fileName: replaceResult.fileName,
          size: replaceResult.fileSize,
          mimetype: replaceResult.mimeType,
          folder: replaceResult.folder,
          uploadedBy: replaceResult.uploadedBy,
          createdAt: replaceResult.createdAt,
        },
        message: "File replaced successfully",
      });
    } catch (error) {
      console.error("❌ Error replacing file:", error);

      let errorMessage = "Failed to replace file";
      if (error instanceof Error) {
        if (error.message.includes("File not found")) {
          errorMessage = "File not found";
        } else if (error.message.includes("S3 is not properly configured")) {
          errorMessage =
            "File storage is not configured. Please contact administrator.";
        }
      }

      res.status(500).json({
        success: false,
        message: errorMessage,
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// GET /api/upload/files/:folder - Get files by folder
router.get(
  "/files/:folder",
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { folder } = req.params;

      if (!folder) {
        res.status(400).json({
          success: false,
          message: "Folder parameter is required",
        });
        return;
      }

      const files = await fileService.getFilesByFolder(folder);

      res.json({
        success: true,
        data: files,
        message: "Files retrieved successfully",
      });
    } catch (error) {
      console.error("❌ Error retrieving files:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retrieve files",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// GET /api/upload/status - Check S3 connection status
router.get("/status", async (_req: Request, res: Response): Promise<void> => {
  try {
    const isConfigured = s3Service.isConfigured();
    const isConnected = isConfigured ? await s3Service.testConnection() : false;

    res.json({
      success: true,
      data: {
        configured: isConfigured,
        connected: isConnected,
        bucket: process.env["AWS_S3_BUCKET"] || null,
        region: process.env["AWS_REGION"] || "us-east-1",
      },
      message: isConfigured
        ? isConnected
          ? "S3 is properly configured and connected"
          : "S3 is configured but connection failed"
        : "S3 is not properly configured",
    });
  } catch (error) {
    console.error("❌ Error checking S3 status:", error);
    res.status(500).json({
      success: false,
      message: "Failed to check S3 status",
      error: process.env["NODE_ENV"] === "development" ? error : undefined,
    });
  }
});

// Error handling middleware for multer
router.use((error: any, _req: Request, res: Response, next: any): void => {
  if (error instanceof multer.MulterError) {
    if (error.code === "LIMIT_FILE_SIZE") {
      res.status(400).json({
        success: false,
        message: "File size too large. Maximum size is 10MB.",
      });
      return;
    }
  }

  if (error.message) {
    res.status(400).json({
      success: false,
      message: error.message,
    });
    return;
  }

  next(error);
});

export default router;
