import { Router, Request, Response } from "express";
import { AdminPin } from "../models";
import { body, validationResult } from "express-validator";

const router = Router();

// Validation error handler
const handleValidationErrors = (
  req: Request,
  res: Response,
  next: any
): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      message: "Validation failed",
      errors: errors.array().map((error) => ({
        field: error.type === "field" ? error.path : "unknown",
        message: error.msg,
        value: "value" in error ? error.value : undefined,
      })),
    });
    return;
  }
  next();
};

// Validation rules for PIN
const validatePin = [
  body("pin")
    .trim()
    .isLength({ min: 6, max: 6 })
    .withMessage("PIN must be exactly 6 characters")
    .matches(/^[A-Za-z0-9]{6}$/)
    .withMessage("PIN can only contain letters and numbers"),
  handleValidationErrors,
];

// POST /api/admin/validate-pin - Validate admin PIN
router.post(
  "/validate-pin",
  validatePin,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { pin } = req.body;

      // Find admin PIN (case insensitive)
      const adminPin = await AdminPin.findOne({
        where: {
          pin: pin.toUpperCase(),
        },
      });

      if (!adminPin) {
        res.status(401).json({
          success: false,
          message: "Invalid PIN",
        });
        return;
      }

      res.json({
        success: true,
        message: "PIN validated successfully",
      });
    } catch (error) {
      console.error("Error validating PIN:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// GET /api/admin/pin-exists - Check if admin PIN exists
router.get(
  "/pin-exists",
  async (_req: Request, res: Response): Promise<void> => {
    try {
      const adminPin = await AdminPin.findOne();

      res.json({
        success: true,
        data: {
          exists: !!adminPin,
        },
        message: "PIN existence checked successfully",
      });
    } catch (error) {
      console.error("Error checking PIN existence:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// POST /api/admin/set-pin - Set admin PIN (for first-time setup)
router.post(
  "/set-pin",
  validatePin,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { pin } = req.body;

      // Check if PIN already exists
      const existingPin = await AdminPin.findOne();
      if (existingPin) {
        res.status(409).json({
          success: false,
          message: "Admin PIN already exists",
        });
        return;
      }

      // Create new admin PIN
      const adminPin = await AdminPin.create({
        pin: pin.toUpperCase(),
      });

      res.status(201).json({
        success: true,
        data: {
          id: adminPin.id,
          pin: adminPin.pin,
        },
        message: "Admin PIN created successfully",
      });
    } catch (error) {
      console.error("Error creating admin PIN:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// PUT /api/admin/update-pin - Update admin PIN
router.put(
  "/update-pin",
  validatePin,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { pin, currentPin } = req.body;

      // Validate current PIN first
      const existingPin = await AdminPin.findOne({
        where: {
          pin: currentPin.toUpperCase(),
        },
      });

      if (!existingPin) {
        res.status(401).json({
          success: false,
          message: "Current PIN is incorrect",
        });
        return;
      }

      // Update PIN
      await existingPin.update({
        pin: pin.toUpperCase(),
      });

      res.json({
        success: true,
        message: "Admin PIN updated successfully",
      });
    } catch (error) {
      console.error("Error updating admin PIN:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

export default router;
