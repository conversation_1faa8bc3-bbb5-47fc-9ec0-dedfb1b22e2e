import { Router, Request, Response } from "express";
import { Client } from "../models";
import fileService from "../services/fileService";
import s3Service from "../services/s3Service";
import {
  validateCreateClient,
  validateUpdateClient,
  validateClientId,
} from "../middleware/validation";
import { requireAdminAuth } from "../middleware/auth";

const router = Router();

// GET /api/clients - Get all clients
router.get("/", async (_req: Request, res: Response): Promise<void> => {
  try {
    const clients = await Client.findAll({
      where: { enabled: true },
      order: [["createdAt", "DESC"]],
    });

    res.json({
      success: true,
      data: clients,
      message: "Clients retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching clients:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: process.env["NODE_ENV"] === "development" ? error : undefined,
    });
  }
});

// GET /api/clients/:id - Get client by ID
router.get(
  "/:id",
  validateClientId,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const client = await Client.findByPk(id);

      if (!client) {
        res.status(404).json({
          success: false,
          message: "Client not found",
        });
        return;
      }

      res.json({
        success: true,
        data: client,
        message: "Client retrieved successfully",
      });
    } catch (error) {
      console.error("Error fetching client:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// POST /api/clients - Create new client
router.post(
  "/",
  requireAdminAuth,
  validateCreateClient,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { name, firstName, logo, description, enabled = true } = req.body;

      // Check if client with same name already exists
      const existingClient = await Client.findOne({ where: { name } });
      if (existingClient) {
        res.status(409).json({
          success: false,
          message: "Client with this name already exists",
        });
        return;
      }

      const client = await Client.create({
        name,
        firstName,
        logo,
        description,
        enabled,
      });

      res.status(201).json({
        success: true,
        data: client,
        message: "Client created successfully",
      });
    } catch (error) {
      console.error("Error creating client:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// PUT /api/clients/:id - Update client
router.put(
  "/:id",
  requireAdminAuth,
  validateUpdateClient,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { name, firstName, logo, description, enabled } = req.body;

      const client = await Client.findByPk(id);
      if (!client) {
        res.status(404).json({
          success: false,
          message: "Client not found",
        });
        return;
      }

      // Check if name is being updated and if it conflicts with existing client
      if (name && name !== client.name) {
        const existingClient = await Client.findOne({ where: { name } });
        if (existingClient) {
          res.status(409).json({
            success: false,
            message: "Client with this name already exists",
          });
          return;
        }
      }

      // Handle logo replacement if logo is being updated
      const finalLogo = logo || client.logo;
      if (logo && logo !== client.logo && s3Service.isValidS3Url(client.logo)) {
        try {
          // Delete old logo from S3 if it's a valid S3 URL
          await fileService.deleteFileByUrl(client.logo);
          console.log(`✅ Old client logo deleted from S3: ${client.logo}`);
        } catch (error) {
          console.warn("⚠️  Could not delete old client logo:", error);
        }
      }

      // Update client
      await client.update({
        name: name || client.name,
        firstName: firstName !== undefined ? firstName : client.firstName,
        logo: finalLogo,
        description:
          description !== undefined ? description : client.description,
        enabled: enabled !== undefined ? enabled : client.enabled,
      });

      res.json({
        success: true,
        data: client,
        message: "Client updated successfully",
      });
    } catch (error) {
      console.error("Error updating client:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// DELETE /api/clients/:id - Delete client (soft delete by setting enabled to false)
router.delete(
  "/:id",
  requireAdminAuth,
  validateClientId,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const client = await Client.findByPk(id);

      if (!client) {
        res.status(404).json({
          success: false,
          message: "Client not found",
        });
        return;
      }

      // Delete logo from S3 if it's a valid S3 URL
      if (s3Service.isValidS3Url(client.logo)) {
        try {
          await fileService.deleteFileByUrl(client.logo);
          console.log(`✅ Client logo deleted from S3: ${client.logo}`);
        } catch (error) {
          console.warn("⚠️  Could not delete client logo from S3:", error);
        }
      }

      // Soft delete by setting enabled to false
      await client.update({ enabled: false });

      res.json({
        success: true,
        message: "Client deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting client:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

export default router;
