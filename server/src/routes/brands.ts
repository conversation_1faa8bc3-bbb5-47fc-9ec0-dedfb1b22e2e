import { Router, Request, Response } from "express";
import { Brand, Client } from "../models";
import fileService from "../services/fileService";
import s3Service from "../services/s3Service";
import {
  validateCreateBrand,
  validateUpdateBrand,
  validateBrandId,
} from "../middleware/validation";
import { requireAdminAuth } from "../middleware/auth";

const router = Router();

// GET /api/brands - Get all brands with client information
router.get("/", async (_req: Request, res: Response): Promise<void> => {
  try {
    const brands = await Brand.findAll({
      include: [
        {
          model: Client,
          as: "client",
          attributes: ["id", "name", "firstName", "logo"],
        },
      ],
      order: [["createdAt", "DESC"]],
    });

    res.json({
      success: true,
      data: brands,
      message: "Brands retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching brands:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: process.env["NODE_ENV"] === "development" ? error : undefined,
    });
  }
});

// GET /api/brands/:id - Get brand by ID with client information
router.get(
  "/:id",
  validateBrandId,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const brand = await Brand.findByPk(id, {
        include: [
          {
            model: Client,
            as: "client",
            attributes: ["id", "name", "firstName", "logo"],
          },
        ],
      });

      if (!brand) {
        res.status(404).json({
          success: false,
          message: "Brand not found",
        });
        return;
      }

      res.json({
        success: true,
        data: brand,
        message: "Brand retrieved successfully",
      });
    } catch (error) {
      console.error("Error fetching brand:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// GET /api/brands/client/:clientId - Get brands by client ID
router.get(
  "/client/:clientId",
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { clientId } = req.params;
      const brands = await Brand.findAll({
        where: { clientId },
        include: [
          {
            model: Client,
            as: "client",
            attributes: ["id", "name", "firstName", "logo"],
          },
        ],
        order: [["createdAt", "DESC"]],
      });

      res.json({
        success: true,
        data: brands,
        message: "Brands retrieved successfully",
      });
    } catch (error) {
      console.error("Error fetching brands by client:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// GET /api/brands/endpoint/:endpoint - Get brand by endpoint
router.get(
  "/endpoint/:endpoint",
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { endpoint } = req.params;
      const brand = await Brand.findOne({
        where: { endpoint },
        include: [
          {
            model: Client,
            as: "client",
            attributes: ["id", "name", "firstName", "logo"],
          },
        ],
      });

      if (!brand) {
        res.status(404).json({
          success: false,
          message: "Brand not found",
        });
        return;
      }

      res.json({
        success: true,
        data: brand,
        message: "Brand retrieved successfully",
      });
    } catch (error) {
      console.error("Error fetching brand by endpoint:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// GET /api/brands/default - Get default brand
router.get("/default", async (_req: Request, res: Response): Promise<void> => {
  try {
    const brand = await Brand.findOne({
      where: { isDefault: true },
      include: [
        {
          model: Client,
          as: "client",
          attributes: ["id", "name", "firstName", "logo"],
        },
      ],
    });

    if (!brand) {
      res.status(404).json({
        success: false,
        message: "Default brand not found",
      });
      return;
    }

    res.json({
      success: true,
      data: brand,
      message: "Default brand retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching default brand:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: process.env["NODE_ENV"] === "development" ? error : undefined,
    });
  }
});

// GET /api/brands/client/:clientId/count - Get brand count by client ID
router.get(
  "/client/:clientId/count",
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { clientId } = req.params;
      const count = await Brand.count({
        where: { clientId },
      });

      res.json({
        success: true,
        data: { count },
        message: "Brand count retrieved successfully",
      });
    } catch (error) {
      console.error("Error fetching brand count by client:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// POST /api/brands - Create new brand
router.post(
  "/",
  requireAdminAuth,
  validateCreateBrand,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { name, logo, endpoint, clientId, isDefault } = req.body;

      // Check if client exists
      const client = await Client.findByPk(clientId);
      if (!client) {
        res.status(404).json({
          success: false,
          message: "Client not found",
        });
        return;
      }

      // Check if brand with same endpoint already exists
      const existingBrand = await Brand.findOne({ where: { endpoint } });
      if (existingBrand) {
        res.status(409).json({
          success: false,
          message: "Brand with this endpoint already exists",
        });
        return;
      }

      // If this brand is being set as default, unset any existing default brand
      if (isDefault) {
        await Brand.update(
          { isDefault: false },
          { where: { isDefault: true } }
        );
      }

      const brand = await Brand.create({
        name,
        logo,
        endpoint,
        clientId,
        isDefault: isDefault || false,
      });

      // Fetch the created brand with client information
      const createdBrand = await Brand.findByPk(brand.id, {
        include: [
          {
            model: Client,
            as: "client",
            attributes: ["id", "name", "firstName", "logo"],
          },
        ],
      });

      res.status(201).json({
        success: true,
        data: createdBrand,
        message: "Brand created successfully",
      });
    } catch (error) {
      console.error("Error creating brand:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// PUT /api/brands/:id - Update brand
router.put(
  "/:id",
  requireAdminAuth,
  validateUpdateBrand,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { name, logo, endpoint, clientId, isDefault } = req.body;

      const brand = await Brand.findByPk(id);
      if (!brand) {
        res.status(404).json({
          success: false,
          message: "Brand not found",
        });
        return;
      }

      // Check if client exists if clientId is being updated
      if (clientId && clientId !== brand.clientId) {
        const client = await Client.findByPk(clientId);
        if (!client) {
          res.status(404).json({
            success: false,
            message: "Client not found",
          });
          return;
        }
      }

      // Check if endpoint is being updated and if it conflicts with existing brand
      if (endpoint && endpoint !== brand.endpoint) {
        const existingBrand = await Brand.findOne({ where: { endpoint } });
        if (existingBrand) {
          res.status(409).json({
            success: false,
            message: "Brand with this endpoint already exists",
          });
          return;
        }
      }

      // If this brand is being set as default, unset any existing default brand
      if (isDefault && !brand.isDefault) {
        await Brand.update(
          { isDefault: false },
          { where: { isDefault: true } }
        );
      }

      // Handle logo replacement if logo is being updated
      const finalLogo = logo || brand.logo;
      if (logo && logo !== brand.logo && s3Service.isValidS3Url(brand.logo)) {
        try {
          // Delete old logo from S3 if it's a valid S3 URL
          await fileService.deleteFileByUrl(brand.logo);
          console.log(`✅ Old brand logo deleted from S3: ${brand.logo}`);
        } catch (error) {
          console.warn("⚠️  Could not delete old brand logo:", error);
        }
      }

      // Update brand
      await brand.update({
        name: name || brand.name,
        logo: finalLogo,
        endpoint: endpoint || brand.endpoint,
        clientId: clientId || brand.clientId,
        isDefault: isDefault !== undefined ? isDefault : brand.isDefault,
      });

      // Fetch the updated brand with client information
      const updatedBrand = await Brand.findByPk(id, {
        include: [
          {
            model: Client,
            as: "client",
            attributes: ["id", "name", "firstName", "logo"],
          },
        ],
      });

      res.json({
        success: true,
        data: updatedBrand,
        message: "Brand updated successfully",
      });
    } catch (error) {
      console.error("Error updating brand:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

// DELETE /api/brands/:id - Delete brand
router.delete(
  "/:id",
  requireAdminAuth,
  validateBrandId,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const brand = await Brand.findByPk(id);

      if (!brand) {
        res.status(404).json({
          success: false,
          message: "Brand not found",
        });
        return;
      }

      // Prevent deletion of default brand
      if (brand.isDefault) {
        res.status(400).json({
          success: false,
          message:
            "Cannot delete the default brand. Please set another brand as default first.",
        });
        return;
      }

      // Delete logo from S3 if it's a valid S3 URL
      if (s3Service.isValidS3Url(brand.logo)) {
        try {
          await fileService.deleteFileByUrl(brand.logo);
          console.log(`✅ Brand logo deleted from S3: ${brand.logo}`);
        } catch (error) {
          console.warn("⚠️  Could not delete brand logo from S3:", error);
        }
      }

      await brand.destroy();

      res.json({
        success: true,
        message: "Brand deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting brand:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: process.env["NODE_ENV"] === "development" ? error : undefined,
      });
    }
  }
);

export default router;
