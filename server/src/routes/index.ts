import { Router, Request, Response } from "express";
import clientRoutes from "./clients";
import brandRoutes from "./brands";
import uploadRoutes from "./upload";
import adminRoutes from "./admin";

const router = Router();

// API routes
router.use("/clients", clientRoutes);
router.use("/brands", brandRoutes);
router.use("/upload", uploadRoutes);
router.use("/admin", adminRoutes);

// Health check endpoint
router.get("/health", (_req: Request, res: Response) => {
  res.json({
    success: true,
    message: "API is running",
    timestamp: new Date().toISOString(),
    environment: process.env["NODE_ENV"] || "development",
  });
});

export default router;
