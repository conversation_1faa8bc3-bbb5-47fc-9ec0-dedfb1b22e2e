import { Request, Response, NextFunction } from "express";
import { AdminPin } from "../models";

// Extend Request interface to include admin authentication
declare global {
  namespace Express {
    interface Request {
      isAdminAuthenticated?: boolean;
    }
  }
}

/**
 * Middleware to validate admin PIN for protected routes
 * Expects PIN in request headers: 'X-Admin-PIN'
 */
export const requireAdminAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const adminPin = req.headers["x-admin-pin"] as string;

    if (!adminPin) {
      res.status(401).json({
        success: false,
        message: "Admin PIN is required",
      });
      return;
    }

    // Find admin PIN (case insensitive)
    const pinRecord = await AdminPin.findOne({
      where: {
        pin: adminPin.toUpperCase(),
      },
    });

    if (!pinRecord) {
      res.status(401).json({
        success: false,
        message: "Invalid admin PIN",
      });
      return;
    }

    // Mark request as authenticated
    req.isAdminAuthenticated = true;
    next();
  } catch (error) {
    console.error("Error in admin authentication:", error);
    res.status(500).json({
      success: false,
      message: "Authentication error",
      error: process.env["NODE_ENV"] === "development" ? error : undefined,
    });
  }
};

/**
 * Optional middleware to check if admin is authenticated
 * Doesn't block the request, just sets the flag
 */
export const checkAdminAuth = async (
  req: Request,
  _res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const adminPin = req.headers["x-admin-pin"] as string;

    if (adminPin) {
      const pinRecord = await AdminPin.findOne({
        where: {
          pin: adminPin.toUpperCase(),
        },
      });

      req.isAdminAuthenticated = !!pinRecord;
    } else {
      req.isAdminAuthenticated = false;
    }

    next();
  } catch (error) {
    console.error("Error checking admin authentication:", error);
    req.isAdminAuthenticated = false;
    next();
  }
};

/**
 * Helper function to check if admin PIN exists
 */
export const adminPinExists = async (): Promise<boolean> => {
  try {
    const pinRecord = await AdminPin.findOne();
    return !!pinRecord;
  } catch (error) {
    console.error("Error checking if admin PIN exists:", error);
    return false;
  }
};
