import { Request, Response, NextFunction } from "express";
import { body, param, validationResult } from "express-validator";

// Validation error handler middleware
export const handleValidationErrors = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      message: "Validation failed",
      errors: errors.array().map((error) => ({
        field: error.type === "field" ? error.path : "unknown",
        message: error.msg,
        value: "value" in error ? error.value : undefined,
      })),
    });
    return;
  }
  next();
};

// Client validation rules
export const validateCreateClient = [
  body("name")
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage("Client name must be between 1 and 255 characters")
    .matches(/^[a-zA-Z0-9\s\-_]+$/)
    .withMessage(
      "Client name can only contain letters, numbers, spaces, hyphens, and underscores"
    ),

  body("firstName")
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage("First name must be less than 100 characters")
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage("First name can only contain letters and spaces"),

  body("logo").trim().isLength({ min: 1 }).withMessage("Logo URL is required"),

  body("description")
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage("Description must be less than 1000 characters"),

  body("enabled")
    .optional()
    .isBoolean()
    .withMessage("Enabled must be a boolean value"),

  handleValidationErrors,
];

export const validateUpdateClient = [
  param("id").isUUID().withMessage("Invalid client ID format"),

  body("name")
    .optional()
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage("Client name must be between 1 and 255 characters")
    .matches(/^[a-zA-Z0-9\s\-_]+$/)
    .withMessage(
      "Client name can only contain letters, numbers, spaces, hyphens, and underscores"
    ),

  body("firstName")
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage("First name must be less than 100 characters")
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage("First name can only contain letters and spaces"),

  body("logo")
    .optional()
    .trim()
    .isLength({ min: 1 })
    .withMessage("Logo URL is required"),

  body("description")
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage("Description must be less than 1000 characters"),

  body("enabled")
    .optional()
    .isBoolean()
    .withMessage("Enabled must be a boolean value"),

  handleValidationErrors,
];

export const validateClientId = [
  param("id").isUUID().withMessage("Invalid client ID format"),

  handleValidationErrors,
];

// Brand validation rules
export const validateCreateBrand = [
  body("name")
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage("Brand name must be between 1 and 255 characters")
    .matches(/^[a-zA-Z0-9\s\-_]+$/)
    .withMessage(
      "Brand name can only contain letters, numbers, spaces, hyphens, and underscores"
    ),

  body("logo").trim().isLength({ min: 1 }).withMessage("Logo URL is required"),

  body("endpoint")
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage("Endpoint must be between 1 and 255 characters")
    .matches(/^[a-z0-9-]+$/)
    .withMessage(
      "Endpoint can only contain lowercase letters, numbers, and hyphens"
    ),

  body("clientId").isUUID().withMessage("Invalid client ID format"),

  body("isDefault")
    .optional()
    .isBoolean()
    .withMessage("isDefault must be a boolean value"),

  handleValidationErrors,
];

export const validateUpdateBrand = [
  param("id").isUUID().withMessage("Invalid brand ID format"),

  body("name")
    .optional()
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage("Brand name must be between 1 and 255 characters")
    .matches(/^[a-zA-Z0-9\s\-_]+$/)
    .withMessage(
      "Brand name can only contain letters, numbers, spaces, hyphens, and underscores"
    ),

  body("logo")
    .optional()
    .trim()
    .isLength({ min: 1 })
    .withMessage("Logo URL is required"),

  body("endpoint")
    .optional()
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage("Endpoint must be between 1 and 255 characters")
    .matches(/^[a-z0-9-]+$/)
    .withMessage(
      "Endpoint can only contain lowercase letters, numbers, and hyphens"
    ),

  body("clientId").optional().isUUID().withMessage("Invalid client ID format"),

  body("isDefault")
    .optional()
    .isBoolean()
    .withMessage("isDefault must be a boolean value"),

  handleValidationErrors,
];

export const validateBrandId = [
  param("id").isUUID().withMessage("Invalid brand ID format"),

  handleValidationErrors,
];

// File upload validation
export const validateFileUpload = [
  body("file").custom((_value, { req }) => {
    if (!req["file"]) {
      throw new Error("File is required");
    }

    // Check file type
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
    ];
    if (!allowedTypes.includes(req["file"].mimetype)) {
      throw new Error("Only JPEG, JPG, PNG, GIF, and WebP files are allowed");
    }

    // Check file size (10MB = 10 * 1024 * 1024 bytes)
    const maxSize = 10 * 1024 * 1024;
    if (req["file"].size > maxSize) {
      throw new Error("File size must be less than 10MB");
    }

    return true;
  }),

  handleValidationErrors,
];
