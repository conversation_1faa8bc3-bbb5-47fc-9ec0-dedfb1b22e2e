import { QueryInterface, DataTypes } from "sequelize";
import sequelize from "../config/database";

async function fixFirstNameColumn(): Promise<void> {
  const queryInterface: QueryInterface = sequelize.getQueryInterface();

  try {
    console.log("🔄 Fixing firstName column in clients table...");

    // First, check if the wrong column exists and drop it
    try {
      await queryInterface.removeColumn("client", "firstName");
      console.log("✅ Removed incorrect 'firstName' column");
    } catch (error) {
      console.log("ℹ️  'firstName' column doesn't exist or already removed");
    }

    // Then add the correct column
    await queryInterface.addColumn("client", "first_name", {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: "First name for personalized welcome messages",
    });

    console.log(
      "✅ Successfully added correct 'first_name' column to clients table"
    );
  } catch (error) {
    console.error("❌ Error fixing firstName column:", error);
    throw error;
  }
}

// Run the fix
fixFirstNameColumn()
  .then(() => {
    console.log("🎉 Column fix completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Column fix failed:", error);
    process.exit(1);
  });
