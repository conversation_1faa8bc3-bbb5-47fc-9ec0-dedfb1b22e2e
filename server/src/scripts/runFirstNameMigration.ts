import { QueryInterface, DataTypes } from "sequelize";
import sequelize from "../config/database";

async function addFirstNameToClients(): Promise<void> {
  const queryInterface: QueryInterface = sequelize.getQueryInterface();

  try {
    console.log("🔄 Adding firstName column to clients table...");

    await queryInterface.addColumn("client", "first_name", {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: "First name for personalized welcome messages",
    });

    console.log("✅ Successfully added firstName column to clients table");
  } catch (error) {
    console.error("❌ Error adding firstName column:", error);
    throw error;
  }
}

// Run the migration
addFirstNameToClients()
  .then(() => {
    console.log("🎉 Migration completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Migration failed:", error);
    process.exit(1);
  });
