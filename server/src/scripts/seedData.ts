import { C<PERSON>, Brand, AdminPin } from "../models";

/**
 * Seed default data for the application
 */
export async function seedDefaultData(): Promise<void> {
  try {
    console.log("🌱 Starting database seeding...");

    // Create default admin PIN if it doesn't exist
    const existingPin = await AdminPin.findOne();
    if (!existingPin) {
      await AdminPin.create({
        pin: "ADMIN1", // Default 6-character PIN
      });
      console.log("✅ Default admin PIN created: ADMIN1");
    } else {
      console.log("ℹ️  Admin PIN already exists");
    }

    // Create default client if it doesn't exist
    const existingClient = await Client.findOne({
      where: { name: "Scoot Insights" },
    });

    let defaultClient: Client;
    if (!existingClient) {
      // Use a placeholder logo URL for now - will be replaced when actual logo is uploaded
      const defaultLogoUrl =
        "https://via.placeholder.com/200x200/3B82F6/FFFFFF?text=Scoot+Insights";

      defaultClient = await C<PERSON>.create({
        name: "Scoot Insights",
        logo: defaultLogoUrl,
        description: "Default client for Scoot Insights platform",
        enabled: true,
      });
      console.log("✅ Default client 'Scoot Insights' created");
    } else {
      defaultClient = existingClient;
      console.log("ℹ️  Default client 'Scoot Insights' already exists");
    }

    // Create default brands if they don't exist
    const defaultBrands = [
      {
        name: "Balsam",
        logo: "https://via.placeholder.com/200x200/10B981/FFFFFF?text=Balsam",
        endpoint: "balsam",
      },
      {
        name: "White Wall",
        logo: "https://via.placeholder.com/200x200/6B7280/FFFFFF?text=White+Wall",
        endpoint: "white-wall",
      },
      {
        name: "Blood & Treasure",
        logo: "https://via.placeholder.com/200x200/DC2626/FFFFFF?text=Blood+%26+Treasure",
        endpoint: "blood-treasure",
      },
    ];

    for (const brandData of defaultBrands) {
      const existingBrand = await Brand.findOne({
        where: { name: brandData.name },
      });

      if (!existingBrand) {
        await Brand.create({
          name: brandData.name,
          logo: brandData.logo,
          endpoint: brandData.endpoint,
          clientId: defaultClient.id,
          isDefault: false, // Set default brands as non-default initially
        });
        console.log(`✅ Default brand '${brandData.name}' created`);
      } else {
        console.log(`ℹ️  Default brand '${brandData.name}' already exists`);
      }
    }

    console.log("🎉 Database seeding completed successfully!");
  } catch (error) {
    console.error("❌ Error seeding database:", error);
    throw error;
  }
}

/**
 * Reset database to default state (for development)
 */
export async function resetDatabase(): Promise<void> {
  try {
    console.log("🔄 Resetting database...");

    // Clear all data
    await Brand.destroy({ where: {} });
    await Client.destroy({ where: {} });
    await AdminPin.destroy({ where: {} });

    console.log("✅ Database reset completed");

    // Re-seed with default data
    await seedDefaultData();
  } catch (error) {
    console.error("❌ Error resetting database:", error);
    throw error;
  }
}
