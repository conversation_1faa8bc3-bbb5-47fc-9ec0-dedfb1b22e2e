import sequelize from "../config/database";
import { up } from "../migrations/createFilesTable";

async function runMigration() {
  try {
    console.log("Running files table migration...");
    await up(sequelize.getQueryInterface());
    console.log("Migration completed successfully!");
    process.exit(0);
  } catch (error) {
    console.error("Migration failed:", error);
    process.exit(1);
  }
}

runMigration();
