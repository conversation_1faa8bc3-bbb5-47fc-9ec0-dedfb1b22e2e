import sequelize from "../config/database";
import dotenv from "dotenv";

dotenv.config();

async function testConnection() {
  console.log("🔍 Testing database connection...");
  console.log("📊 Configuration:");
  console.log(`   Host: ${process.env["DB_HOST"]}`);
  console.log(`   Port: ${process.env["DB_PORT"]}`);
  console.log(`   Database: ${process.env["DB_NAME"]}`);
  console.log(`   User: ${process.env["DB_USER"]}`);
  console.log(
    `   SSL: ${
      process.env["DB_HOST"]?.includes("amazonaws.com") ? "Enabled" : "Disabled"
    }`
  );
  console.log("");

  try {
    await sequelize.authenticate();
    console.log("✅ Database connection successful!");

    // Test a simple query
    const [results] = await sequelize.query("SELECT version() as version");
    console.log("📋 Database version:", results[0]);

    // Check if tables exist
    const [tables] = await sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);

    console.log(
      "📋 Existing tables:",
      tables.map((t: any) => t.table_name)
    );
  } catch (error: any) {
    console.error("❌ Database connection failed:");
    console.error(error);

    // Provide helpful suggestions
    if (error.message?.includes("pg_hba.conf")) {
      console.log(
        "\n💡 Suggestion: Check AWS RDS security groups and network configuration"
      );
    } else if (error.message?.includes("timeout")) {
      console.log(
        "\n💡 Suggestion: Check network connectivity and firewall settings"
      );
    } else if (error.message?.includes("authentication")) {
      console.log("\n💡 Suggestion: Verify username and password in .env file");
    } else if (error.message?.includes("does not exist")) {
      console.log("\n💡 Suggestion: Check database name in .env file");
    }
  } finally {
    await sequelize.close();
    process.exit(0);
  }
}

testConnection();
