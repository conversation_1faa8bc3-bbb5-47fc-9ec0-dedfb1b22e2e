# Production Deployment Guide

## Environment Variables for Production

### Required Environment Variables

```env
# Server Configuration
PORT=3000
NODE_ENV=production

# Database Configuration (AWS RDS or your production database)
DB_HOST=your-production-db-host
DB_PORT=5432
DB_NAME=scoot_insights
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# CORS Configuration - CRITICAL FOR PRODUCTION
CORS_ORIGIN=https://marketing.bloodandtreasure.com,https://marketing-api.bloodandtreasure.com

# Logging
LOG_LEVEL=info
```

## CORS Configuration

### For Production Deployment

The `CORS_ORIGIN` environment variable must be set to include your production domains:

```env
CORS_ORIGIN=https://marketing.bloodandtreasure.com,https://marketing-api.bloodandtreasure.com
```

### For Development

```env
CORS_ORIGIN=http://localhost:5173
```

## Common Deployment Platforms

### AWS EC2 / ECS

Set environment variables in your task definition or EC2 instance.

### Vercel

Add environment variables in the Vercel dashboard under Project Settings > Environment Variables.

### Railway

Add environment variables in the Railway dashboard under Variables.

### Heroku

```bash
heroku config:set CORS_ORIGIN=https://marketing.bloodandtreasure.com,https://marketing-api.bloodandtreasure.com
```

## Troubleshooting CORS Issues

1. **Check environment variables are loaded:**

   - Look for the "🔗 CORS Origins:" log message when the server starts
   - Verify the origins include your production domain

2. **Verify the request origin:**

   - Check browser network tab for the exact origin being sent
   - Ensure it matches one of the allowed origins

3. **Test CORS configuration:**
   ```bash
   curl -H "Origin: https://marketing.bloodandtreasure.com" \
        -H "Access-Control-Request-Method: GET" \
        -H "Access-Control-Request-Headers: Content-Type" \
        -X OPTIONS \
        https://marketing-api.bloodandtreasure.com/brands/default
   ```

## Security Considerations

- Only include domains that actually need access
- Use HTTPS in production
- Consider using a more restrictive CORS policy for sensitive endpoints
- Regularly review and update allowed origins
