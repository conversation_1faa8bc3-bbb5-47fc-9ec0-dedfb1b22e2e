# Scoot Insights Backend API Documentation

## Overview

The Scoot Insights backend is a Node.js/Express.js API built with TypeScript that provides a comprehensive system for managing clients, brands, and file uploads. The API uses PostgreSQL as the database and AWS S3 for file storage.

## Table of Contents

1. [Server Configuration](#server-configuration)
2. [Database Models](#database-models)
3. [API Endpoints](#api-endpoints)
4. [Services](#services)
5. [Middleware](#middleware)
6. [File Upload System](#file-upload-system)
7. [Error Handling](#error-handling)
8. [Security Features](#security-features)

## Server Configuration

### Main Server (`src/index.ts`)

The main server file configures the Express application with the following features:

- **Port**: Configurable via `PORT` environment variable (default: 3000)
- **CORS**: Configured for cross-origin requests with configurable origin
- **Security**: Helmet.js for security headers
- **Logging**: Morgan for HTTP request logging
- **Body Parsing**: JSON and URL-encoded data with 10MB limit
- **Database**: Automatic connection testing and table synchronization on startup

### Environment Variables

```env
PORT=3000
NODE_ENV=development
CORS_ORIGIN=http://localhost:5173
DB_HOST=localhost
DB_PORT=5432
DB_NAME=scoot_insights
DB_USER=postgres
DB_PASSWORD=your_password
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your_bucket_name
```

## Database Models

### Client Model (`src/models/Client.ts`)

**Table**: `client`

**Columns**:

- `id` (UUID, Primary Key): Unique identifier for the client
- `name` (VARCHAR(255), Unique): Name of the client
- `logo` (TEXT): URL or base64 string of the client logo
- `description` (TEXT, Optional): Optional description of the client
- `enabled` (BOOLEAN): Whether the client is active/enabled (default: true)
- `created_at` (TIMESTAMP): Record creation timestamp
- `updated_at` (TIMESTAMP): Record update timestamp

**Indexes**:

- Unique index on `name`
- Index on `enabled`

### Brand Model (`src/models/Brand.ts`)

**Table**: `brand`

**Columns**:

- `id` (UUID, Primary Key): Unique identifier for the brand
- `name` (VARCHAR(255)): Name of the brand
- `logo` (TEXT): URL or base64 string of the brand logo
- `endpoint` (VARCHAR(255), Unique): URL-friendly endpoint for the brand
- `client_id` (UUID, Foreign Key): Reference to the client
- `created_at` (TIMESTAMP): Record creation timestamp
- `updated_at` (TIMESTAMP): Record update timestamp

**Relationships**:

- Belongs to Client (Many-to-One)

**Indexes**:

- Unique index on `endpoint`
- Index on `client_id`
- Index on `name`

### File Model (`src/models/File.ts`)

**Table**: `files`

**Columns**:

- `id` (UUID, Primary Key): Unique identifier for the file
- `original_name` (VARCHAR(255)): Original filename uploaded by user
- `file_name` (VARCHAR(255)): Generated unique filename
- `file_key` (VARCHAR(500)): S3 object key for the file
- `file_url` (VARCHAR(1000)): Full S3 URL for the file
- `file_size` (BIGINT): File size in bytes
- `mime_type` (VARCHAR(100)): MIME type of the file
- `folder` (VARCHAR(100)): Folder path in S3
- `uploaded_by` (VARCHAR(100), Optional): User who uploaded the file
- `created_at` (TIMESTAMP): Record creation timestamp
- `updated_at` (TIMESTAMP): Record update timestamp

**Indexes**:

- Index on `file_key`
- Index on `folder`
- Index on `mime_type`

### AdminPin Model (`src/models/AdminPin.ts`)

**Table**: `admin_pins`

**Columns**:

- `id` (UUID, Primary Key): Unique identifier for the admin PIN
- `pin` (VARCHAR(6), Unique): 6-character alphanumeric admin PIN (case insensitive)
- `created_at` (TIMESTAMP): Record creation timestamp
- `updated_at` (TIMESTAMP): Record update timestamp

**Indexes**:

- Unique index on `pin`

## API Endpoints

### Health Check

#### GET `/api/health`

Returns the API health status and environment information.

**Response**:

```json
{
  "success": true,
  "message": "API is running",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "environment": "development"
}
```

### Admin PIN Management

#### POST `/api/admin/validate-pin`

Validates an admin PIN.

**Request Body**:

```json
{
  "pin": "ADMIN1"
}
```

**Validation Rules**:

- `pin`: Required, exactly 6 characters, alphanumeric only

**Response**:

```json
{
  "success": true,
  "message": "PIN validated successfully"
}
```

#### GET `/api/admin/pin-exists`

Checks if an admin PIN exists in the database.

**Response**:

```json
{
  "success": true,
  "data": {
    "exists": true
  },
  "message": "PIN existence checked successfully"
}
```

#### POST `/api/admin/set-pin`

Sets the admin PIN (for first-time setup).

**Request Body**:

```json
{
  "pin": "ADMIN1"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "pin": "ADMIN1"
  },
  "message": "Admin PIN created successfully"
}
```

#### PUT `/api/admin/update-pin`

Updates the admin PIN.

**Request Body**:

```json
{
  "pin": "NEWPIN",
  "currentPin": "ADMIN1"
}
```

**Response**:

```json
{
  "success": true,
  "message": "Admin PIN updated successfully"
}
```

### Client Management

#### GET `/api/clients`

Retrieves all enabled clients.

**Response**:

```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "Client Name",
      "logo": "logo_url",
      "description": "Client description",
      "enabled": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "message": "Clients retrieved successfully"
}
```

#### GET `/api/clients/:id`

Retrieves a specific client by ID.

**Parameters**:

- `id` (UUID): Client ID

**Response**:

```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Client Name",
    "logo": "logo_url",
    "description": "Client description",
    "enabled": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "Client retrieved successfully"
}
```

#### POST `/api/clients`

Creates a new client.

**Request Body**:

```json
{
  "name": "Client Name",
  "logo": "logo_url",
  "description": "Client description",
  "enabled": true
}
```

**Validation Rules**:

- `name`: Required, 1-255 characters, alphanumeric with spaces, hyphens, underscores
- `logo`: Required, non-empty string
- `description`: Optional, max 1000 characters
- `enabled`: Optional, boolean

**Response**:

```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Client Name",
    "logo": "logo_url",
    "description": "Client description",
    "enabled": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "Client created successfully"
}
```

#### PUT `/api/clients/:id`

Updates an existing client.

**Parameters**:

- `id` (UUID): Client ID

**Request Body**:

```json
{
  "name": "Updated Client Name",
  "logo": "updated_logo_url",
  "description": "Updated description",
  "enabled": false
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Updated Client Name",
    "logo": "updated_logo_url",
    "description": "Updated description",
    "enabled": false,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "Client updated successfully"
}
```

#### DELETE `/api/clients/:id`

Soft deletes a client by setting `enabled` to false.

**Parameters**:

- `id` (UUID): Client ID

**Response**:

```json
{
  "success": true,
  "message": "Client deleted successfully"
}
```

### Brand Management

#### GET `/api/brands`

Retrieves all brands with client information.

**Response**:

```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "Brand Name",
      "logo": "logo_url",
      "endpoint": "brand-endpoint",
      "clientId": "client_uuid",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z",
      "client": {
        "id": "client_uuid",
        "name": "Client Name",
        "logo": "client_logo_url"
      }
    }
  ],
  "message": "Brands retrieved successfully"
}
```

#### GET `/api/brands/:id`

Retrieves a specific brand by ID with client information.

**Parameters**:

- `id` (UUID): Brand ID

**Response**: Same as GET `/api/brands` but for a single brand.

#### GET `/api/brands/client/:clientId`

Retrieves all brands for a specific client.

**Parameters**:

- `clientId` (UUID): Client ID

**Response**: Same as GET `/api/brands` but filtered by client.

#### POST `/api/brands`

Creates a new brand.

**Request Body**:

```json
{
  "name": "Brand Name",
  "logo": "logo_url",
  "endpoint": "brand-endpoint",
  "clientId": "client_uuid"
}
```

**Validation Rules**:

- `name`: Required, 1-255 characters, alphanumeric with spaces, hyphens, underscores
- `logo`: Required, non-empty string
- `endpoint`: Required, 1-255 characters, lowercase letters, numbers, hyphens only
- `clientId`: Required, valid UUID

**Response**:

```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Brand Name",
    "logo": "logo_url",
    "endpoint": "brand-endpoint",
    "clientId": "client_uuid",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "client": {
      "id": "client_uuid",
      "name": "Client Name",
      "logo": "client_logo_url"
    }
  },
  "message": "Brand created successfully"
}
```

#### PUT `/api/brands/:id`

Updates an existing brand.

**Parameters**:

- `id` (UUID): Brand ID

**Request Body**:

```json
{
  "name": "Updated Brand Name",
  "logo": "updated_logo_url",
  "endpoint": "updated-endpoint",
  "clientId": "new_client_uuid"
}
```

**Response**: Same as POST `/api/brands` but for updated brand.

#### DELETE `/api/brands/:id`

Hard deletes a brand.

**Parameters**:

- `id` (UUID): Brand ID

**Response**:

```json
{
  "success": true,
  "message": "Brand deleted successfully"
}
```

### File Upload Management

#### POST `/api/upload/logo`

Uploads a logo file to S3.

**Request**:

- Content-Type: `multipart/form-data`
- Body:
  - `file`: Image file (JPEG, JPG, PNG, GIF, WebP, max 10MB)
  - `type`: String ('client' or 'brand')
  - `uploadedBy`: String (optional user identifier)

**Response**:

```json
{
  "success": true,
  "data": {
    "id": "file_uuid",
    "url": "https://s3.amazonaws.com/bucket/folder/filename.jpg",
    "key": "logos/client/filename.jpg",
    "originalName": "original_filename.jpg",
    "fileName": "1234567890-abc123.jpg",
    "size": 1024000,
    "mimetype": "image/jpeg",
    "folder": "logos/client",
    "uploadedBy": "user123",
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "File uploaded successfully"
}
```

#### DELETE `/api/upload/logo`

Deletes a logo file from S3.

**Request Body**:

```json
{
  "key": "logos/client/filename.jpg"
}
```

**Response**:

```json
{
  "success": true,
  "message": "File deleted successfully"
}
```

#### POST `/api/upload/image`

Uploads a general image file.

**Request**:

- Content-Type: `multipart/form-data`
- Body:
  - `file`: Image file (JPEG, JPG, PNG, GIF, WebP, max 10MB)
  - `category`: String ('personas', 'general', etc.)
  - `uploadedBy`: String (optional user identifier)

**Response**: Same as POST `/api/upload/logo` but for general images.

#### PUT `/api/upload/replace/:fileId`

Replaces an existing file with a new one.

**Parameters**:

- `fileId` (UUID): File ID

**Request**:

- Content-Type: `multipart/form-data`
- Body:
  - `file`: New image file
  - `folder`: String (optional, default: 'general')
  - `uploadedBy`: String (optional user identifier)

**Response**: Same as POST `/api/upload/logo` but for replaced file.

#### GET `/api/upload/files/:folder`

Retrieves all files in a specific folder.

**Parameters**:

- `folder`: String (folder path)

**Response**:

```json
{
  "success": true,
  "data": [
    {
      "id": "file_uuid",
      "originalName": "original_filename.jpg",
      "fileName": "1234567890-abc123.jpg",
      "fileKey": "logos/client/filename.jpg",
      "fileUrl": "https://s3.amazonaws.com/bucket/folder/filename.jpg",
      "fileSize": 1024000,
      "mimeType": "image/jpeg",
      "folder": "logos/client",
      "uploadedBy": "user123",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "message": "Files retrieved successfully"
}
```

## Services

### File Service (`src/services/fileService.ts`)

The File Service manages file operations including:

- **uploadFile()**: Uploads files to S3 and stores metadata in database
- **deleteFile()**: Deletes files from S3 and database by file ID
- **deleteFileByKey()**: Deletes files from S3 and database by S3 key
- **getFileById()**: Retrieves file metadata by ID
- **getFileByKey()**: Retrieves file metadata by S3 key
- **getFilesByFolder()**: Retrieves all files in a specific folder
- **updateFile()**: Updates file metadata
- **replaceFile()**: Replaces existing file with new file

### S3 Service (`src/services/s3Service.ts`)

The S3 Service handles AWS S3 operations including:

- **uploadFile()**: Uploads files to S3 with validation and optimization
- **deleteFile()**: Deletes files from S3
- **generateFileKey()**: Generates unique S3 object keys
- **getFileUrl()**: Constructs S3 URLs from keys
- **extractKeyFromUrl()**: Extracts S3 keys from URLs

**Features**:

- File validation (type, size, security scanning)
- Image optimization using Sharp
- Virus scanning for suspicious file patterns
- Automatic image resizing for large images
- Quality optimization for different image formats

## Middleware

### Validation Middleware (`src/middleware/validation.ts`)

Provides comprehensive input validation for all endpoints:

- **Client Validation**: Name format, logo requirements, description limits
- **Brand Validation**: Name format, endpoint format, client relationship
- **File Upload Validation**: File type, size limits, required fields
- **UUID Validation**: Ensures proper UUID format for IDs

**Validation Rules**:

- Client/Brand names: 1-255 characters, alphanumeric with spaces, hyphens, underscores
- Endpoints: 1-255 characters, lowercase letters, numbers, hyphens only
- File types: JPEG, JPG, PNG, GIF, WebP only
- File size: Maximum 10MB
- UUIDs: Valid UUID v4 format

## File Upload System

### Supported File Types

- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)

### File Size Limits

- Maximum file size: 10MB

### Security Features

- File type validation
- File size validation
- Basic virus scanning (executable and script detection)
- Image optimization and resizing
- Unique file naming to prevent conflicts

### S3 Integration

- Automatic file upload to AWS S3
- Public read access for uploaded files
- 1-year cache control headers
- Organized folder structure (logos/client, logos/brand, images/category)

## Error Handling

### Global Error Handler

The API includes a comprehensive error handling system:

- **400 Bad Request**: Validation errors, invalid input
- **404 Not Found**: Resource not found
- **409 Conflict**: Duplicate resource (e.g., existing name/endpoint)
- **500 Internal Server Error**: Server-side errors

### Error Response Format

```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "field_name",
      "message": "Validation error message",
      "value": "invalid_value"
    }
  ]
}
```

### Development vs Production

- Development: Includes error stack traces
- Production: Sanitized error messages without sensitive information

## Security Features

### Input Validation

- Comprehensive validation for all inputs
- SQL injection prevention through parameterized queries
- XSS prevention through input sanitization

### File Upload Security

- File type validation
- File size limits
- Basic virus scanning
- Secure file naming

### CORS Configuration

- Configurable origin
- Credentials support
- Specific HTTP methods allowed
- Custom headers support

### Database Security

- SSL support for AWS RDS
- Connection pooling
- Prepared statements through Sequelize

### API Security

- Helmet.js for security headers
- Request logging
- Rate limiting ready (can be added)
- Environment-based configuration

## Database Configuration

### PostgreSQL Setup

- Connection pooling (max 5 connections)
- SSL support for AWS RDS
- Automatic table creation and synchronization
- Timestamp tracking on all records
- Snake_case column naming convention

### Environment Configuration

- Configurable database host, port, name, user, password
- Development vs production logging
- SSL configuration for cloud deployments

## Deployment Considerations

### Environment Variables

All sensitive configuration is externalized through environment variables:

- Database credentials
- AWS credentials
- S3 bucket configuration
- CORS origins
- Server ports

### Health Monitoring

- Health check endpoint for monitoring
- Database connection testing
- Graceful shutdown handling

### Logging

- HTTP request logging with Morgan
- Error logging with stack traces in development
- Database query logging in development

This backend API provides a robust foundation for the Scoot Insights application with comprehensive client and brand management, secure file uploads, and proper error handling.
