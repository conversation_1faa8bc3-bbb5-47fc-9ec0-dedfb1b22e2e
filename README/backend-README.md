# Scoot Insights Backend API

A Node.js/Express.js backend API built with TypeScript, Sequelize ORM, and PostgreSQL for the Scoot Insights application.

## Features

- **TypeScript**: Full TypeScript support with strict type checking
- **Express.js**: Fast, unopinionated web framework
- **Sequelize ORM**: Database ORM with PostgreSQL support
- **AWS S3 Integration**: File upload and management
- **CORS Support**: Cross-origin resource sharing enabled
- **Validation**: Request validation using express-validator
- **Error Handling**: Comprehensive error handling and logging
- **Environment Configuration**: Environment-based configuration

## Prerequisites

- Node.js (v16 or higher)
- PostgreSQL (v12 or higher) - Local or AWS RDS
- AWS S3 bucket (for file uploads)
- AWS RDS PostgreSQL instance (for production/remote development)

## Installation

1. **Clone the repository and navigate to the server directory:**

   ```bash
   cd server
   ```

2. **Install dependencies:**

   ```bash
   npm install
   ```

3. **Set up environment variables:**

   ```bash
   cp env.example .env
   ```

   Edit `.env` file with your configuration:

   **For Local Development:**

   ```env
   # Server Configuration
   PORT=3000
   NODE_ENV=development

   # Database Configuration (Local PostgreSQL)
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=scoot_insights
   DB_USER=postgres
   DB_PASSWORD=your_password

   # AWS S3 Configuration
   AWS_ACCESS_KEY_ID=your_aws_access_key_id
   AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
   AWS_REGION=us-east-1
   AWS_S3_BUCKET=your-s3-bucket-name

   # CORS Configuration
   CORS_ORIGIN=http://localhost:5173

   # Logging
   LOG_LEVEL=info
   ```

   **For AWS RDS (Production/Remote):**

   ```env
   # Server Configuration
   PORT=3000
   NODE_ENV=development

   # Database Configuration (AWS RDS)
   DB_HOST=your-rds-endpoint.amazonaws.com
   DB_PORT=5432
   DB_NAME=your_database_name
   DB_USER=your_username
   DB_PASSWORD=your_password

   # AWS S3 Configuration
   AWS_ACCESS_KEY_ID=your_aws_access_key_id
   AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
   AWS_REGION=us-east-1
   AWS_S3_BUCKET=your-s3-bucket-name

   # CORS Configuration
   CORS_ORIGIN=http://localhost:5173

   # Logging
   LOG_LEVEL=info
   ```

4. **Set up PostgreSQL database:**

   ```sql
   CREATE DATABASE scoot_insights;
   ```

5. **Run the application:**

   ```bash
   # Development mode
   npm run dev

   # Production mode
   npm run build
   npm start
   ```

## Database Schema

### Client Table

- `id` (UUID, Primary Key)
- `name` (VARCHAR(255), Unique, Not Null)
- `logo` (TEXT, Not Null)
- `description` (TEXT, Nullable)
- `enabled` (BOOLEAN, Default: true)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### Brand Table

- `id` (UUID, Primary Key)
- `name` (VARCHAR(255), Not Null)
- `logo` (TEXT, Not Null)
- `endpoint` (VARCHAR(255), Unique, Not Null)
- `client_id` (UUID, Foreign Key to Client)
- `is_default` (BOOLEAN, Default: false) - Whether this brand is the default brand
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### File Table

- `id` (UUID, Primary Key)
- `original_name` (VARCHAR(255), Not Null) - Original filename uploaded by user
- `file_name` (VARCHAR(255), Not Null) - Generated unique filename
- `file_key` (VARCHAR(500), Unique, Not Null) - S3 object key
- `file_url` (VARCHAR(1000), Not Null) - Full S3 URL
- `file_size` (BIGINT, Not Null) - File size in bytes
- `mime_type` (VARCHAR(100), Not Null) - MIME type of the file
- `folder` (VARCHAR(100), Not Null) - Folder path in S3
- `uploaded_by` (VARCHAR(100), Nullable) - User who uploaded the file
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

## File Upload Security

**⚠️ IMPORTANT SECURITY CONSIDERATIONS:**

1. **Public Access**: Files uploaded to S3 are set to public read access (`ACL: "public-read"`). This means anyone with the URL can access the files.

2. **File Type Validation**: Only image files are allowed (JPEG, JPG, PNG, GIF, WebP) with a maximum size of 10MB.

3. **Virus Scanning**: Basic virus scanning is implemented by checking file headers for suspicious patterns (executable files, scripts).

4. **Image Optimization**: Images are automatically optimized using Sharp library to reduce file size and improve performance.

5. **Unique Filenames**: All uploaded files get unique filenames to prevent conflicts and security issues.

6. **Database Tracking**: All file metadata is stored in the database for audit trails and easy access.

**Recommendations for Production:**

- Consider implementing more robust virus scanning
- Add authentication/authorization for file access
- Implement file access logging
- Consider using signed URLs for sensitive files
- Regular security audits of uploaded files
- `name` (VARCHAR(255), Not Null)
- `logo` (TEXT, Not Null)
- `endpoint` (VARCHAR(255), Unique, Not Null)
- `client_id` (UUID, Foreign Key to Client)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

## API Endpoints

### Health Check

- `GET /api/health` - Check API status

### Clients

- `GET /api/clients` - Get all enabled clients
- `GET /api/clients/:id` - Get client by ID
- `POST /api/clients` - Create new client
- `PUT /api/clients/:id` - Update client
- `DELETE /api/clients/:id` - Soft delete client (sets enabled to false)

### Brands

- `GET /api/brands` - Get all brands with client information
- `GET /api/brands/:id` - Get brand by ID with client information
- `GET /api/brands/client/:clientId` - Get brands by client ID
- `GET /api/brands/default` - Get the default brand
- `POST /api/brands` - Create new brand
- `PUT /api/brands/:id` - Update brand
- `DELETE /api/brands/:id` - Delete brand

### File Upload

- `POST /api/upload/logo` - Upload logo file to S3
- `DELETE /api/upload/logo` - Delete logo file from S3

## Request/Response Examples

### Create Client

```bash
POST /api/clients
Content-Type: application/json

{
  "name": "Acme Corporation",
  "logo": "https://example.com/logo.png",
  "description": "A leading technology company",
  "enabled": true
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "uuid-here",
    "name": "Acme Corporation",
    "logo": "https://example.com/logo.png",
    "description": "A leading technology company",
    "enabled": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "Client created successfully"
}
```

### Get Default Brand

```bash
GET /api/brands/default
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "uuid-here",
    "name": "Acme Pro",
    "logo": "https://example.com/brand-logo.png",
    "endpoint": "acme-pro",
    "clientId": "client-uuid-here",
    "isDefault": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "client": {
      "id": "client-uuid-here",
      "name": "Acme Corporation",
      "logo": "https://example.com/logo.png"
    }
  },
  "message": "Default brand retrieved successfully"
}
```

### Create Brand

```bash
POST /api/brands
Content-Type: application/json

{
  "name": "Acme Pro",
  "logo": "https://example.com/brand-logo.png",
  "endpoint": "acme-pro",
  "clientId": "client-uuid-here",
  "isDefault": false
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "uuid-here",
    "name": "Acme Pro",
    "logo": "https://example.com/brand-logo.png",
    "endpoint": "acme-pro",
    "clientId": "client-uuid-here",
    "isDefault": false,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "client": {
      "id": "client-uuid-here",
      "name": "Acme Corporation",
      "logo": "https://example.com/logo.png"
    }
  },
  "message": "Brand created successfully"
}
```

### Upload Logo

```bash
POST /api/upload/logo
Content-Type: multipart/form-data

file: [logo file]
type: "client" | "brand"
uploadedBy: "user123" (optional)
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "file-uuid-here",
    "url": "https://your-bucket.s3.region.amazonaws.com/logos/client/timestamp-random.jpg",
    "key": "logos/client/timestamp-random.jpg",
    "originalName": "logo.jpg",
    "fileName": "1704067200000-abc123.jpg",
    "size": 12345,
    "mimetype": "image/jpeg",
    "folder": "logos/client",
    "uploadedBy": "user123",
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "File uploaded successfully"
}
```

### Upload General Image

```bash
POST /api/upload/image
Content-Type: multipart/form-data

file: [image file]
category: "personas" | "general"
uploadedBy: "user123" (optional)
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "file-uuid-here",
    "url": "https://your-bucket.s3.region.amazonaws.com/images/personas/timestamp-random.jpg",
    "key": "images/personas/timestamp-random.jpg",
    "originalName": "persona.jpg",
    "fileName": "1704067200000-abc123.jpg",
    "size": 12345,
    "mimetype": "image/jpeg",
    "folder": "images/personas",
    "uploadedBy": "user123",
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "Image uploaded successfully"
}
```

### Replace File

```bash
PUT /api/upload/replace/:fileId
Content-Type: multipart/form-data

file: [new file]
folder: "logos/client" (optional)
uploadedBy: "user123" (optional)
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "file-uuid-here",
    "url": "https://your-bucket.s3.region.amazonaws.com/logos/client/new-timestamp-random.jpg",
    "key": "logos/client/new-timestamp-random.jpg",
    "originalName": "new-logo.jpg",
    "fileName": "1704067200000-def456.jpg",
    "size": 12345,
    "mimetype": "image/jpeg",
    "folder": "logos/client",
    "uploadedBy": "user123",
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "File replaced successfully"
}
```

### Delete File

```bash
DELETE /api/upload/logo
Content-Type: application/json

{
  "key": "logos/client/timestamp-random.jpg"
}
```

**Response:**

```json
{
  "success": true,
  "message": "File deleted successfully"
}
```

### Get Files by Folder

```bash
GET /api/upload/files/:folder
```

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": "file-uuid-here",
      "originalName": "logo.jpg",
      "fileName": "1704067200000-abc123.jpg",
      "fileKey": "logos/client/timestamp-random.jpg",
      "fileUrl": "https://your-bucket.s3.region.amazonaws.com/logos/client/timestamp-random.jpg",
      "fileSize": 12345,
      "mimeType": "image/jpeg",
      "folder": "logos/client",
      "uploadedBy": "user123",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "message": "Files retrieved successfully"
}
```

## Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "name",
      "message": "Client name is required",
      "value": ""
    }
  ]
}
```

## Development

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run lint` - Run ESLint
- `npm run migrate:files` - Run files table migration

## Troubleshooting

### AWS RDS Connection Issues

If you're having trouble connecting to AWS RDS, check the following:

1. **Security Groups**: Ensure the RDS security group allows connections from your IP address
2. **VPC Configuration**: Make sure the RDS instance is in a public subnet or you have proper VPN access
3. **SSL Requirements**: The application automatically configures SSL for AWS RDS connections
4. **Database Status**: Verify the RDS instance is running and accessible
5. **Credentials**: Double-check username, password, and database name

### Common Error Messages

- `no pg_hba.conf entry for host` - Security group or network configuration issue
- `connection timeout` - Network connectivity or firewall issue
- `authentication failed` - Incorrect username/password
- `database does not exist` - Wrong database name

### Testing Database Connection

You can test the database connection separately:

```bash
# Test connection
npm run dev

# Look for this message in the console:
# ✅ Database connection has been established successfully.
```

- `npm run lint:fix` - Fix ESLint errors
- `npm test` - Run tests

### Project Structure

```
server/
├── src/
│   ├── config/          # Database and app configuration
│   ├── models/          # Sequelize models
│   ├── routes/          # API routes
│   ├── services/        # Business logic and external services
│   ├── middleware/      # Custom middleware
│   └── index.ts         # Server entry point
├── package.json
├── tsconfig.json
├── env.example
└── README.md
```

## Production Deployment

1. **Set environment variables for production**
2. **Build the application:**
   ```bash
   npm run build
   ```
3. **Start the production server:**
   ```bash
   npm start
   ```

## Contributing

1. Follow TypeScript best practices
2. Add proper error handling
3. Include validation for all inputs
4. Write tests for new features
5. Update documentation

## License

MIT License
