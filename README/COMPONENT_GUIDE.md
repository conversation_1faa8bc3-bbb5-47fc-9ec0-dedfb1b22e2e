# Component Style Guide

This guide shows exactly where to find and change styles for each component in the Scoot Insights application using the new design system.

## 🧭 Quick Reference

### Design System Architecture

- **Design Tokens**: `src/styles/index.css` (CSS custom properties)
- **Component Classes**: `src/styles/components.css` (organized by component type)
- **Global Styles**: `src/styles/index.css` (base, utilities, animations)
- **Tailwind Config**: `tailwind.config.js` (uses design tokens)

### Key Principles

1. **Use Design Tokens**: All colors, spacing, typography use CSS custom properties
2. **Use Component Classes**: Prefer component classes over long Tailwind strings
3. **Combine with Tailwind**: Use component classes + Tailwind utilities for flexibility
4. **Avoid Inline Styles**: Only use for dynamic values (like GSAP animations)

## 📄 Pages

### PersonaList (`src/pages/PersonaList.tsx`)

**Where to change styles:**

- **Carousel container**: `src/styles/components.css` (`.carousel-*` classes)
- **3D carousel effects**: `src/pages/PersonaList.tsx` (inline styles for GSAP)
- **Navigation arrows**: `src/styles/components.css` (`.carousel-arrow`)
- **Pagination dots**: `src/styles/components.css` (`.carousel-dot-*`)
- **Glass effect**: `src/styles/index.css` (`.glass` class)

**Key classes to use:**

```jsx
// Carousel navigation
<button className="carousel-arrow left-8"> // or right-8

// Pagination dots
<button className="carousel-dot carousel-dot-active"> // or carousel-dot-inactive

// Glass container
<div className="glass">
```

### PersonaDetail (`src/pages/PersonaDetail.tsx`)

**Where to change styles:**

- **Card layouts**: `src/styles/components.css` (`.card-*` classes)
- **Profile sections**: `src/styles/components.css` (`.section-container`)

**Key classes to use:**

```jsx
// Profile card
<motion.div className="card">

// Content sections
<motion.section className="section-container">
```

### Progress (`src/pages/Progress.tsx`)

**Where to change styles:**

- **Progress bars**: `src/styles/components.css` (`.progress-*` classes)
- **Text shadows**: `src/styles/index.css` (`.text-shadow-neumorphic`)

**Key classes to use:**

```jsx
// Progress bar
<div className="progress-bar">
  <div className="progress-fill" style={{ width: `${progressPercentage}%` }} />
</div>

// Text with neumorphic shadow
<p className="text-shadow-neumorphic">
```

### Timeline (`src/pages/Timeline.tsx`)

**Where to change styles:**

- **Timeline layout**: `src/styles/components.css` (`.timeline-*` classes)
- **Modal styles**: `src/styles/components.css` (`.modal-*` classes)

**Key classes to use:**

```jsx
// Timeline container
<div className="timeline-container">
  <div className="timeline-line" />

  // Timeline items
  <div className="timeline-item group">
    <div className="timeline-dot timeline-dot-completed" />
    <div className="timeline-content" />
  </div>
</div>

// Modal
<div className="modal-backdrop">
  <div className="modal-container">
    <div className="modal-content" />
  </div>
</div>
```

### Videos (`src/pages/Videos.tsx`)

**Where to change styles:**

- **Video cards**: `src/styles/components.css` (`.card-*` classes)
- **Grid layout**: `src/styles/components.css` (`.grid-responsive`)

**Key classes to use:**

```jsx
// Video grid
<div className="grid-responsive">

// Video cards
<div className="card">
  <div className="card-body" />
</div>
```

### Podcasts (`src/pages/Podcasts.tsx`)

**Where to change styles:**

- **Podcast cards**: `src/styles/components.css` (`.card-*` classes)
- **Modal styles**: `src/styles/components.css` (`.modal-*` classes)

## 🧩 Components

### Header (`src/components/Header.tsx`)

**Where to change styles:**

- **Header container**: `src/styles/index.css` (`.neumorphic-header`)
- **Navigation buttons**: `src/styles/components.css` (`.btn-*` classes)
- **Modal styles**: `src/styles/components.css` (`.modal-*` classes)

**Key classes to use:**

```jsx
// Header container
<header className="neumorphic-header">

// Navigation buttons
<button className="btn btn-ghost">
<button className="btn btn-primary">
```

### Sidebar (`src/components/Sidebar.tsx`)

**Where to change styles:**

- **Navigation items**: `src/styles/components.css` (`.nav-*` classes)
- **Collapse button**: `src/styles/components.css` (`.btn-icon`)

**Key classes to use:**

```jsx
// Navigation items
<button className="nav-item nav-item-active"> // or nav-item-inactive

// Collapse button
<button className="btn-icon">
```

### PersonaCard (`src/components/PersonaCard.tsx`)

**Where to change styles:**

- **Card container**: `src/styles/components.css` (`.persona-card-*` classes)
- **Category badges**: `src/styles/components.css` (`.badge-*` classes)

**Key classes to use:**

```jsx
// Interactive card container (entire card is clickable)
<div
  className="persona-card"
  role="button"
  tabIndex={0}
  onClick={handlePersonaClick}
  onKeyDown={handleKeyDown}
  aria-label={`View details for ${persona.name}`}
>
  <div className="persona-card-header">
    <img className="persona-card-avatar" />
  </div>
  <div className="persona-card-body" />
  {/* No footer needed - entire card is clickable */}
</div>

// Category badge
<span className="badge badge-primary">
```

### PersonaInfo (`src/components/PersonaInfo.tsx`)

**Where to change styles:**

- **Info containers**: `src/styles/index.css` (`.neumorphic-*` classes)

**Key classes to use:**

```jsx
// Info container
<div className="neumorphic-elevated"> // or neumorphic-inset
```

### FilterBar (`src/components/FilterBar.tsx`)

**Where to change styles:**

- **Form inputs**: `src/styles/components.css` (`.form-*` classes)
- **Labels**: `src/styles/components.css` (`.form-label`)

**Key classes to use:**

```jsx
// Form inputs
<input className="form-input" />
<select className="form-select" />

// Labels
<label className="form-label">
```

### NeumorphicContainer (`src/components/NeumorphicContainer.tsx`)

**Where to change styles:**

- **Container**: `src/styles/index.css` (`.neumorphic-container`)

**Key classes to use:**

```jsx
// Container
<div className="neumorphic-container">
```

### SectionBox (`src/components/SectionBox.tsx`)

**Where to change styles:**

- **Section container**: `src/styles/components.css` (`.section-container`)

**Key classes to use:**

```jsx
// Section container
<div className="section-container">
```

### Timeline (`src/components/Timeline.tsx`)

**Where to change styles:**

- **Timeline layout**: `src/styles/components.css` (`.timeline-*` classes)

**Key classes to use:**

```jsx
// Timeline container
<div='timeline-container'>
  <div className='timeline-line' />
  // Timeline items
  <div className='timeline-item group'>
    <div className='timeline-dot timeline-dot-completed' />
    <div className='timeline-content' />
  </div>
</div>
```

### AnimatedBackground (`src/components/AnimatedBackground.tsx`)

**Where to change styles:**

- **Background effects**: `src/components/AnimatedBackground.tsx` (inline styles for GSAP)
- **Glass effects**: `src/styles/index.css` (`.glass` class)

**Note**: This component uses inline styles for GSAP animations. Changes should be made directly in the component file.

## 🎨 Common Patterns

### Buttons

```jsx
// Primary button
<button className="btn btn-primary">Primary Action</button>

// Secondary button
<button className="btn btn-secondary">Secondary Action</button>

// Ghost button
<button className="btn btn-ghost">Ghost Action</button>

// Icon button
<button className="btn-icon">
  <Icon className="h-5 w-5" />
</button>

// Button sizes
<button className="btn btn-primary btn-sm">Small</button>
<button className="btn btn-primary btn-lg">Large</button>
```

### Cards

```jsx
// Basic card
<div className='card'>
  <div className='card-header'>
    <h3>Card Title</h3>
  </div>
  <div className='card-body'>
    <p>Card content</p>
  </div>
  <div className='card-footer'>
    <button className='btn btn-primary'>Action</button>
  </div>
</div>

// Interactive card
<div className='card card-interactive'>
  <div className='card-body'>
    <p>Hover me!</p>
  </div>
</div>

// Glass card
<div className='card card-glass'>
  <div className='card-body'>
    <p>Glass effect</p>
  </div>
</div>
```

### Modals

```jsx
// Modal structure
<div className='modal-backdrop' onClick={onClose}>
  <div className='modal-container' onClick={(e) => e.stopPropagation()}>
    <div className='modal-content'>
      <div className='modal-header'>
        <h2>Modal Title</h2>
        <button className='modal-close' onClick={onClose}>
          ×
        </button>
      </div>
      <div className='modal-body'>
        <p>Modal content</p>
      </div>
      <div className='modal-footer'>
        <button className='btn btn-secondary' onClick={onClose}>
          Cancel
        </button>
        <button className='btn btn-primary'>Save</button>
      </div>
    </div>
  </div>
</div>
```

### Forms

```jsx
// Form structure
<form>
  <label className='form-label'>Email</label>
  <input className='form-input' type='email' placeholder='Enter email' />
  <div className='form-error'>Invalid email format</div>

  <label className='form-label'>Category</label>
  <select className='form-select'>
    <option>Option 1</option>
    <option>Option 2</option>
  </select>
</form>
```

### Badges

```jsx
// Badge variants
<span className="badge badge-primary">Primary</span>
<span className="badge badge-secondary">Secondary</span>
<span className="badge badge-success">Success</span>
<span className="badge badge-warning">Warning</span>
<span className="badge badge-error">Error</span>
```

### Progress

```jsx
// Progress bar
<div className='flex items-center gap-4'>
  <div className='progress-bar'>
    <div
      className='progress-fill'
      style={{ width: `${progressPercentage}%` }}
    />
  </div>
  <span className='progress-label'>{progressPercentage}%</span>
</div>
```

### Timeline

```jsx
// Timeline structure
<div className='timeline-container'>
  <div className='timeline-line' />

  <div className='timeline-item'>
    <div className='timeline-dot timeline-dot-completed' />
    <div className='timeline-content'>
      <h3 className='timeline-title'>Completed Step</h3>
      <p className='timeline-date'>2024-01-15</p>
    </div>
  </div>

  <div className='timeline-item'>
    <div className='timeline-dot timeline-dot-current' />
    <div className='timeline-content'>
      <h3 className='timeline-title'>Current Step</h3>
      <p className='timeline-date'>2024-01-20</p>
    </div>
  </div>
</div>
```

## 🔧 Migration Checklist

When updating a component:

1. **Identify the component** in this guide
2. **Find the style location** listed above
3. **Replace inline styles** with component classes
4. **Replace long Tailwind strings** with component classes
5. **Use design tokens** instead of hardcoded values
6. **Test the changes** across different screen sizes
7. **Update this guide** if adding new patterns

## 🎯 Best Practices

### 1. Use Design Tokens

**✅ Good:**

```jsx
<div className='bg-primary-500 text-white p-4 rounded-lg'>Content</div>
```

**❌ Bad:**

```jsx
<div className='bg-blue-600 text-white p-4 rounded-lg'>Content</div>
```

### 2. Use Component Classes

**✅ Good:**

```jsx
<button className='btn btn-primary'>Action</button>
```

**❌ Bad:**

```jsx
<button className='inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium bg-primary-600 text-white hover:bg-primary-700'>
  Action
</button>
```

### 3. Combine Component Classes with Tailwind

**✅ Good:**

```jsx
<div className='card mb-6'>
  <div className='card-header flex-between'>
    <h3>Title</h3>
    <button className='btn btn-icon'>×</button>
  </div>
</div>
```

### 4. Avoid Inline Styles (except for dynamic values)

**✅ Good:**

```jsx
<div className='progress-fill' style={{ width: `${progressPercentage}%` }} />
```

**❌ Bad:**

```jsx
<div
  style={{
    backgroundColor: "#3b82f6",
    color: "white",
    padding: "16px",
    borderRadius: "8px",
  }}
>
  Content
</div>
```

## 📝 Notes

- **Inline styles**: Only use for dynamic values (like GSAP animations)
- **Component classes**: Use for consistent, reusable patterns
- **Design tokens**: Use for colors, spacing, typography
- **Tailwind utilities**: Use for one-off adjustments only
- **Testing**: Always test changes across different screen sizes and browsers

---

**Last Updated**: December 2024  
**Version**: 2.0.0  
**Maintainer**: Development Team

## Header Component

The header component has its own dedicated CSS classes in `components.css` under the "HEADER COMPONENTS" section.

### Main Header Classes

- `.header` - Main header container with fixed positioning and neumorphic styling
- `.header-content` - Inner content container with flex layout
- `.header-spacer` - Spacer div to prevent content from being hidden behind fixed header

### Header Brand Section

- `.header-brand` - Container for the project name and subtitle
- `.header-brand-label` - "Name:" label styling
- `.header-brand-title` - Main project title ("Project Roots")
- `.header-brand-subtitle` - Project subtitle ("Persona Immersion")

### Header Navigation

- `.header-navigation` - Navigation container
- `.header-nav-list` - List of navigation items
- `.header-nav-item` - Individual navigation button base styles
- `.header-nav-item-active` - Active navigation item styling
- `.header-nav-item-inactive` - Inactive navigation item styling

### Header Actions

- `.header-actions` - Right-side actions container
- `.header-user-status` - User login status indicator
- `.header-user-indicator` - Green dot indicator
- `.header-user-email` - User email text
- `.header-action-button` - Standard action buttons (search, share)
- `.header-ai-button` - Special AI button with gradient background

### How to Modify Header Styles

1. **Change header background**: Modify `--color-header-bg` in `index.css` (recommended) or `.header` background-color in `components.css`
2. **Adjust header height**: Change `--header-height` in `index.css`
3. **Update navigation colors**: Modify `.header-nav-item-active` and `.header-nav-item-inactive`
4. **Change AI button gradient**: Update `.header-ai-button` background property
5. **Modify spacing**: Adjust padding and margin values in respective classes

### Example Usage in JSX

```jsx
<header className='header left-64'>
  <div className='header-content'>
    <div className='header-brand'>
      <span className='header-brand-label'>Name:</span>
      <h1 className='header-brand-title'>Project Roots</h1>
    </div>
    <nav className='header-navigation'>
      <div className='header-nav-list'>
        <button className='header-nav-item header-nav-item-active'>
          Overview
        </button>
      </div>
    </nav>
    <div className='header-actions'>
      <button className='header-action-button'>Search</button>
      <button className='header-ai-button'>AI</button>
    </div>
  </div>
</header>
```

## Button Components

### Base Button Classes

- `.btn` - Base button styles with consistent padding, border-radius, and transitions
- `.btn-primary` - Primary button with blue background and hover effects
- `.btn-secondary` - Secondary button with gray background and border
- `.btn-ghost` - Ghost button with transparent background
- `.btn-icon` - Icon-only button with square aspect ratio

### Button Sizes

- `.btn-sm` - Small button with reduced padding
- `.btn-lg` - Large button with increased padding

### How to Modify Button Styles

1. **Change button colors**: Modify the respective button variant classes
2. **Adjust button size**: Update padding values in size classes
3. **Update border-radius**: Change `--radius-lg` in `index.css` for consistent radius
4. **Modify hover effects**: Update transform and box-shadow properties

### Example Usage

```jsx
<button className="btn btn-primary">Primary Button</button>
<button className="btn btn-secondary btn-sm">Small Secondary</button>
<button className="btn btn-icon">🔍</button>
```

## Card Components

### Base Card Classes

- `.card` - Base card with white background, shadow, and border-radius
- `.card-header` - Card header section with gray background
- `.card-body` - Main card content area
- `.card-footer` - Card footer section with gray background

### Card Variants

- `.card-interactive` - Interactive card with hover animations
- `.card-glass` - Glass morphism effect with backdrop blur

### How to Modify Card Styles

1. **Change card background**: Modify `.card` background-color
2. **Adjust card shadow**: Update box-shadow in `.card` and hover states
3. **Update border-radius**: Change `--radius-xl` in `index.css`
4. **Modify glass effect**: Adjust backdrop-filter and background opacity in `.card-glass`

### Example Usage

```jsx
<div className='card card-interactive'>
  <div className='card-header'>
    <h3>Card Title</h3>
  </div>
  <div className='card-body'>
    <p>Card content goes here</p>
  </div>
</div>
```

## Form Components

### Input Classes

- `.form-input` - Text input with consistent styling
- `.form-select` - Select dropdown with custom arrow
- `.form-label` - Form label styling
- `.form-error` - Error message styling

### How to Modify Form Styles

1. **Change input colors**: Update border and focus colors in `.form-input`
2. **Adjust input size**: Modify padding in `.form-input`
3. **Update focus ring**: Change box-shadow color in focus state
4. **Modify border-radius**: Update `--radius-lg` in `index.css`

### Example Usage

```jsx
<label className="form-label">Email</label>
<input type="email" className="form-input" placeholder="Enter email" />
<span className="form-error">Invalid email format</span>
```

## Modal Components

### Modal Classes

- `.modal-backdrop` - Full-screen backdrop with blur effect
- `.modal-container` - Modal container with animations
- `.modal-content` - Modal content wrapper
- `.modal-header` - Modal header section
- `.modal-body` - Modal body content
- `.modal-footer` - Modal footer with actions
- `.modal-close` - Close button styling

### How to Modify Modal Styles

1. **Change backdrop blur**: Update backdrop-filter in `.modal-backdrop`
2. **Adjust modal size**: Modify max-width in `.modal-container`
3. **Update animations**: Change keyframes in `modalSlideIn` animation
4. **Modify border-radius**: Update `--radius-2xl` in `index.css`

### Example Usage

```jsx
<div className='modal-backdrop'>
  <div className='modal-container'>
    <div className='modal-content'>
      <div className='modal-header'>
        <h2>Modal Title</h2>
        <button className='modal-close'>×</button>
      </div>
      <div className='modal-body'>
        <p>Modal content</p>
      </div>
    </div>
  </div>
</div>
```

## Navigation Components

### Navigation Classes

- `.nav-item` - Base navigation item
- `.nav-item-active` - Active navigation state
- `.nav-item-inactive` - Inactive navigation state
- `.nav-item-icon` - Navigation icon styling

### How to Modify Navigation Styles

1. **Change active colors**: Update `.nav-item-active` background and text colors
2. **Adjust hover effects**: Modify `.nav-item-inactive:hover` styles
3. **Update spacing**: Change padding in `.nav-item`
4. **Modify border-radius**: Update `--radius-lg` in `index.css`

### Example Usage

```jsx
<nav>
  <button className='nav-item nav-item-active'>
    <Icon className='nav-item-icon' />
    Overview
  </button>
  <button className='nav-item nav-item-inactive'>
    <Icon className='nav-item-icon' />
    Settings
  </button>
</nav>
```

## Badge Components

### Badge Classes

- `.badge` - Base badge with consistent styling
- `.badge-primary` - Primary badge variant
- `.badge-secondary` - Secondary badge variant
- `.badge-success` - Success badge variant
- `.badge-warning` - Warning badge variant
- `.badge-error` - Error badge variant

### How to Modify Badge Styles

1. **Change badge colors**: Update background and text colors in variant classes
2. **Adjust badge size**: Modify padding in `.badge`
3. **Update border-radius**: Change `--radius-full` in `index.css`
4. **Modify font size**: Update font-size in `.badge`

### Example Usage

```jsx
<span className="badge badge-primary">New</span>
<span className="badge badge-success">Completed</span>
<span className="badge badge-error">Error</span>
```

## Progress Components

### Progress Classes

- `.progress-bar` - Progress bar container
- `.progress-fill` - Progress fill with gradient
- `.progress-label` - Progress label styling

### How to Modify Progress Styles

1. **Change progress colors**: Update gradient in `.progress-fill`
2. **Adjust progress height**: Modify height in `.progress-bar`
3. **Update border-radius**: Change `--radius-full` in `index.css`
4. **Modify label styling**: Update `.progress-label` colors and font

### Example Usage

```jsx
<div>
  <div className='progress-label'>Loading...</div>
  <div className='progress-bar'>
    <div className='progress-fill' style={{ width: "75%" }}></div>
  </div>
</div>
```

## Timeline Components

### Timeline Classes

- `.timeline-container` - Timeline wrapper
- `.timeline-line` - Vertical timeline line
- `.timeline-item` - Individual timeline item
- `.timeline-dot` - Timeline dot indicator
- `.timeline-dot-completed` - Completed state dot
- `.timeline-dot-current` - Current state dot
- `.timeline-dot-pending` - Pending state dot
- `.timeline-content` - Timeline item content
- `.timeline-title` - Timeline item title
- `.timeline-date` - Timeline item date

### How to Modify Timeline Styles

1. **Change timeline colors**: Update dot colors and line color
2. **Adjust spacing**: Modify margins in `.timeline-item`
3. **Update dot size**: Change width/height in `.timeline-dot`
4. **Modify line position**: Update left position in `.timeline-line`

### Example Usage

```jsx
<div className='timeline-container'>
  <div className='timeline-line'></div>
  <div className='timeline-item'>
    <div className='timeline-dot timeline-dot-completed'></div>
    <div className='timeline-content'>
      <div className='timeline-title'>Step 1</div>
      <div className='timeline-date'>Completed</div>
    </div>
  </div>
</div>
```

## Persona Components

### Persona Classes

- `.persona-card` - Persona card container
- `.persona-card-header` - Card header with avatar and info
- `.persona-card-body` - Main card content
- `.persona-card-footer` - Card footer with actions
- `.persona-card-avatar` - Avatar image styling
- `.persona-category-badge` - Category badge styling

### How to Modify Persona Styles

1. **Change card appearance**: Update `.persona-card` background and shadow
2. **Adjust avatar size**: Modify width/height in `.persona-card-avatar`
3. **Update hover effects**: Change transform in `.persona-card:hover`
4. **Modify badge styling**: Update `.persona-category-badge` colors

### Example Usage

```jsx
<div className='persona-card'>
  <div className='persona-card-header'>
    <img src='avatar.jpg' className='persona-card-avatar' />
    <span className='persona-category-badge'>Primary</span>
  </div>
  <div className='persona-card-body'>
    <h3>Persona Name</h3>
    <p>Description...</p>
  </div>
</div>
```

## Carousel Components

### Carousel Classes

- `.carousel-arrow` - Navigation arrow buttons
- `.carousel-arrow.left-8` - Left arrow positioning
- `.carousel-arrow.right-8` - Right arrow positioning
- `.carousel-dot` - Carousel dot indicator
- `.carousel-dot-active` - Active dot styling
- `.carousel-dot-inactive` - Inactive dot styling

### How to Modify Carousel Styles

1. **Change arrow appearance**: Update background and shadow in `.carousel-arrow`
2. **Adjust dot colors**: Modify active and inactive dot colors
3. **Update positioning**: Change left/right positioning for arrows
4. **Modify hover effects**: Update transform and background in hover states

### Example Usage

```jsx
<button className="carousel-arrow left-8">‹</button>
<button className="carousel-arrow right-8">›</button>
<button className="carousel-dot carousel-dot-active"></button>
<button className="carousel-dot carousel-dot-inactive"></button>
```

## Layout Components

### Layout Classes

- `.section-container` - Section wrapper with consistent spacing
- `.grid-responsive` - Responsive grid layout
- `.divider` - Horizontal divider line
- `.spacer` - Vertical spacing element

### Utility Classes

- `.flex-center` - Flexbox center alignment
- `.flex-between` - Flexbox space-between alignment
- `.flex-start` - Flexbox start alignment
- `.flex-end` - Flexbox end alignment
- `.text-truncate` - Text truncation with ellipsis
- `.text-balance` - Text balance for better readability

### Animation Classes

- `.fade-in` - Fade in animation
- `.slide-up` - Slide up animation
- `.scale-in` - Scale in animation

### How to Modify Layout Styles

1. **Change section spacing**: Update margin-bottom in `.section-container`
2. **Adjust grid breakpoints**: Modify minmax values in `.grid-responsive`
3. **Update divider color**: Change background-color in `.divider`
4. **Modify animations**: Update keyframes for animation classes

### Example Usage

```jsx
<div className='section-container'>
  <div className='grid-responsive'>
    <div className='card'>Content 1</div>
    <div className='card'>Content 2</div>
  </div>
  <div className='divider'></div>
  <div className='flex-center'>
    <button className='btn btn-primary'>Action</button>
  </div>
</div>
```

## Best Practices

1. **Use semantic class names**: All classes are descriptive and follow BEM-like naming
2. **Leverage CSS custom properties**: Most values use design tokens from `index.css`
3. **Maintain consistency**: Use the provided classes rather than inline styles
4. **Test responsive behavior**: Ensure components work across different screen sizes
5. **Follow the hierarchy**: Use base classes with modifier classes for variants

## Modifying Design Tokens

Most component styles use CSS custom properties defined in `src/styles/index.css`. To make global changes:

- **Colors**: Update color variables in the `:root` section
- **Spacing**: Modify spacing variables for consistent spacing
- **Typography**: Change font-size and font-weight variables
- **Shadows**: Update shadow variables for consistent depth
- **Border radius**: Modify radius variables for consistent rounding
- **Transitions**: Update transition variables for consistent animations

This ensures all components maintain visual consistency when making design changes.
