import { useState, useCallback } from "react";
import { ApiError } from "../services/apiClient";

// API state interface
export interface ApiState<T = any> {
  data: T | null;
  loading: boolean;
  error: ApiError | null;
}

// API actions interface
export interface ApiActions<T = any> {
  execute: (...args: any[]) => Promise<T>;
  reset: () => void;
  setData: (data: T) => void;
  setError: (error: ApiError) => void;
}

// Hook return type
export type UseApiReturn<T = any> = [ApiState<T>, ApiActions<T>];

/**
 * Custom hook for managing API calls with loading and error states
 */
export function useApi<T = any>(
  apiFunction: (...args: any[]) => Promise<T>,
  initialData: T | null = null
): UseApiReturn<T> {
  const [state, setState] = useState<ApiState<T>>({
    data: initialData,
    loading: false,
    error: null,
  });

  const execute = useCallback(
    async (...args: any[]) => {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      try {
        const result = await apiFunction(...args);
        setState((prev) => ({ ...prev, data: result, loading: false }));
        return result;
      } catch (error) {
        const apiError = error as ApiError;
        setState((prev) => ({ ...prev, error: apiError, loading: false }));
        throw apiError;
      }
    },
    [apiFunction]
  );

  const reset = useCallback(() => {
    setState({
      data: initialData,
      loading: false,
      error: null,
    });
  }, [initialData]);

  const setData = useCallback((data: T) => {
    setState((prev) => ({ ...prev, data, error: null }));
  }, []);

  const setError = useCallback((error: ApiError) => {
    setState((prev) => ({ ...prev, error, loading: false }));
  }, []);

  const actions: ApiActions<T> = {
    execute,
    reset,
    setData,
    setError,
  };

  return [state, actions];
}

/**
 * Hook for managing multiple API calls
 */
export function useMultipleApis<T extends Record<string, any>>(
  apiFunctions: T
): {
  states: { [K in keyof T]: ApiState<Awaited<ReturnType<T[K]>>> };
  actions: { [K in keyof T]: ApiActions<Awaited<ReturnType<T[K]>>> };
} {
  const states: any = {};
  const actions: any = {};

  Object.keys(apiFunctions).forEach((key) => {
    const [state, action] = useApi(apiFunctions[key]);
    states[key] = state;
    actions[key] = action;
  });

  return { states, actions };
}

/**
 * Hook for managing form submission with API
 */
export function useApiForm<T = any>(
  apiFunction: (data: any) => Promise<T>
): [
  ApiState<T>,
  {
    submit: (data: any) => Promise<T>;
    reset: () => void;
    setData: (data: T) => void;
    setError: (error: ApiError) => void;
  }
] {
  const [state, actions] = useApi(apiFunction);

  const submit = useCallback(
    async (data: any) => {
      return await actions.execute(data);
    },
    [actions]
  );

  return [
    state,
    {
      submit,
      reset: actions.reset,
      setData: actions.setData,
      setError: actions.setError,
    },
  ];
}
