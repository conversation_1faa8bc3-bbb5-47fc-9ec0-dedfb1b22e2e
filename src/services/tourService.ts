import { TourStep } from "../components/TourGuide";

// Tour configurations for different pages
export const MAIN_WEBSITE_TOUR: TourStep[] = [
  {
    id: "welcome",
    target: ".sidebar", // Target the sidebar container
    title: "Welcome to Scoot Insights!",
    content:
      "Hi {firstName},\n\nThis demo introduces a **streamlined tool** built to help **marketing clients easily navigate** and make sense of the **research** they've commissioned.\n\nSome features are still in progress, but this preview is meant to give you a sense of how the process can be made **more efficient** - and how **insights can be surfaced more quickly**.\n\nTake a look around and see what's possible.",
    position: "right",
  },
  {
    id: "timeline-section",
    target: '[data-tour="timeline-nav"]',
    title: "Project Timeline",
    content:
      "View your research timeline with important milestones, deadlines, and completed phases.",
    position: "right",
    navigateTo: "timeline", // Auto-navigate to timeline section
  },
  {
    id: "progress-section",
    target: '[data-tour="progress-nav"]',
    title: "Progress Overview",
    content:
      "Track your research progress, goals, and key metrics. See how your project is advancing at a glance.",
    position: "right",
    navigateTo: "progress", // Auto-navigate to progress section
  },
  {
    id: "team-section",
    target: '[data-tour="team-nav"]',
    title: "Research Team",
    content:
      "Meet your research team members, their roles, and contact information.",
    position: "right",
    navigateTo: "team", // Auto-navigate to team section
  },
  {
    id: "personas-section",
    target: '[data-tour="personas-nav"]',
    title: "Personas",
    content:
      "Explore detailed user personas based on your research findings. Click to dive deeper into each persona.",
    position: "right",
    navigateTo: "personas", // Auto-navigate to personas section
  },
  {
    id: "media-section",
    target: '[data-tour="media-nav"]',
    title: "Research Media",
    content:
      "Access recorded interviews, user sessions, research videos, and audio discussions for deeper insights.",
    position: "right",
    navigateTo: "media", // Auto-navigate to media section
  },
  {
    id: "help-button",
    target: ".help-button", // Target the help button
    title: "Need Help?",
    content:
      "Click the help button anytime to restart this tour or get assistance.",
    position: "left",
  },
];

export const ADMIN_TOUR: TourStep[] = [
  {
    id: "admin-welcome",
    target: ".admin-header", // Target the admin header
    title: "Admin Dashboard",
    content:
      "Welcome to the admin panel! Here you can manage your multi-brand setup and configure settings.",
    position: "bottom",
  },
  {
    id: "add-brand-button",
    target: '[data-tour="add-brand-btn"]',
    title: "Add New Brand",
    content:
      "Create new brand configurations with custom logos and settings for different research projects.",
    position: "bottom",
  },
  {
    id: "brand-cards",
    target: '[data-tour="brand-cards"]',
    title: "Brand Management",
    content:
      "View and manage all your brands. Each card shows the brand logo, name, and quick actions.",
    position: "top",
  },
  {
    id: "brand-actions",
    target: '[data-tour="brand-actions"]',
    title: "Brand Actions",
    content:
      "Navigate to a brand's dashboard or edit its settings. The default brand cannot be deleted.",
    position: "left",
  },
  {
    id: "settings-button",
    target: '[data-tour="settings-btn"]',
    title: "Brand Settings",
    content:
      "Click the settings icon to edit brand name, logo, and other configurations.",
    position: "left",
  },
];

// Local storage keys
const TOUR_COMPLETED_KEY = "scoot_insights_tour_completed";
const ADMIN_TOUR_COMPLETED_KEY = "scoot_insights_admin_tour_completed";

// Check if a tour has been completed
export function isTourCompleted(tourId: string): boolean {
  const key =
    tourId === "admin" ? ADMIN_TOUR_COMPLETED_KEY : TOUR_COMPLETED_KEY;
  const sessionKey = `session_${key}`;

  // Check if this is a new session (new tab/window)
  const currentSession = sessionStorage.getItem(sessionKey);
  if (!currentSession) {
    // This is a new session, reset the tour completion
    localStorage.removeItem(key);
    sessionStorage.setItem(sessionKey, "true");
    return false;
  }

  return localStorage.getItem(key) === "true";
}

// Mark a tour as completed
export function markTourCompleted(tourId: string): void {
  const key =
    tourId === "admin" ? ADMIN_TOUR_COMPLETED_KEY : TOUR_COMPLETED_KEY;
  localStorage.setItem(key, "true");
}

// Reset tour completion status (for testing or admin purposes)
export function resetTourCompletion(tourId?: string): void {
  if (tourId) {
    const key =
      tourId === "admin" ? ADMIN_TOUR_COMPLETED_KEY : TOUR_COMPLETED_KEY;
    localStorage.removeItem(key);
    sessionStorage.removeItem(`session_${key}`);
  } else {
    localStorage.removeItem(TOUR_COMPLETED_KEY);
    localStorage.removeItem(ADMIN_TOUR_COMPLETED_KEY);
    sessionStorage.removeItem(`session_${TOUR_COMPLETED_KEY}`);
    sessionStorage.removeItem(`session_${ADMIN_TOUR_COMPLETED_KEY}`);
  }
}

// Get tour steps based on tour ID
export function getTourSteps(tourId: string): TourStep[] {
  switch (tourId) {
    case "admin":
      return ADMIN_TOUR;
    case "main":
    default:
      return MAIN_WEBSITE_TOUR;
  }
}

// Check if user should see tour (first time or reset)
export function shouldShowTour(tourId: string): boolean {
  return !isTourCompleted(tourId);
}
