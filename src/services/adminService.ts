import { api, default as apiClient } from "./apiClient";

// Admin PIN interface
export interface AdminPin {
  id: string;
  pin: string;
  createdAt: string;
  updatedAt: string;
}

// PIN validation interface
export interface PinValidationData {
  pin: string;
}

// PIN update interface
export interface PinUpdateData {
  pin: string;
  currentPin: string;
}

// Admin service class
class AdminService {
  private readonly baseUrl = "/admin";

  /**
   * Validate admin PIN
   */
  async validatePin(pin: string): Promise<boolean> {
    try {
      // Use the raw axios client for this endpoint since it doesn't return data
      const response = await apiClient.post(`${this.baseUrl}/validate-pin`, {
        pin,
      });
      return response.data.success === true;
    } catch (error) {
      console.error("Error validating PIN:", error);
      return false;
    }
  }

  /**
   * Check if admin PIN exists
   */
  async pinExists(): Promise<boolean> {
    try {
      const response = await api.get<{ exists: boolean }>(
        `${this.baseUrl}/pin-exists`
      );
      return response.exists;
    } catch (error) {
      console.error("Error checking PIN existence:", error);
      throw error;
    }
  }

  /**
   * Set admin PIN (for first-time setup)
   */
  async setPin(pin: string): Promise<AdminPin> {
    try {
      return await api.post<AdminPin>(`${this.baseUrl}/set-pin`, { pin });
    } catch (error) {
      console.error("Error setting PIN:", error);
      throw error;
    }
  }

  /**
   * Update admin PIN
   */
  async updatePin(currentPin: string, newPin: string): Promise<void> {
    try {
      await api.put<void>(`${this.baseUrl}/update-pin`, {
        currentPin,
        pin: newPin,
      });
    } catch (error) {
      console.error("Error updating PIN:", error);
      throw error;
    }
  }

  /**
   * Validate PIN format
   */
  validatePinFormat(pin: string): { isValid: boolean; error?: string } {
    if (!pin || pin.length !== 6) {
      return {
        isValid: false,
        error: "PIN must be exactly 6 characters",
      };
    }

    if (!/^[A-Za-z0-9]{6}$/.test(pin)) {
      return {
        isValid: false,
        error: "PIN can only contain letters and numbers",
      };
    }

    return { isValid: true };
  }

  /**
   * Format PIN for display (masked)
   */
  formatPinForDisplay(pin: string): string {
    if (!pin) return "";
    return pin.replace(/./g, "•");
  }

  /**
   * Generate a random PIN
   */
  generateRandomPin(): string {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = "";
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}

// Export singleton instance
export const adminService = new AdminService();
export default adminService;
