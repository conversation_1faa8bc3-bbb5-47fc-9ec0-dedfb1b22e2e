import { api } from "./apiClient";

// Upload response interface
export interface UploadResponse {
  url: string;
  key: string;
  originalName: string;
  size: number;
  mimetype: string;
}

// File validation result interface
export interface FileValidationResult {
  isValid: boolean;
  error?: string;
}

// S3 status interface
export interface S3Status {
  configured: boolean;
  connected: boolean;
  bucket: string | null;
  region: string;
}

// Upload service class
class UploadService {
  private readonly baseUrl = "/upload";

  /**
   * Validate file before upload
   */
  validateFile(file: File): FileValidationResult {
    // Check file type
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
    ];
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: "Only JPEG, JPG, PNG, GIF, and WebP files are allowed.",
      };
    }

    // Check file size (10MB = 10 * 1024 * 1024 bytes)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `File size must be less than ${maxSize / (1024 * 1024)}MB.`,
      };
    }

    return { isValid: true };
  }

  /**
   * Upload logo file to S3
   */
  async uploadLogo(
    file: File,
    type: "client" | "brand" = "client"
  ): Promise<UploadResponse> {
    try {
      // Validate file first
      const validation = this.validateFile(file);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // Create FormData
      const formData = new FormData();
      formData.append("file", file);
      formData.append("type", type);

      // Upload to backend
      return await api.upload<UploadResponse>(`${this.baseUrl}/logo`, formData);
    } catch (error) {
      console.error("❌ Error uploading logo:", error);
      throw error;
    }
  }

  /**
   * Replace existing logo file in S3
   */
  async replaceLogo(
    file: File,
    oldLogoUrl: string,
    type: "client" | "brand" = "client"
  ): Promise<UploadResponse> {
    try {
      // Validate file first
      const validation = this.validateFile(file);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // Create FormData
      const formData = new FormData();
      formData.append("file", file);
      formData.append("oldLogoUrl", oldLogoUrl);
      formData.append("type", type);

      // Replace logo via backend
      return await api.put<UploadResponse>(
        `${this.baseUrl}/logo/replace`,
        formData
      );
    } catch (error) {
      console.error("❌ Error replacing logo:", error);
      throw error;
    }
  }

  /**
   * Delete logo file from S3
   */
  async deleteLogo(key: string): Promise<void> {
    try {
      await api.delete<void>(
        `${this.baseUrl}/logo?key=${encodeURIComponent(key)}`
      );
    } catch (error) {
      console.error("❌ Error deleting logo:", error);
      throw error;
    }
  }

  /**
   * Delete logo file from S3 by URL
   */
  async deleteLogoByUrl(url: string): Promise<void> {
    try {
      await api.delete<void>(
        `${this.baseUrl}/logo?url=${encodeURIComponent(url)}`
      );
    } catch (error) {
      console.error("❌ Error deleting logo by URL:", error);
      throw error;
    }
  }

  /**
   * Check S3 connection status
   */
  async checkS3Status(): Promise<S3Status> {
    try {
      const response = await api.get<S3Status>(`${this.baseUrl}/status`);
      return response;
    } catch (error) {
      console.error("❌ Error checking S3 status:", error);
      throw error;
    }
  }

  /**
   * Convert file to base64 data URL (for preview)
   */
  fileToDataUrl(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Get file extension from filename
   */
  getFileExtension(filename: string): string {
    return filename.split(".").pop()?.toLowerCase() || "";
  }

  /**
   * Get file size in human readable format
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Check if file is an image
   */
  isImageFile(file: File): boolean {
    return file.type.startsWith("image/");
  }

  /**
   * Get image dimensions from file
   */
  getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({ width: img.width, height: img.height });
      };
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Check if URL is a valid S3 URL
   */
  isValidS3Url(url: string): boolean {
    if (!url) return false;
    return url.includes("s3.amazonaws.com") || url.includes("amazonaws.com");
  }

  /**
   * Get a placeholder logo URL for testing
   */
  getPlaceholderLogo(text: string, color: string = "3B82F6"): string {
    return `https://via.placeholder.com/200x200/${color}/FFFFFF?text=${encodeURIComponent(
      text
    )}`;
  }
}

// Export singleton instance
export const uploadService = new UploadService();
export default uploadService;
