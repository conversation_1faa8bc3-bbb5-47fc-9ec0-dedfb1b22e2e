import { api } from "./apiClient";

// Brand interface matching backend model
export interface Brand {
  id: string;
  name: string;
  logo: string;
  endpoint: string;
  clientId: string;
  isDefault: boolean; // Add isDefault field
  createdAt: string;
  updatedAt: string;
  client?: {
    id: string;
    name: string;
    firstName?: string;
    logo: string;
  };
}

// Brand creation interface
export interface CreateBrandData {
  name: string;
  logo: string;
  endpoint: string;
  clientId: string;
  isDefault?: boolean; // Add isDefault field
}

// Brand update interface
export interface UpdateBrandData {
  name?: string;
  logo?: string;
  endpoint?: string;
  clientId?: string;
  isDefault?: boolean; // Add isDefault field
}

// Brand service class
class BrandApiService {
  private readonly baseUrl = "/brands";

  /**
   * Get all brands with client information
   */
  async getAllBrands(): Promise<Brand[]> {
    try {
      return await api.get<Brand[]>(this.baseUrl);
    } catch (error) {
      console.error("Error fetching brands:", error);
      throw error;
    }
  }

  /**
   * Get brand by ID with client information
   */
  async getBrandById(id: string): Promise<Brand> {
    try {
      return await api.get<Brand>(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`Error fetching brand ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get brands by client ID
   */
  async getBrandsByClientId(clientId: string): Promise<Brand[]> {
    try {
      return await api.get<Brand[]>(`${this.baseUrl}/client/${clientId}`);
    } catch (error) {
      console.error(`Error fetching brands for client ${clientId}:`, error);
      throw error;
    }
  }

  /**
   * Create new brand
   */
  async createBrand(brandData: CreateBrandData): Promise<Brand> {
    try {
      return await api.post<Brand>(this.baseUrl, brandData);
    } catch (error) {
      console.error("Error creating brand:", error);
      throw error;
    }
  }

  /**
   * Update brand
   */
  async updateBrand(id: string, brandData: UpdateBrandData): Promise<Brand> {
    try {
      return await api.put<Brand>(`${this.baseUrl}/${id}`, brandData);
    } catch (error) {
      console.error(`Error updating brand ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete brand
   */
  async deleteBrand(id: string): Promise<void> {
    try {
      await api.delete<void>(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`Error deleting brand ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get brand by endpoint - direct API call
   */
  async getBrandByEndpoint(endpoint: string): Promise<Brand | null> {
    try {
      return await api.get<Brand>(`${this.baseUrl}/endpoint/${endpoint}`);
    } catch (error) {
      console.error(`Error fetching brand by endpoint ${endpoint}:`, error);
      // If brand not found, return null instead of throwing
      if (
        error &&
        typeof error === "object" &&
        "status" in error &&
        error.status === 404
      ) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Get default brand
   */
  async getDefaultBrand(): Promise<Brand> {
    try {
      return await api.get<Brand>(`${this.baseUrl}/default`);
    } catch (error) {
      console.error("Error fetching default brand:", error);
      throw error;
    }
  }

  /**
   * Validate brand endpoint uniqueness
   */
  async validateBrandEndpoint(
    endpoint: string,
    excludeId?: string
  ): Promise<boolean> {
    try {
      const brands = await this.getAllBrands();
      const existingBrand = brands.find(
        (brand) => brand.endpoint === endpoint && brand.id !== excludeId
      );
      return !existingBrand;
    } catch (error) {
      console.error("Error validating brand endpoint:", error);
      throw error;
    }
  }

  /**
   * Convert brand name to URL-friendly endpoint
   */
  nameToEndpoint(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single
      .trim();
  }

  /**
   * Generate unique endpoint from name
   */
  async generateUniqueEndpoint(
    name: string,
    excludeId?: string
  ): Promise<string> {
    const endpoint = this.nameToEndpoint(name);
    let counter = 1;
    let uniqueEndpoint = endpoint;

    while (!(await this.validateBrandEndpoint(uniqueEndpoint, excludeId))) {
      uniqueEndpoint = `${endpoint}-${counter}`;
      counter++;
    }

    return uniqueEndpoint;
  }
}

// Export singleton instance
export const brandApiService = new BrandApiService();
export default brandApiService;
