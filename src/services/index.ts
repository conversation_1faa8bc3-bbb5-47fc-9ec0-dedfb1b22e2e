// Export all API services
export { default as apiClient } from "./apiClient";
export { default as brandApiService } from "./brandApiService";
export { default as clientApiService } from "./clientApiService";
export { default as uploadService } from "./uploadService";
export { default as adminService } from "./adminService";

// Export types
export type { ApiResponse, ApiError } from "./apiClient";
export type {
  Brand,
  CreateBrandData,
  UpdateBrandData,
} from "./brandApiService";
export type {
  Client as ClientApi,
  CreateClientData as CreateClientApiData,
  UpdateClientData as UpdateClientApiData,
} from "./clientApiService";
export type { UploadResponse, FileValidationResult } from "./uploadService";
export type {
  AdminPin,
  PinValidationData,
  PinUpdateData,
} from "./adminService";

// Legacy exports for backward compatibility - keeping only essential services
export * from "./personaService";
export * from "./tourService";
