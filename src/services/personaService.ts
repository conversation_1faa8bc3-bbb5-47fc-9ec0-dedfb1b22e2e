import { Persona } from '../types/persona';
import { mockPersonas } from '../data/mockPersonas';

class PersonaService {
  async getPersona(id: string): Promise<Persona | null> {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockPersonas[id] || null;
  }

  async getAllPersonas(): Promise<Persona[]> {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    return Object.values(mockPersonas);
  }
}

export const personaService = new PersonaService();
