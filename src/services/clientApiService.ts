import { api } from "./apiClient";

// Client interface matching backend model
export interface Client {
  id: string;
  name: string;
  firstName?: string;
  logo: string;
  description?: string;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
}

// Client creation interface
export interface CreateClientData {
  name: string;
  firstName?: string;
  logo: string;
  description?: string;
  enabled?: boolean;
}

// Client update interface
export interface UpdateClientData {
  name?: string;
  firstName?: string;
  logo?: string;
  description?: string;
  enabled?: boolean;
}

// Client service class
class ClientApiService {
  private readonly baseUrl = "/clients";

  /**
   * Get all enabled clients
   */
  async getAllClients(): Promise<Client[]> {
    try {
      return await api.get<Client[]>(this.baseUrl);
    } catch (error) {
      console.error("Error fetching clients:", error);
      throw error;
    }
  }

  /**
   * Get client by ID
   */
  async getClientById(id: string): Promise<Client> {
    try {
      return await api.get<Client>(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`Error fetching client ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get client by name
   */
  async getClientByName(name: string): Promise<Client | null> {
    try {
      const clients = await this.getAllClients();
      return clients.find((client) => client.name === name) || null;
    } catch (error) {
      console.error(`Error fetching client by name ${name}:`, error);
      throw error;
    }
  }

  /**
   * Create new client
   */
  async createClient(clientData: CreateClientData): Promise<Client> {
    try {
      return await api.post<Client>(this.baseUrl, clientData);
    } catch (error) {
      console.error("Error creating client:", error);
      throw error;
    }
  }

  /**
   * Update client
   */
  async updateClient(
    id: string,
    clientData: UpdateClientData
  ): Promise<Client> {
    try {
      return await api.put<Client>(`${this.baseUrl}/${id}`, clientData);
    } catch (error) {
      console.error(`Error updating client ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete client (soft delete)
   */
  async deleteClient(id: string): Promise<void> {
    try {
      await api.delete<void>(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`Error deleting client ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get brand count for a client
   */
  async getClientBrandCount(clientId: string): Promise<number> {
    try {
      const response = await api.get<{ count: number }>(
        `/brands/client/${clientId}/count`
      );
      return response.count;
    } catch (error) {
      console.error(`Error getting brand count for client ${clientId}:`, error);
      throw error;
    }
  }

  /**
   * Validate client name uniqueness
   */
  async validateClientName(name: string, excludeId?: string): Promise<boolean> {
    try {
      const clients = await this.getAllClients();
      const existingClient = clients.find(
        (client) => client.name === name && client.id !== excludeId
      );
      return !existingClient;
    } catch (error) {
      console.error("Error validating client name:", error);
      throw error;
    }
  }
}

// Export singleton instance
export const clientApiService = new ClientApiService();
export default clientApiService;
