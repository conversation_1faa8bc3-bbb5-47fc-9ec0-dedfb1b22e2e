/* Loading container styles */
.loadingContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  /* background: linear-gradient(to bottom right, #eff6ff, #e0e7ff); */
  background: rgb(20, 79, 137);
}

.loadingContent {
  text-align: center;
}

.spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 3rem;
  width: 3rem;
  border-bottom: 2px solid rgb(20, 79, 137);
  margin: 0 auto;
}

.loadingText {
  margin-top: 1rem;
  color: #102a4f;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
