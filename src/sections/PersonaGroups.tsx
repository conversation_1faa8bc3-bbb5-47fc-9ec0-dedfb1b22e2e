import React, { useState, useId } from "react";
import {
  ChevronDown,
  ChevronUp,
  User,
  Users,
  X,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  Heart,
  Target,
  AlertCircle,
} from "lucide-react";
import { useScrollManager } from "../utils/scrollManager";

interface PersonaGroupsProps {
  filters: {
    category: string;
    stage: string;
    location: string;
    dateFrom?: string;
    dateTo?: string;
  };
  onPersonaSelect: (id: string) => void;
}

const personaGroups = [
  {
    id: "customers",
    title: "Primary Customers",
    description: "Core user base representing our main target audience",
    personas: [
      {
        id: "1",
        name: "<PERSON>",
        role: "Marketing Manager",
        category: "customer",
        stage: "implementation",
        location: "US",
        age: 32,
        experience: "8 years",
        email: "<EMAIL>",
        phone: "+****************",
        summary: "Tech-savvy professional focused on digital transformation",
        imageUrl:
          "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&q=80&w=200&h=200",
        details: {
          background:
            "<PERSON> is a seasoned marketing professional passionate about digital transformation.",
          goals: [
            "Streamline workflows",
            "Improve collaboration",
            "Increase ROI",
          ],
          painPoints: [
            "Time-consuming processes",
            "Limited budget",
            "Team communication",
          ],
          preferences: [
            "Data-driven decision making",
            "Visual project management tools",
            "Regular team check-ins",
            "Automated reporting systems",
          ],
        },
      },
      {
        id: "2",
        name: "<PERSON>",
        role: "Digital Nomad",
        category: "customer",
        stage: "research",
        location: "Europe",
        age: 28,
        experience: "5 years",
        email: "<EMAIL>",
        phone: "+****************",
        summary: "Remote professional combining work with travel",
        imageUrl:
          "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&q=80&w=200&h=200",
        details: {
          background:
            "Sarah is a remote professional who prioritizes flexibility and innovative solutions.",
          goals: [
            "Maintain work-life balance",
            "Optimize remote collaboration",
            "Expand professional network",
          ],
          painPoints: [
            "Internet connectivity",
            "Time zone coordination",
            "Remote team collaboration",
          ],
          preferences: [
            "Cloud-based tools",
            "Asynchronous communication",
            "Digital productivity apps",
            "Remote team culture",
          ],
        },
      },
    ],
  },
  {
    id: "business",
    title: "Business Owners",
    description: "Small to medium business owners using our platform",
    personas: [
      {
        id: "3",
        name: "Michael Chen",
        role: "Small Business Owner",
        category: "business owner",
        stage: "validation",
        location: "Asia",
        age: 45,
        experience: "15 years",
        email: "<EMAIL>",
        phone: "+****************",
        summary: "Experienced entrepreneur focused on scaling operations",
        imageUrl:
          "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&q=80&w=200&h=200",
        details: {
          background:
            "Michael runs multiple successful small businesses with a focus on scalability.",
          goals: [
            "Scale operations",
            "Optimize resources",
            "Improve efficiency",
          ],
          painPoints: [
            "Resource management",
            "Market competition",
            "Technology integration",
          ],
          preferences: [
            "Efficiency-focused solutions",
            "Cost-effective tools",
            "Simple user interfaces",
            "Mobile accessibility",
          ],
        },
      },
      {
        id: "4",
        name: "Emma Thompson",
        role: "E-commerce Owner",
        category: "business owner",
        stage: "implementation",
        location: "US",
        age: 35,
        experience: "10 years",
        email: "<EMAIL>",
        phone: "+****************",
        summary: "Digital retail expert specializing in online sales",
        imageUrl:
          "https://images.unsplash.com/photo-1534528741775-53994a69daeb?auto=format&fit=crop&q=80&w=200&h=200",
        details: {
          background:
            "Emma has built a successful online retail business focusing on customer experience.",
          goals: [
            "Increase online sales",
            "Optimize conversion rates",
            "Enhance customer service",
          ],
          painPoints: [
            "Inventory management",
            "Shipping logistics",
            "Customer retention",
          ],
          preferences: [
            "E-commerce platforms",
            "Analytics tools",
            "Customer service software",
            "Inventory systems",
          ],
        },
      },
    ],
  },
  {
    id: "stakeholders",
    title: "Key Stakeholders",
    description: "Decision makers and influential users",
    personas: [
      {
        id: "5",
        name: "James Wilson",
        role: "Product Director",
        category: "stakeholder",
        stage: "validation",
        location: "US",
        age: 42,
        experience: "18 years",
        email: "<EMAIL>",
        phone: "+****************",
        summary: "Strategic leader focused on product innovation",
        imageUrl:
          "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&q=80&w=200&h=200",
        details: {
          background:
            "James leads product strategy with a focus on innovation and market fit.",
          goals: [
            "Drive product innovation",
            "Increase market share",
            "Improve user satisfaction",
          ],
          painPoints: [
            "Feature prioritization",
            "Resource allocation",
            "Time to market",
          ],
          preferences: [
            "Data-driven decisions",
            "Agile methodologies",
            "Cross-functional collaboration",
            "Regular stakeholder updates",
          ],
        },
      },
      {
        id: "6",
        name: "Lisa Chen",
        role: "Innovation Lead",
        category: "stakeholder",
        stage: "research",
        location: "Asia",
        age: 38,
        experience: "12 years",
        email: "<EMAIL>",
        phone: "+****************",
        summary: "Innovation specialist driving digital transformation",
        imageUrl:
          "https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&q=80&w=200&h=200",
        details: {
          background:
            "Lisa specializes in digital transformation and emerging technologies.",
          goals: [
            "Lead innovation initiatives",
            "Implement new technologies",
            "Drive digital adoption",
          ],
          painPoints: [
            "Change management",
            "Technology integration",
            "User adoption",
          ],
          preferences: [
            "Innovation frameworks",
            "Emerging technologies",
            "Design thinking",
            "Rapid prototyping",
          ],
        },
      },
    ],
  },
];

export function PersonaGroups({
  filters,
  onPersonaSelect,
}: PersonaGroupsProps) {
  const [expandedGroups, setExpandedGroups] = useState<string[]>([]);
  const [selectedPersona, setSelectedPersona] = useState<any | null>(null);
  const modalId = useId(); // Generate unique ID for this modal instance
  
  // Use the scroll manager to preserve scroll position (PersonaGroups modal is detected as regular)
  useScrollManager(modalId, selectedPersona !== null, 'PersonaGroups');

  const toggleGroup = (groupId: string) => {
    setExpandedGroups((prev) =>
      prev.includes(groupId)
        ? prev.filter((id) => id !== groupId)
        : [...prev, groupId]
    );
  };

  const handlePersonaClick = (persona: any) => {
    setSelectedPersona(persona);
  };

  const filterPersonas = (personas: any[]) => {
    return personas.filter((persona) => {
      const categoryMatch =
        filters.category === "all" ||
        persona.category === filters.category.toLowerCase();
      const stageMatch =
        filters.stage === "all" ||
        persona.stage === filters.stage.toLowerCase();
      const locationMatch =
        filters.location === "all" || persona.location === filters.location;
      return categoryMatch && stageMatch && locationMatch;
    });
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold text-gray-900'>Persona Groups</h2>
          <p className='text-gray-500 mt-1'>
            Detailed user segments and insights
          </p>
        </div>
      </div>

      <div className='space-y-6'>
        {personaGroups.map((group) => {
          const filteredPersonas = filterPersonas(group.personas);
          if (filteredPersonas.length === 0) return null;

          return (
            <div
              key={group.id}
              className='bg-white rounded-xl shadow-sm overflow-hidden'
            >
              <button
                onClick={() => toggleGroup(group.id)}
                className='w-full px-6 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors'
              >
                <div className='flex items-center space-x-4'>
                  <div className='p-2 bg-blue-50 rounded-lg'>
                    <Users className='h-5 w-5 text-blue-600' />
                  </div>
                  <div className='text-left'>
                    <h3 className='text-lg font-semibold text-gray-900'>
                      {group.title}
                    </h3>
                    <p className='text-sm text-gray-500'>{group.description}</p>
                  </div>
                </div>
                {expandedGroups.includes(group.id) ? (
                  <ChevronUp className='h-5 w-5 text-gray-400' />
                ) : (
                  <ChevronDown className='h-5 w-5 text-gray-400' />
                )}
              </button>

              {expandedGroups.includes(group.id) && (
                <div className='border-t border-gray-100'>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4 p-6'>
                    {filteredPersonas.map((persona) => (
                      <div
                        key={persona.id}
                        onClick={() => handlePersonaClick(persona)}
                        className='group cursor-pointer bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-all duration-200'
                      >
                        <div className='flex items-start space-x-4'>
                          {persona.imageUrl ? (
                            <img
                              src={persona.imageUrl}
                              alt={persona.name}
                              className='w-16 h-16 rounded-lg object-cover'
                            />
                          ) : (
                            <div className='w-16 h-16 rounded-lg bg-gray-200 flex items-center justify-center'>
                              <User className='h-8 w-8 text-gray-400' />
                            </div>
                          )}
                          <div>
                            <h4 className='text-lg font-medium text-gray-900 group-hover:text-blue-600'>
                              {persona.name}
                            </h4>
                            <p className='text-sm text-gray-600'>
                              {persona.role}
                            </p>
                            <p className='text-sm text-gray-500 mt-1'>
                              {persona.summary}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Persona Detail Modal */}
      {selectedPersona && (
        <div className='modal-with-sidebar'>
          <div
            className='fixed inset-0 bg-black/75 backdrop-blur-sm'
            onClick={() => setSelectedPersona(null)}
          />
          <div className='fixed inset-0 overflow-y-auto'>
            <div className='flex min-h-full items-center justify-center p-4'>
              <div
                className='relative bg-white rounded-xl shadow-xl w-full max-w-4xl mx-4'
                onClick={(e) => e.stopPropagation()}
              >
                <button
                  onClick={() => setSelectedPersona(null)}
                  className='absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-lg transition-colors z-10'
                >
                  <X className='h-5 w-5 text-gray-500' />
                </button>

                <div className='p-8'>
                  <div className='flex items-start space-x-6'>
                    <img
                      src={selectedPersona.imageUrl}
                      alt={selectedPersona.name}
                      className='w-24 h-24 rounded-xl object-cover'
                    />
                    <div className='flex-1'>
                      <h3 className='text-2xl font-bold text-gray-900 mb-1'>
                        {selectedPersona.name}
                      </h3>
                      <p className='text-lg text-gray-600 mb-4'>
                        {selectedPersona.role}
                      </p>

                      <div className='grid grid-cols-2 gap-4 text-sm'>
                        <div className='flex items-center text-gray-600'>
                          <Mail className='h-4 w-4 mr-2' />
                          {selectedPersona.email}
                        </div>
                        <div className='flex items-center text-gray-600'>
                          <Phone className='h-4 w-4 mr-2' />
                          {selectedPersona.phone}
                        </div>
                        <div className='flex items-center text-gray-600'>
                          <MapPin className='h-4 w-4 mr-2' />
                          {selectedPersona.location}
                        </div>
                        <div className='flex items-center text-gray-600'>
                          <Briefcase className='h-4 w-4 mr-2' />
                          {selectedPersona.experience}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className='grid grid-cols-2 gap-8 mt-8'>
                    <div>
                      <div className='flex items-center mb-4'>
                        <Target className='h-5 w-5 text-blue-600 mr-2' />
                        <h4 className='text-lg font-semibold text-gray-900'>
                          Goals
                        </h4>
                      </div>
                      <ul className='space-y-2'>
                        {selectedPersona.details.goals.map(
                          (goal: string, index: number) => (
                            <li key={index} className='flex items-start'>
                              <div className='h-1.5 w-1.5 rounded-full bg-blue-600 mt-2 mr-2' />
                              <span className='text-gray-600'>{goal}</span>
                            </li>
                          )
                        )}
                      </ul>
                    </div>

                    <div>
                      <div className='flex items-center mb-4'>
                        <AlertCircle className='h-5 w-5 text-red-600 mr-2' />
                        <h4 className='text-lg font-semibold text-gray-900'>
                          Pain Points
                        </h4>
                      </div>
                      <ul className='space-y-2'>
                        {selectedPersona.details.painPoints.map(
                          (point: string, index: number) => (
                            <li key={index} className='flex items-start'>
                              <div className='h-1.5 w-1.5 rounded-full bg-red-600 mt-2 mr-2' />
                              <span className='text-gray-600'>{point}</span>
                            </li>
                          )
                        )}
                      </ul>
                    </div>

                    <div className='col-span-2'>
                      <div className='flex items-center mb-4'>
                        <Heart className='h-5 w-5 text-purple-600 mr-2' />
                        <h4 className='text-lg font-semibold text-gray-900'>
                          Preferences
                        </h4>
                      </div>
                      <ul className='space-y-2'>
                        {selectedPersona.details.preferences.map(
                          (pref: string, index: number) => (
                            <li key={index} className='flex items-start'>
                              <div className='h-1.5 w-1.5 rounded-full bg-purple-600 mt-2 mr-2' />
                              <span className='text-gray-600'>{pref}</span>
                            </li>
                          )
                        )}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
