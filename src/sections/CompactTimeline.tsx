import React from "react";
import { Check, Clock } from "lucide-react";

const timelineEvents = [
  {
    phase: "Research Planning",
    date: "2024-11-19",
    completed: true,
    description: "Initial research planning and participant selection",
  },
  {
    phase: "User Interviews",
    date: "2024-11-20",
    completed: true,
    description: "Conducted in-depth user interviews",
  },
  {
    phase: "Data Analysis",
    date: "2024-11-21",
    completed: true,
    description: "Analyzed interview data and identified patterns",
  },
  {
    phase: "Persona Creation",
    date: "2024-11-22",
    completed: true,
    description: "Developed initial persona profiles",
  },
  {
    phase: "Validation",
    date: "2024-11-23",
    completed: true,
    description: "Validated personas with stakeholders",
  },
  {
    phase: "Workshop",
    date: "2024-11-24",
    current: true,
    description: "Team workshop for final refinements",
  },
  {
    phase: "Implementation",
    date: "2024-11-25",
    upcoming: true,
    description: "Roll out personas across teams",
  },
];

export function CompactTimeline() {
  return (
    <section id="timeline" className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Research Timeline</h2>
        <p className="text-gray-500 mt-1">Project phases and milestones</p>
      </div>

      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="grid grid-cols-7 gap-4">
          {timelineEvents.map((event, index) => (
            <div key={index} className="relative">
              {/* Connector Line */}
              {index < timelineEvents.length - 1 && (
                <div className="absolute top-4 left-1/2 w-full h-0.5 bg-gray-200" />
              )}

              {/* Event Node */}
              <div className="relative flex flex-col items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center z-10
                  ${
                    event.current
                      ? "bg-blue-500 ring-4 ring-blue-100"
                      : event.completed
                      ? "bg-green-500"
                      : "bg-white border-2 border-gray-300"
                  }`}
                >
                  {event.completed ? (
                    <Check className="h-4 w-4 text-white" />
                  ) : event.current ? (
                    <Clock className="h-4 w-4 text-white" />
                  ) : null}
                </div>

                <div className="mt-4 text-center">
                  <h3 className="text-sm font-medium text-gray-900">
                    {event.phase}
                  </h3>
                  <p className="text-xs text-gray-500 mt-1">{event.date}</p>
                  <p className="text-xs text-gray-600 mt-2">
                    {event.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
