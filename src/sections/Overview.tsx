import React from 'react';
import { Users, BarChart2, Clock, Target } from 'lucide-react';

const stats = [
  {
    label: 'Interviews',
    value: '24/24',
    change: '+100%',
    icon: Users,
    color: 'blue'
  },
  {
    label: 'Goals Met',
    value: '8/10',
    change: '+80%',
    icon: Target,
    color: 'green'
  },
  {
    label: 'Days Left',
    value: '2',
    change: 'On Track',
    icon: Clock,
    color: 'purple'
  },
  {
    label: 'Insights',
    value: '47',
    change: '+12',
    icon: BarChart2,
    color: 'orange'
  }
];

export function Overview() {
  return (
    <section id="overview" className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Project Overview</h2>
        <p className="text-gray-500 mt-1">Current status and key metrics</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-white rounded-xl shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <div className={`p-2 bg-${stat.color}-50 rounded-lg`}>
                  <Icon className={`h-6 w-6 text-${stat.color}-600`} />
                </div>
                <span className="text-sm font-medium px-2.5 py-0.5 rounded-full bg-green-50 text-green-600">
                  {stat.change}
                </span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</h3>
              <p className="text-sm text-gray-500">{stat.label}</p>
            </div>
          );
        })}
      </div>
    </section>
  );
}