import React from 'react';
import { Lightbulb, TrendingUp, AlertCircle } from 'lucide-react';

const insights = [
  {
    category: 'Key Findings',
    icon: Lightbulb,
    color: 'blue',
    items: [
      'Users prioritize mobile-first experiences',
      'Integration capabilities are crucial',
      'Automated workflows are highly valued'
    ]
  },
  {
    category: 'Trends',
    icon: TrendingUp,
    color: 'green',
    items: [
      'Increasing demand for real-time analytics',
      'Growing focus on collaborative features',
      'Shift towards cloud-based solutions'
    ]
  },
  {
    category: 'Challenges',
    icon: AlertCircle,
    color: 'red',
    items: [
      'Complex onboarding processes',
      'Limited customization options',
      'Integration with legacy systems'
    ]
  }
];

export function ResearchInsights() {
  return (
    <section id="insights" className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Research Insights</h2>
        <p className="text-gray-500 mt-1">Key findings and observations</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {insights.map((category, index) => {
          const Icon = category.icon;
          return (
            <div key={index} className="bg-white rounded-xl shadow-sm p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className={`p-2 bg-${category.color}-50 rounded-lg`}>
                  <Icon className={`h-5 w-5 text-${category.color}-600`} />
                </div>
                <h3 className="font-semibold text-gray-900">{category.category}</h3>
              </div>
              <ul className="space-y-3">
                {category.items.map((item, itemIndex) => (
                  <li key={itemIndex} className="flex items-start">
                    <div className={`h-1.5 w-1.5 rounded-full bg-${category.color}-600 mt-2 mr-2`} />
                    <span className="text-sm text-gray-600">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          );
        })}
      </div>
    </section>
  );
}