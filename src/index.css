/* Import organized CSS files */
@import "./styles/index.css";
@import "./styles/components.css";
@import "./styles/neumorphic.css";
@import "./styles/tour.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Header Font Size Controls */
:root {
  --header-label-size: 1.125rem; /* "Name:" label size */
  --header-title-size: 1.125rem; /* "Project Roots" title size */
  --header-subtitle-size: 0.875rem; /* "Persona Immersion" subtitle size */
}

/* Carousel rotation animation */
.carousel-card {
  transition: transform 0.5s ease-in-out, opacity 0.3s ease;
  transform-origin: center center;
}

.carousel-card.rotating-right {
  transform: translateX(140%) scale(0.8);
}

.carousel-card.rotating-left {
  transform: translateX(-140%) scale(0.8);
}

.carousel-card.center {
  transform: translateX(0) scale(1);
}

.carousel-card.side {
  transform: scale(0.8);
  opacity: 0.85;
}

/* Smooth z-index transitions */
.carousel-container {
  perspective: 1000px;
}

/* Enhanced slider styles for better touch and horizontal scrolling */
/* 
 * These styles enable smooth horizontal scrolling gestures on mobile devices
 * and improve the overall touch experience for the persona carousel
 */
.slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -khtml-user-select: none;
  -ms-touch-action: pan-x; /* Allow horizontal pan gestures */
  touch-action: pan-x; /* Enable horizontal scrolling/swiping */
  -webkit-tap-highlight-color: transparent;
}

.slick-list {
  position: relative;
  display: block;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* Enhanced touch support */
.slick-slider .slick-track,
.slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.slick-slide.slick-loading img {
  display: none;
}

.slick-slide.dragging img {
  pointer-events: none;
}

.slick-initialized .slick-slide {
  display: block;
}

/* Add padding to slick-slide to prevent hover scaling clipping */
/* This ensures cards can scale up by 1.02 without being clipped */
.slick-slide {
  padding: 1rem 0;
}

/* Improve mobile touch scrolling */
@media (max-width: 768px) {
  .slick-slider {
    -ms-touch-action: pan-x;
    touch-action: pan-x;
  }

  .slick-list {
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
  }

  .slick-list::-webkit-scrollbar {
    display: none; /* WebKit */
  }
}

/* Custom arrow styles */
.slick-prev,
.slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  width: 40px;
  height: 40px;
  padding: 0;
  -webkit-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  transform: translate(0, -50%);
  cursor: pointer;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  transition: all 0.2s ease;
}

.slick-prev:hover,
.slick-next:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.slick-prev {
  left: -20px;
}

.slick-next {
  right: -20px;
}

.slick-prev:before,
.slick-next:before {
  font-family: "slick";
  font-size: 16px;
  line-height: 1;
  opacity: 1;
  color: #6b7280;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.slick-prev:before {
  content: "←";
}

.slick-next:before {
  content: "→";
}

/* Dots styling */
.slick-dots {
  position: absolute;
  bottom: -45px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.slick-dots li {
  position: relative;
  display: inline-block;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  width: 12px;
  height: 12px;
  padding: 0;
  cursor: pointer;
  background: #d1d5db;
  border: 0;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.slick-dots li.slick-active button {
  background: #3b82f6;
  transform: scale(1.2);
}

.slick-dots li button:hover {
  background: #9ca3af;
}

.slick-dots li.slick-active button:hover {
  background: #2563eb;
}
