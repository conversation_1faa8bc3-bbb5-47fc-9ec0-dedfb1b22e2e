import React from 'react';

// Simple localStorage-based overlay state management
const OVERLAY_STORAGE_KEY = 'isOverlayOpen';

export const setOverlayOpen = (isOpen: boolean) => {
  localStorage.setItem(OVERLAY_STORAGE_KEY, JSON.stringify(isOpen));
  
  // Add/remove CSS class to body to disable header/sidebar buttons
  if (isOpen) {
    document.body.classList.add('overlay-active');
  } else {
    document.body.classList.remove('overlay-active');
  }
};

export const isOverlayOpen = (): boolean => {
  try {
    const value = localStorage.getItem(OVERLAY_STORAGE_KEY);
    return value ? JSON.parse(value) : false;
  } catch (error) {
    console.warn('Error reading overlay state from localStorage:', error);
    return false;
  }
};

// Hook for modals and tooltips to use
export const useOverlayState = (isOpen: boolean) => {
  React.useEffect(() => {
    setOverlayOpen(isOpen);
    
    // Cleanup on unmount
    return () => {
      setOverlayOpen(false);
    };
  }, [isOpen]);
};
