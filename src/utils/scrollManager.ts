import React from 'react';

// Scroll position management utility for modals
// This utility automatically detects modal types and applies appropriate scroll behavior

interface ScrollState {
  scrollX: number;
  scrollY: number;
}

interface ModalInfo {
  id: string;
  type: 'regular' | 'tooltip';
  componentName?: string;
}

class ScrollManager {
  private scrollStates: Map<string, ScrollState> = new Map();
  private activeModals: Map<string, ModalInfo> = new Map();
  private originalBodyStyle: string | null = null;
  private originalScrollPosition: ScrollState | null = null;

  /**
   * Register a modal and preserve current scroll position
   * @param modalId - Unique identifier for the modal
   * @param componentName - Name of the component for type detection
   */
  registerModal(modalId: string, componentName?: string): void {
    // Determine modal type
    const modalType = this.detectModalType(componentName);
    
    // Store current scroll position
    const currentScroll: ScrollState = {
      scrollX: window.scrollX,
      scrollY: window.scrollY,
    };
    
    this.scrollStates.set(modalId, currentScroll);
    this.activeModals.set(modalId, { id: modalId, type: modalType, componentName });

    // Only prevent body scroll for regular modals
    if (modalType === 'regular') {
      this.preventBodyScroll(currentScroll);
    }
  }

  /**
   * Unregister a modal and restore scroll position if no other modals are active
   * @param modalId - Unique identifier for the modal
   */
  unregisterModal(modalId: string): void {
    const modalInfo = this.activeModals.get(modalId);
    this.activeModals.delete(modalId);
    this.scrollStates.delete(modalId);

    // If no more modals are active, restore body scroll
    if (this.activeModals.size === 0) {
      this.restoreBodyScroll();
    }
  }

  /**
   * Get the scroll position for a specific modal
   * @param modalId - Unique identifier for the modal
   * @returns ScrollState or null if not found
   */
  getScrollPosition(modalId: string): ScrollState | null {
    return this.scrollStates.get(modalId) || null;
  }

  /**
   * Detect modal type based on component name
   * @param componentName - Name of the component
   * @returns Modal type
   */
  private detectModalType(componentName?: string): 'regular' | 'tooltip' {
    if (!componentName) return 'regular';
    
    // Tooltip modals that should allow background scrolling
    const tooltipComponents = ['TourGuide'];
    
    return tooltipComponents.includes(componentName) ? 'tooltip' : 'regular';
  }

  /**
   * Check if a modal is a tooltip type
   * @param modalId - Unique identifier for the modal
   * @returns True if tooltip modal
   */
  isTooltipModal(modalId: string): boolean {
    const modalInfo = this.activeModals.get(modalId);
    return modalInfo?.type === 'tooltip';
  }

  /**
   * Prevent body scrolling while preserving scroll position (for regular modals only)
   */
  private preventBodyScroll(scrollPosition: ScrollState): void {
    if (this.originalBodyStyle === null) {
      // Store original body style and scroll position
      this.originalBodyStyle = document.body.style.cssText;
      this.originalScrollPosition = scrollPosition;
    }

    // Set body styles to prevent scrolling while maintaining position
    document.body.style.position = 'fixed';
    document.body.style.top = `-${scrollPosition.scrollY}px`;
    document.body.style.left = '0';
    document.body.style.right = '0';
    document.body.style.overflow = 'hidden';
  }

  /**
   * Restore body scrolling and scroll position
   */
  private restoreBodyScroll(): void {
    if (this.originalBodyStyle !== null) {
      // Restore original body styles
      document.body.style.cssText = this.originalBodyStyle;
      this.originalBodyStyle = null;
    } else {
      // Fallback: just remove the fixed positioning
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.left = '';
      document.body.style.right = '';
      document.body.style.overflow = '';
    }

    // Restore scroll position to the original position when first modal opened
    if (this.originalScrollPosition) {
      window.scrollTo(this.originalScrollPosition.scrollX, this.originalScrollPosition.scrollY);
      this.originalScrollPosition = null;
    }
  }

  /**
   * Get the number of active modals
   */
  getActiveModalCount(): number {
    return this.activeModals.size;
  }

  /**
   * Check if any modals are currently active
   */
  hasActiveModals(): boolean {
    return this.activeModals.size > 0;
  }

  /**
   * Check if any regular modals are active (that prevent scrolling)
   */
  hasActiveRegularModals(): boolean {
    return Array.from(this.activeModals.values()).some(modal => modal.type === 'regular');
  }

  /**
   * Clear all modal states (useful for cleanup)
   */
  clearAll(): void {
    this.scrollStates.clear();
    this.activeModals.clear();
    this.restoreBodyScroll();
  }
}

// Create a singleton instance
const scrollManager = new ScrollManager();

// React hook for managing scroll in components
export const useScrollManager = (modalId: string, isOpen: boolean, componentName?: string) => {
  React.useEffect(() => {
    if (isOpen) {
      scrollManager.registerModal(modalId, componentName);
    } else {
      scrollManager.unregisterModal(modalId);
    }

    // Cleanup on unmount
    return () => {
      scrollManager.unregisterModal(modalId);
    };
  }, [modalId, isOpen, componentName]);
};

// Export the singleton for direct use
export { scrollManager };



