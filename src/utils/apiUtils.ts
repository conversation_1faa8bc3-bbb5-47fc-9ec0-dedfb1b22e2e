import { ApiError } from "../services/apiClient";
import { API_CONFIG } from "../config/apiConfig";

/**
 * Format API error for display
 */
export function formatApiError(error: ApiError): string {
  if (error.errors && error.errors.length > 0) {
    return error.errors.map((err) => `${err.field}: ${err.message}`).join(", ");
  }
  return error.message || API_CONFIG.ERRORS.SERVER_ERROR;
}

/**
 * Check if error is a network error
 */
export function isNetworkError(error: ApiError): boolean {
  return !error.status || error.status === 0;
}

/**
 * Check if error is a validation error
 */
export function isValidationError(error: ApiError): boolean {
  return error.status === 400 && error.errors && error.errors.length > 0;
}

/**
 * Check if error is a not found error
 */
export function isNotFoundError(error: ApiError): boolean {
  return error.status === 404;
}

/**
 * Check if error is an unauthorized error
 */
export function isUnauthorizedError(error: ApiError): boolean {
  return error.status === 401;
}

/**
 * Check if error is a forbidden error
 */
export function isForbiddenError(error: ApiError): boolean {
  return error.status === 403;
}

/**
 * Get appropriate error message based on error type
 */
export function getErrorMessage(error: ApiError): string {
  if (isNetworkError(error)) {
    return API_CONFIG.ERRORS.NETWORK_ERROR;
  }

  if (isValidationError(error)) {
    return formatApiError(error);
  }

  if (isNotFoundError(error)) {
    return API_CONFIG.ERRORS.NOT_FOUND;
  }

  if (isUnauthorizedError(error)) {
    return API_CONFIG.ERRORS.UNAUTHORIZED;
  }

  if (isForbiddenError(error)) {
    return API_CONFIG.ERRORS.FORBIDDEN;
  }

  return error.message || API_CONFIG.ERRORS.SERVER_ERROR;
}

/**
 * Retry API call with exponential backoff
 */
export async function retryApiCall<T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error as Error;

      if (attempt === maxRetries) {
        throw lastError;
      }

      // Don't retry on client errors (4xx)
      if (error && typeof error === "object" && "status" in error) {
        const apiError = error as ApiError;
        if (
          apiError.status &&
          apiError.status >= 400 &&
          apiError.status < 500
        ) {
          throw lastError;
        }
      }

      // Wait before retrying
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

/**
 * Debounce API calls
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle API calls
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Create a cancellable API call
 */
export function createCancellableApiCall<T>(apiCall: () => Promise<T>): {
  execute: () => Promise<T>;
  cancel: () => void;
} {
  let abortController: AbortController | null = null;

  return {
    execute: async () => {
      // Cancel previous request if it exists
      if (abortController) {
        abortController.abort();
      }

      abortController = new AbortController();

      try {
        return await apiCall();
      } finally {
        abortController = null;
      }
    },
    cancel: () => {
      if (abortController) {
        abortController.abort();
        abortController = null;
      }
    },
  };
}

/**
 * Validate API response structure
 */
export function validateApiResponse(response: any): boolean {
  return (
    response &&
    typeof response === "object" &&
    "success" in response &&
    typeof response.success === "boolean"
  );
}

/**
 * Extract data from API response
 */
export function extractApiData<T>(response: any): T | null {
  if (validateApiResponse(response) && response.success) {
    return response.data || null;
  }
  return null;
}
