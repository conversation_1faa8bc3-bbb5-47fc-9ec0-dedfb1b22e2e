/**
 * Check if user is authenticated as admin
 */
export function isAdminAuthenticated(): boolean {
  return sessionStorage.getItem("adminAuthenticated") === "true";
}

/**
 * Get the admin PIN from session storage
 */
export function getAdminPin(): string | null {
  return sessionStorage.getItem("adminPin");
}

/**
 * Set admin authentication
 */
export function setAdminAuthenticated(pin: string): void {
  sessionStorage.setItem("adminAuthenticated", "true");
  sessionStorage.setItem("adminPin", pin);
}

/**
 * Clear admin authentication
 */
export function clearAdminAuthentication(): void {
  sessionStorage.removeItem("adminAuthenticated");
  sessionStorage.removeItem("adminPin");
}

/**
 * Check if admin authentication has expired (optional)
 * You can implement session timeout logic here
 */
export function isAdminSessionValid(): boolean {
  // For now, just check if authenticated
  // You could add timestamp-based expiration here
  return isAdminAuthenticated();
}

/**
 * Require admin authentication - redirect if not authenticated
 */
export function requireAdminAuth(): boolean {
  if (!isAdminAuthenticated()) {
    // Redirect to admin login
    window.location.href = "/admin";
    return false;
  }
  return true;
}
