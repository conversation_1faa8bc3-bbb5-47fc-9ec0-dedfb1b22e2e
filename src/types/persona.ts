export type PersonaCategory =
  | "Ambitious"
  | "Architect"
  | "Contented"
  | "Sophisticate"
  | "Overcomer"
  | "Traditionalist"
  | "Minimalist"
  | "Over-Achiever"
  | "Curious"
  | "Creative";

export interface PersonaTraits {
  characteristics: string[];
  personality: string[];
  motivations: string[];
  behaviors: string[];
}

export interface UserDetails {
  segment: string;
  demographics: string;
  household: string;
  lifestyle: string;
  celebrations: string[];
}

export interface HolidayPreferences {
  decoratingStyle: string;
  decoratingPhilosophy: string;
  inspirationSources: string[];
  decorationStorage: string;
  decoratingResponsibilities: string;
  spendingHabits: {
    currentYear: string;
    previousYear: string;
    philosophy: string;
  };
  shoppingPreferences: {
    stores: string[];
    timing: string;
    decisionMaking: string;
  };
}

export interface Persona {
  id: string;
  name: string;
  title: string;
  location: string;
  age: number;
  experience: string;
  email: string;
  phone: string;
  profileImageUrl?: string; // Unsplash URL (nullable)
  profileImageName?: string; // Local image filename (nullable)
  background: string;
  goals: string[];
  painPoints: string[];
  preferences: string[];
  category: PersonaCategory;
  personaTraits: PersonaTraits;
  userDetails: UserDetails;
  holidayPreferences: HolidayPreferences;
}
