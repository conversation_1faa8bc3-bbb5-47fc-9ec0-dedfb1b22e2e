// Slider configuration for PersonaList component
import React from "react";
import { ArrowLeft, ArrowRight } from "lucide-react";
import styles from "../pages/PersonaList.module.css";

// Custom arrow components for All Personas slider
export function CustomPrevArrow(props: any) {
  const { className, style, onClick } = props;
  return (
    <button
      type='button'
      onClick={onClick}
      className={`${styles.customArrow} ${styles.customArrowPrev}`}
      style={{ ...style, display: "block" }}
      aria-label='Previous Persona'
    >
      <ArrowLeft className={styles.arrowIcon} />
    </button>
  );
}

export function CustomNextArrow(props: any) {
  const { className, style, onClick } = props;
  return (
    <button
      type='button'
      onClick={onClick}
      className={`${styles.customArrow} ${styles.customArrowNext}`}
      style={{ ...style, display: "block" }}
      aria-label='Next Persona'
    >
      <ArrowRight className={styles.arrowIcon} />
    </button>
  );
}

// Slider settings configuration
export const sliderSettings = {
  dots: true,
  infinite: true,
  speed: 500,
  slidesToShow: 3, // Show 3 cards at a time
  slidesToScroll: 1,
  swipeToSlide: true, // Enable swipe to slide
  touchMove: true, // Enable touch move
  swipe: true, // Enable swipe
  draggable: true, // Enable dragging
  touchThreshold: 10, // Sensitivity for touch gestures
  arrows: true, // Show navigation arrows
  adaptiveHeight: false,
  centerMode: false,
  variableWidth: false,
  prevArrow: <CustomPrevArrow />,
  nextArrow: <CustomNextArrow />,
  appendDots: (dots: React.ReactNode) => (
    <div className={styles.sliderDots}>{dots}</div>
  ),
  customPaging: (i: number) => (
    <button
      className={styles.sliderDot}
      aria-label={`Go to persona ${i + 1}`}
      type='button'
    />
  ),
  responsive: [
    {
      breakpoint: 1024, // For tablets
      settings: {
        slidesToShow: 2,
        swipeToSlide: true,
        touchMove: true,
        swipe: true,
        draggable: true,
      },
    },
    {
      breakpoint: 600, // For mobile devices
      settings: {
        slidesToShow: 1,
        swipeToSlide: true,
        touchMove: true,
        swipe: true,
        draggable: true,
        arrows: false, // Hide arrows on mobile for cleaner UI
      },
    },
  ],
};
