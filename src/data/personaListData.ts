// PersonaList page data and configuration
import { mockPersonas, CATEGORY_DESCRIPTIONS } from "./mockPersonas";
import ambitious from "../assets/Ambitious.jpeg";
import architect from "../assets/Architect.jpeg";
import contented from "../assets/Contented.jpeg";
import sophisticate from "../assets/Sophisticate.jpeg";
import overcomer from "../assets/Overcomer.jpeg";

// Convert mockPersonas to the format expected by PersonaCard
export const PERSONAS_FOR_CARDS = Object.values(mockPersonas).map((persona) => ({
  id: persona.id,
  name: persona.name,
  category: persona.category,
  title: persona.title,
  summary: persona.background, // Use background as summary
  profileImageUrl: persona.profileImageUrl,
  profileImageName: persona.profileImageName,
  description: CATEGORY_DESCRIPTIONS[persona.category]?.description || "",
}));

// Persona categories for filtering
export const categories = [
  "All",
  "Ambitious",
  "Architect",
  "Contented",
  "Sophisticate",
  "Overcomer",
  "Traditionalist",
  "Minimalist",
  "Over-Achiever",
  "Curious",
  "Creative",
];

// Tips data for the tips section
export const tips = [
  {
    title: "What is a persona?",
    content:
      "A persona is a fictional character created based on user research to represent different user types.",
  },
  {
    title: "Why use personas?",
    content:
      "Personas help teams understand user needs, experiences, behaviors, and goals.",
  },
  {
    title: "How are personas created?",
    content:
      "Personas are created from qualitative and quantitative user research.",
  },
  {
    title: "Personas in design",
    content: "Personas guide design decisions by keeping real users in mind.",
  },
  {
    title: "Personas in communication",
    content:
      "Personas help align teams on who the users are and what they need.",
  },
  {
    title: "Personas and empathy",
    content:
      "Personas foster empathy for users throughout the product development process.",
  },
  {
    title: "Personas and business",
    content:
      "Personas help prioritize features and improvements that matter most to users.",
  },
];

// Persona overview data using actual category descriptions
export const personaOverviews = [
  {
    title: "Ambitious",
    description: CATEGORY_DESCRIPTIONS.Ambitious.description,
    image: ambitious,
  },
  {
    title: "Architect",
    description: CATEGORY_DESCRIPTIONS.Architect.description,
    image: architect,
  },
  {
    title: "Contented",
    description: CATEGORY_DESCRIPTIONS.Contented.description,
    image: contented,
  },
  {
    title: "Sophisticate",
    description: CATEGORY_DESCRIPTIONS.Sophisticate.description,
    image: sophisticate,
  },
  {
    title: "Overcomer",
    description: CATEGORY_DESCRIPTIONS.Overcomer.description,
    image: overcomer,
  },
  {
    title: "Traditionalist",
    description: CATEGORY_DESCRIPTIONS.Traditionalist.description,
    image: ambitious, // Using existing image as placeholder
  },
  {
    title: "Minimalist",
    description: CATEGORY_DESCRIPTIONS.Minimalist.description,
    image: contented, // Using existing image as placeholder
  },
  {
    title: "Over-Achiever",
    description: CATEGORY_DESCRIPTIONS["Over-Achiever"].description,
    image: sophisticate, // Using existing image as placeholder
  },
  {
    title: "Curious",
    description: CATEGORY_DESCRIPTIONS.Curious.description,
    image: architect, // Using existing image as placeholder
  },
  {
    title: "Creative",
    description: CATEGORY_DESCRIPTIONS.Creative.description,
    image: overcomer, // Using existing image as placeholder
  },
];

// Utility functions for PersonaList component
export const getTitle = (selectedCategory: string) => {
  if (selectedCategory === "All") {
    return "All Personas";
  }
  return `${selectedCategory} Personas`;
};

export const getSubtitle = (selectedCategory: string) => {
  if (selectedCategory === "All") {
    return "";
  }
  return `Key insights for ${selectedCategory.toLowerCase()} personas`;
};

export const getCardTitle = (persona: (typeof PERSONAS_FOR_CARDS)[0]) => {
  return `${persona.name} - ${persona.category}`;
};

export const getCardSubtitle = (persona: (typeof PERSONAS_FOR_CARDS)[0]) => {
  return persona.title;
};

// Filter personas by category
export const getFilteredPersonas = (selectedCategory: string) => {
  return PERSONAS_FOR_CARDS.filter((persona) =>
    selectedCategory === "All" ? true : persona.category === selectedCategory
  );
};
