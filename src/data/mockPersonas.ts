import { Persona } from "../types/persona";

export const CATEGORY_STYLES = {
  Ambitious: {
    // Strong orange/amber colors for ambitious, energetic personality
    border: "border-orange-500",
    borderHover: "hover:border-orange-600",
    containerBg: "bg-orange-100",
    containerBorder: "border-orange-200",
    textColor: "text-orange-800",
    badgeBg: "bg-orange-500",
    badgeText: "text-white",
    gradient: "from-orange-400 to-amber-600",
    accent: "text-orange-600",
    highlight: "bg-orange-50",
  },
  Architect: {
    // Strong blue/indigo colors for analytical, strategic personality
    border: "border-blue-600",
    borderHover: "hover:border-blue-700",
    containerBg: "bg-blue-100",
    containerBorder: "border-blue-200",
    textColor: "text-blue-800",
    badgeBg: "bg-blue-600",
    badgeText: "text-white",
    gradient: "from-blue-400 to-indigo-600",
    accent: "text-blue-600",
    highlight: "bg-blue-50",
  },
  Contented: {
    // Mild green/sage colors for balanced, content personality
    border: "border-green-400",
    borderHover: "hover:border-green-500",
    containerBg: "bg-green-50",
    containerBorder: "border-green-100",
    textColor: "text-green-700",
    badgeBg: "bg-green-400",
    badgeText: "text-white",
    gradient: "from-green-400 to-emerald-600",
    accent: "text-green-600",
    highlight: "bg-green-50",
  },
  Sophisticate: {
    // Strong purple/violet colors for visionary, innovative personality
    border: "border-purple-600",
    borderHover: "hover:border-purple-700",
    containerBg: "bg-purple-100",
    containerBorder: "border-purple-200",
    textColor: "text-purple-800",
    badgeBg: "bg-purple-600",
    badgeText: "text-white",
    gradient: "from-purple-400 to-violet-600",
    accent: "text-purple-600",
    highlight: "bg-purple-50",
  },
  Overcomer: {
    // Moderate teal/cyan colors for creative, empathetic personality
    border: "border-teal-500",
    borderHover: "hover:border-teal-600",
    containerBg: "bg-teal-100",
    containerBorder: "border-teal-200",
    textColor: "text-teal-800",
    badgeBg: "bg-teal-500",
    badgeText: "text-white",
    gradient: "from-teal-400 to-cyan-600",
    accent: "text-teal-600",
    highlight: "bg-teal-50",
  },
  Traditionalist: {
    // Strong brown/amber colors for conservative, value-driven personality
    border: "border-amber-700",
    borderHover: "hover:border-amber-800",
    containerBg: "bg-amber-100",
    containerBorder: "border-amber-200",
    textColor: "text-amber-800",
    badgeBg: "bg-amber-700",
    badgeText: "text-white",
    gradient: "from-amber-500 to-yellow-700",
    accent: "text-amber-700",
    highlight: "bg-amber-50",
  },
  Minimalist: {
    // Mild gray/slate colors for simple, practical personality
    border: "border-slate-400",
    borderHover: "hover:border-slate-500",
    containerBg: "bg-slate-50",
    containerBorder: "border-slate-100",
    textColor: "text-slate-700",
    badgeBg: "bg-slate-400",
    badgeText: "text-white",
    gradient: "from-slate-300 to-gray-500",
    accent: "text-slate-600",
    highlight: "bg-slate-50",
  },
  "Over-Achiever": {
    // Strong red/crimson colors for competitive, success-driven personality
    border: "border-red-600",
    borderHover: "hover:border-red-700",
    containerBg: "bg-red-100",
    containerBorder: "border-red-200",
    textColor: "text-red-800",
    badgeBg: "bg-red-600",
    badgeText: "text-white",
    gradient: "from-red-400 to-crimson-600",
    accent: "text-red-600",
    highlight: "bg-red-50",
  },
  Curious: {
    // Moderate yellow/amber colors for inquisitive, learning-focused personality
    border: "border-yellow-500",
    borderHover: "hover:border-yellow-600",
    containerBg: "bg-yellow-100",
    containerBorder: "border-yellow-200",
    textColor: "text-yellow-800",
    badgeBg: "bg-yellow-500",
    badgeText: "text-white",
    gradient: "from-yellow-400 to-amber-500",
    accent: "text-yellow-600",
    highlight: "bg-yellow-50",
  },
  Creative: {
    // Strong magenta/pink colors for artistic, innovative personality
    border: "border-pink-600",
    borderHover: "hover:border-pink-700",
    containerBg: "bg-pink-100",
    containerBorder: "border-pink-200",
    textColor: "text-pink-800",
    badgeBg: "bg-pink-600",
    badgeText: "text-white",
    gradient: "from-pink-400 to-rose-600",
    accent: "text-pink-600",
    highlight: "bg-pink-50",
  },
} as const;

// Category descriptions for each persona type
export const CATEGORY_DESCRIPTIONS = {
  Ambitious: {
    title: "Ambitious",
    description:
      "Confident, decisive, and not afraid to take risks. Enjoys being a leader and inspiring others. Ambitious personas are often the first to step forward in challenging situations, inspiring those around them with their courage and determination. They thrive in environments that reward initiative and are not afraid to voice their opinions, even when it means standing alone.",
    characteristics: [
      "All-In",
      "Positive",
      "More is More",
      "Aspiring Showcaser",
      "Extra",
    ],
    personality: [
      "Extrovert",
      "Go Big",
      "Leave Nothing Undone",
      "Generous",
      "Upbeat",
      "Big Heart",
      "Dream Big",
    ],
    motivations: [
      "Recognition",
      "Compliments",
      "Pleasing Others",
      "Full Life",
      "Achieving",
      "Productivity",
    ],
    behaviors: [
      "Live Life to the Fullest",
      "Social",
      "Seek Inspiration",
      "High Social Media User",
      "Spontaneous",
    ],
  },
  Architect: {
    title: "Architect",
    description:
      "Strategic, analytical, and pragmatic problem-solvers who value efficiency and systematic approaches. Architect personas approach challenges with a critical eye and a methodical mindset. They are skilled at breaking down complex issues and are often the go-to people for data-driven decisions and in-depth analysis. Their preference for proven methods makes them invaluable when consistency and dependability are required.",
    characteristics: [
      "Strategic",
      "Analytical",
      "Pragmatic",
      "Detail-Oriented",
      "Planner",
    ],
    personality: [
      "Introvert",
      "Logical",
      "Methodical",
      "Focused",
      "Independent",
      "Problem Solver",
    ],
    motivations: [
      "Efficiency",
      "Optimization",
      "Growth",
      "Innovation",
      "Success",
    ],
    behaviors: [
      "Research-Oriented",
      "Data-Driven",
      "Systematic",
      "Goal-Oriented",
      "Continuous Learner",
    ],
  },
  Contented: {
    title: "Contented",
    description:
      "Flexible, adaptable, and values work-life balance. Contented personas are driven by curiosity and a desire for new experiences. They are quick to embrace change and are often the ones encouraging others to step outside their comfort zones. Their enthusiasm for discovery makes them natural explorers and innovators who prioritize personal growth and meaningful experiences.",
    characteristics: [
      "Flexible",
      "Adventurous",
      "Independent",
      "Tech-Savvy",
      "Curious",
    ],
    personality: [
      "Introvert",
      "Adaptable",
      "Innovative",
      "Self-Motivated",
      "Resourceful",
    ],
    motivations: [
      "Freedom",
      "Exploration",
      "Learning",
      "Work-Life Balance",
      "Personal Growth",
    ],
    behaviors: [
      "Remote Work",
      "Travel",
      "Continuous Learning",
      "Networking",
      "Tech Enthusiast",
    ],
  },
  Sophisticate: {
    title: "Sophisticate",
    description:
      "Innovative, visionary, and charismatic leaders who drive change and inspire others. Sophisticate personas are always looking for new ways to solve problems and express ideas. They are not afraid to challenge conventions and are often the source of innovative solutions and fresh perspectives within a team or organization. Their creative approach and strategic thinking make them natural leaders.",
    characteristics: [
      "Innovative",
      "Visionary",
      "Risk-Taker",
      "Leader",
      "Ambitious",
    ],
    personality: [
      "Extrovert",
      "Charismatic",
      "Driven",
      "Creative",
      "Strategic",
    ],
    motivations: ["Success", "Growth", "Innovation", "Leadership", "Impact"],
    behaviors: [
      "Networking",
      "Mentoring",
      "Continuous Learning",
      "Team Building",
      "Pitching",
    ],
  },
  Overcomer: {
    title: "Overcomer",
    description:
      "Creative, empathetic, and detail-oriented professionals who focus on user-centered solutions. Overcomer personas take the time to reflect before acting and are deeply empathetic to the needs of those around them. They excel in roles that require patience and understanding, and their insights often help teams avoid pitfalls and make well-informed decisions. Their user-focused approach drives meaningful innovation.",
    characteristics: [
      "Creative",
      "Empathetic",
      "Detail-Oriented",
      "User-Focused",
      "Innovative",
    ],
    personality: [
      "Introvert",
      "Thoughtful",
      "Curious",
      "Analytical",
      "Collaborative",
    ],
    motivations: [
      "User Satisfaction",
      "Innovation",
      "Creativity",
      "Problem Solving",
      "Collaboration",
    ],
    behaviors: [
      "User Research",
      "Prototyping",
      "Design Thinking",
      "Collaboration",
      "Continuous Learning",
    ],
  },
  Traditionalist: {
    title: "Traditionalist",
    description:
      "Conservative, value-driven, and family-oriented individuals who prioritize tradition and stability. Traditionalist personas find comfort in familiar routines and established customs. They are often the keepers of family traditions and prefer proven methods over new trends. Their approach to life is methodical and they value quality, durability, and timeless design over fleeting fashions.",
    characteristics: [
      "Conservative",
      "Value-Driven",
      "Family-Oriented",
      "Stable",
      "Traditional",
    ],
    personality: [
      "Reliable",
      "Patient",
      "Loyal",
      "Practical",
      "Respectful",
      "Steady",
    ],
    motivations: [
      "Family Security",
      "Tradition",
      "Stability",
      "Quality",
      "Heritage",
    ],
    behaviors: [
      "Maintain Traditions",
      "Plan Ahead",
      "Invest in Quality",
      "Family-Focused",
      "Conservative Choices",
    ],
  },
  Minimalist: {
    title: "Minimalist",
    description:
      "Simple, practical, and quality-over-quantity focused individuals who value intentional living. Minimalist personas prefer clean, uncluttered spaces and meaningful experiences over material possessions. They are thoughtful about their purchases and prioritize functionality and sustainability. Their approach to life is deliberate and they find beauty in simplicity and purposeful design.",
    characteristics: [
      "Simple",
      "Practical",
      "Intentional",
      "Quality-Focused",
      "Sustainable",
    ],
    personality: [
      "Thoughtful",
      "Organized",
      "Mindful",
      "Efficient",
      "Independent",
      "Calm",
    ],
    motivations: [
      "Simplicity",
      "Quality",
      "Sustainability",
      "Peace of Mind",
      "Intentional Living",
    ],
    behaviors: [
      "Declutter Regularly",
      "Research Purchases",
      "Choose Quality",
      "Sustainable Choices",
      "Mindful Consumption",
    ],
  },
  "Over-Achiever": {
    title: "Over-Achiever",
    description:
      "Goal-oriented, competitive, and success-driven individuals who constantly push their limits. Over-Achiever personas are highly motivated by accomplishment and recognition. They set ambitious goals and work tirelessly to exceed expectations. Their competitive nature drives them to be the best in everything they do, and they thrive in challenging environments that reward excellence and performance.",
    characteristics: [
      "Goal-Oriented",
      "Competitive",
      "Success-Driven",
      "Ambitious",
      "Perfectionist",
    ],
    personality: [
      "Driven",
      "Focused",
      "Determined",
      "Confident",
      "Results-Oriented",
      "High Standards",
    ],
    motivations: [
      "Achievement",
      "Recognition",
      "Success",
      "Excellence",
      "Competition",
    ],
    behaviors: [
      "Set High Goals",
      "Work Hard",
      "Seek Recognition",
      "Compete",
      "Exceed Expectations",
    ],
  },
  Curious: {
    title: "Curious",
    description:
      "Inquisitive, learning-focused, and exploration-driven individuals who are always seeking new knowledge and experiences. Curious personas have a natural thirst for understanding and are constantly asking questions and exploring new ideas. They are open-minded and enjoy discovering new perspectives, technologies, and ways of doing things. Their enthusiasm for learning makes them natural researchers and innovators.",
    characteristics: [
      "Inquisitive",
      "Learning-Focused",
      "Exploration-Driven",
      "Open-Minded",
      "Knowledge-Seeking",
    ],
    personality: [
      "Curious",
      "Enthusiastic",
      "Adaptable",
      "Analytical",
      "Innovative",
      "Eager to Learn",
    ],
    motivations: [
      "Knowledge",
      "Discovery",
      "Understanding",
      "Innovation",
      "Growth",
    ],
    behaviors: [
      "Research Topics",
      "Ask Questions",
      "Explore New Ideas",
      "Try New Things",
      "Share Knowledge",
    ],
  },
  Creative: {
    title: "Creative",
    description:
      "Artistic, innovative, and expression-driven individuals who see the world through a unique lens. Creative personas are natural problem-solvers who approach challenges with imagination and originality. They are drawn to beauty, self-expression, and innovative solutions. Their artistic nature makes them excellent at thinking outside the box and finding creative ways to enhance their environment and experiences.",
    characteristics: [
      "Artistic",
      "Innovative",
      "Expression-Driven",
      "Imaginative",
      "Original",
    ],
    personality: [
      "Creative",
      "Expressive",
      "Intuitive",
      "Passionate",
      "Visionary",
      "Artistic",
    ],
    motivations: [
      "Self-Expression",
      "Beauty",
      "Innovation",
      "Creativity",
      "Inspiration",
    ],
    behaviors: [
      "Create Art",
      "Design Spaces",
      "Express Ideas",
      "Seek Inspiration",
      "Innovate Solutions",
    ],
  },
} as const;

export const mockPersonas: Record<string, Persona> = {
  "1": {
    id: "1",
    name: "Dana Mitchell",
    title: "Segment A, Customer",
    location: "Clive, IA",
    age: 43,
    experience: "Stay-at-home mother",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&q=80&w=400",
    profileImageName: "1.jpg",
    background:
      "Dana is a married, 43-year-old Jewish stay-at-home mother of 3 kids, sons aged 6 & 10, and daughter aged 11. Mixed faith family. Lives in single family home in Clive, IA., in a 3K-4K Sq ft with a household income of $400-$499K.",
    goals: [
      "Create warm, meaningful holiday experiences",
      "Balance family traditions across faiths",
      "Build lasting memories through decorations",
      "Maintain organized holiday storage system",
    ],
    painPoints: [
      "Storage space limitations",
      "Balancing multiple holiday celebrations",
      "Managing decoration budget effectively",
      "Coordinating family decoration timing",
    ],
    preferences: [
      "Organic, evolving decoration style",
      "Mix of old and new decorations",
      "Spontaneous decoration purchases",
      "Family involvement in decorating",
    ],
    category: "Ambitious",
    personaTraits: {
      characteristics: [
        "All-In",
        "Positive",
        "More is More",
        "Aspiring Showcaser",
        "Extra",
      ],
      personality: [
        "Extrovert",
        "Go Big",
        "Leave Nothing Undone",
        "Generous",
        "Upbeat",
        "Big Heart",
        "Dream Big",
      ],
      motivations: [
        "Recognition",
        "Compliments",
        "Pleasing Others",
        "Full Life",
        "Achieving",
        "Productivity",
      ],
      behaviors: [
        "Live Life to the Fullest",
        "Social",
        "Seek Inspiration",
        "High Social Media User",
        "Spontaneous",
      ],
    },
    userDetails: {
      segment: "Segment A, Customer",
      demographics: "Married, 43-year-old Jewish stay-at-home mother",
      household: "3 kids (sons 6 & 10, daughter 11), Mixed faith family",
      lifestyle: "High SM user - IG, Pinterest, Facebook",
      celebrations: [
        "Christmas",
        "Yom Kippur",
        "Thanksgiving",
        "Hanukkah",
        "Halloween",
      ],
    },
    holidayPreferences: {
      decoratingStyle:
        "A culmination of acquired pieces from years past and new pieces we have picked up along the way. Warm, collection of memories, not forced or perfect, in the spirit of family and holiday season.",
      decoratingPhilosophy:
        "If I see it, love it and it speaks to me, I buy it. If it's off season and I love it, I buy it. With Hanukkah items, I usually pick them up if they seem unique and special.",
      inspirationSources: [
        "Other people's homes",
        "Spontaneous finds while shopping",
        "Travel discoveries",
        "Small boutiques",
      ],
      decorationStorage:
        "Various bins and storage containers in basement with specific shelving area",
      decoratingResponsibilities:
        "Primary decorator, starts the process. Kids help with tree and outdoor decorations. Husband and daughter put up tree. Handles fragile and detailed decorating personally.",
      spendingHabits: {
        currentYear: "$100-$150 on small additions",
        previousYear: "Several thousand on major items",
        philosophy:
          "Varies year to year, focuses on permanent investments with annual refreshes",
      },
      shoppingPreferences: {
        stores: [
          "HomeGoods",
          "Marshalls",
          "TJ Maxx",
          "Target",
          "Anthropologie",
          "Pottery Barn",
        ],
        timing: "Year-round, especially during off-season for unique finds",
        decisionMaking:
          "Emotional, intuitive purchasing based on personal connection to items",
      },
    },
  },
  "2": {
    id: "2",
    name: "Michael Chen",
    title: "Segment B, Business Owner",
    location: "Seattle, WA",
    age: 45,
    experience: "15 years",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&q=80&w=400",
    profileImageName: "2.jpg",
    background:
      "Michael is a 45-year-old entrepreneur living in Seattle, WA. He owns a small business and focuses on scalable operations and cost-effective solutions. He lives in a townhouse with a household income of $150-$199K.",
    goals: [
      "Scale business operations",
      "Optimize resource allocation",
      "Improve customer retention",
      "Implement automation tools",
    ],
    painPoints: [
      "Resource management",
      "Market competition",
      "Employee training",
      "Technology integration",
    ],
    preferences: [
      "Efficiency-focused solutions",
      "Cost-effective tools",
      "Simple user interfaces",
      "Mobile accessibility",
    ],
    category: "Architect",
    personaTraits: {
      characteristics: [
        "Strategic",
        "Analytical",
        "Pragmatic",
        "Detail-Oriented",
        "Planner",
      ],
      personality: [
        "Introvert",
        "Logical",
        "Methodical",
        "Focused",
        "Independent",
        "Problem Solver",
      ],
      motivations: [
        "Efficiency",
        "Optimization",
        "Growth",
        "Innovation",
        "Success",
      ],
      behaviors: [
        "Research-Oriented",
        "Data-Driven",
        "Systematic",
        "Goal-Oriented",
        "Continuous Learner",
      ],
    },
    userDetails: {
      segment: "Segment B, Business Owner",
      demographics: "45-year-old entrepreneur",
      household: "Lives in a townhouse",
      lifestyle: "Focuses on scalable operations and cost-effective solutions",
      celebrations: ["Chinese New Year", "Thanksgiving", "Christmas"],
    },
    holidayPreferences: {
      decoratingStyle:
        "Minimalist and functional approach to holiday decorations",
      decoratingPhilosophy:
        "Efficiency-focused with strategic planning and systematic organization",
      inspirationSources: [
        "Business efficiency principles",
        "Professional design resources",
        "Industry best practices",
        "Technology solutions",
      ],
      decorationStorage:
        "Organized storage system with clear labeling and inventory management",
      decoratingResponsibilities:
        "Strategic planning and delegation of tasks to team members",
      spendingHabits: {
        currentYear: "$200-$300 on essential items",
        previousYear: "Focused on ROI and long-term value",
        philosophy: "Investment in quality items that provide lasting value",
      },
      shoppingPreferences: {
        stores: ["Home Depot", "Lowe's", "Target", "Amazon", "Costco"],
        timing: "Strategic planning during off-season sales",
        decisionMaking: "Data-driven decisions based on cost-benefit analysis",
      },
    },
  },
  "3": {
    id: "3",
    name: "Sarah Rodriguez",
    title: "Segment C, Digital Nomad",
    location: "Remote",
    age: 28,
    experience: "5 years",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&q=80&w=400",
    profileImageName: "3.jpg",
    background:
      "Sarah is a 28-year-old remote worker who combines work with travel. She prioritizes flexibility and innovative technology solutions in her workflow. She lives in various locations around the world.",
    goals: [
      "Maintain work-life balance",
      "Optimize remote collaboration",
      "Expand professional network",
      "Learn new technologies",
    ],
    painPoints: [
      "Internet connectivity",
      "Time zone coordination",
      "Remote team collaboration",
      "Work-life boundaries",
    ],
    preferences: [
      "Cloud-based tools",
      "Asynchronous communication",
      "Digital productivity apps",
      "Remote team culture",
    ],
    category: "Contented",
    personaTraits: {
      characteristics: [
        "Flexible",
        "Adventurous",
        "Independent",
        "Tech-Savvy",
        "Curious",
      ],
      personality: [
        "Introvert",
        "Adaptable",
        "Innovative",
        "Self-Motivated",
        "Resourceful",
      ],
      motivations: [
        "Freedom",
        "Exploration",
        "Learning",
        "Work-Life Balance",
        "Personal Growth",
      ],
      behaviors: [
        "Remote Work",
        "Travel",
        "Continuous Learning",
        "Networking",
        "Tech Enthusiast",
      ],
    },
    userDetails: {
      segment: "Segment C, Digital Nomad",
      demographics: "28-year-old remote worker",
      household: "Lives in various locations around the world",
      lifestyle: "Combines work with travel",
      celebrations: ["New Year", "Christmas", "Thanksgiving"],
    },
    holidayPreferences: {
      decoratingStyle:
        "Portable and adaptable decorations that work in any location",
      decoratingPhilosophy:
        "Flexible and minimalist approach that can be easily transported",
      inspirationSources: [
        "Travel experiences",
        "Local cultures",
        "Digital inspiration",
        "Global design trends",
      ],
      decorationStorage:
        "Compact, portable storage solutions that fit in luggage",
      decoratingResponsibilities:
        "Personal responsibility with occasional collaboration with hosts",
      spendingHabits: {
        currentYear: "$50-$100 on portable items",
        previousYear: "Minimal spending on temporary decorations",
        philosophy: "Invest in versatile items that can be used anywhere",
      },
      shoppingPreferences: {
        stores: [
          "Local markets",
          "Online retailers",
          "Travel shops",
          "Digital marketplaces",
        ],
        timing: "Spontaneous purchases during travels",
        decisionMaking: "Intuitive choices based on current location and mood",
      },
    },
  },
  "4": {
    id: "4",
    name: "James Wilson",
    title: "Segment D, Startup Founder",
    location: "Austin, TX",
    age: 35,
    experience: "10 years",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&q=80&w=400",
    profileImageName: "4.jpg",
    background:
      "James is a 35-year-old tech entrepreneur living in Austin, TX. He focuses on building innovative solutions and prioritizes user experience and rapid growth. He lives in a modern apartment with a household income of $200-$299K.",
    goals: [
      "Accelerate market growth",
      "Enhance user experience",
      "Build strong team culture",
      "Secure funding opportunities",
    ],
    painPoints: [
      "Fast-paced market changes",
      "Talent acquisition",
      "Product-market fit",
      "Investor relations",
    ],
    preferences: [
      "Agile methodologies",
      "Data analytics tools",
      "Team collaboration platforms",
      "Innovation frameworks",
    ],
    category: "Sophisticate",
    personaTraits: {
      characteristics: [
        "Innovative",
        "Visionary",
        "Risk-Taker",
        "Leader",
        "Ambitious",
      ],
      personality: [
        "Extrovert",
        "Charismatic",
        "Driven",
        "Creative",
        "Strategic",
      ],
      motivations: ["Success", "Growth", "Innovation", "Leadership", "Impact"],
      behaviors: [
        "Networking",
        "Mentoring",
        "Continuous Learning",
        "Team Building",
        "Pitching",
      ],
    },
    userDetails: {
      segment: "Segment D, Startup Founder",
      demographics: "35-year-old tech entrepreneur",
      household: "Lives in a modern apartment",
      lifestyle: "Focuses on building innovative solutions",
      celebrations: ["New Year", "Christmas", "Thanksgiving"],
    },
    holidayPreferences: {
      decoratingStyle:
        "Modern, innovative approach with cutting-edge technology integration",
      decoratingPhilosophy:
        "Pioneering new trends and creating unique experiences through innovation",
      inspirationSources: [
        "Tech industry trends",
        "Innovation hubs",
        "Design thinking principles",
        "Emerging technologies",
      ],
      decorationStorage:
        "Smart storage solutions with IoT integration and automation",
      decoratingResponsibilities:
        "Leading innovation initiatives and inspiring team creativity",
      spendingHabits: {
        currentYear: "$500-$1000 on innovative items",
        previousYear:
          "Investment in emerging technologies and unique experiences",
        philosophy:
          "Premium investment in cutting-edge solutions and experiences",
      },
      shoppingPreferences: {
        stores: [
          "High-end tech stores",
          "Innovation boutiques",
          "Online marketplaces",
          "Design studios",
        ],
        timing: "Early adoption of new trends and technologies",
        decisionMaking:
          "Innovation-driven choices focused on unique experiences",
      },
    },
  },
  "5": {
    id: "5",
    name: "Emma Thompson",
    title: "Segment E, UX Designer",
    location: "Portland, OR",
    age: 29,
    experience: "6 years",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1534528741775-53994a69daeb?auto=format&fit=crop&q=80&w=400",
    profileImageName: "5.jpg",
    background:
      "Emma is a 29-year-old creative professional specializing in user-centered design. She lives in Portland, OR, and combines research insights with design thinking to create meaningful digital experiences. Her household income is $100-$149K.",
    goals: [
      "Create intuitive interfaces",
      "Improve user satisfaction",
      "Implement design systems",
      "Drive design innovation",
    ],
    painPoints: [
      "Stakeholder alignment",
      "Design consistency",
      "User research constraints",
      "Project timelines",
    ],
    preferences: [
      "Design thinking methods",
      "Collaborative tools",
      "User testing platforms",
      "Design documentation",
    ],
    category: "Overcomer",
    personaTraits: {
      characteristics: [
        "Creative",
        "Empathetic",
        "Detail-Oriented",
        "User-Focused",
        "Innovative",
      ],
      personality: [
        "Introvert",
        "Thoughtful",
        "Curious",
        "Analytical",
        "Collaborative",
      ],
      motivations: [
        "User Satisfaction",
        "Innovation",
        "Creativity",
        "Problem Solving",
        "Collaboration",
      ],
      behaviors: [
        "User Research",
        "Prototyping",
        "Design Thinking",
        "Collaboration",
        "Continuous Learning",
      ],
    },
    userDetails: {
      segment: "Segment E, UX Designer",
      demographics: "29-year-old creative professional",
      household: "Lives in Portland, OR",
      lifestyle: "Specializes in user-centered design",
      celebrations: ["New Year", "Christmas", "Thanksgiving"],
    },
    holidayPreferences: {
      decoratingStyle:
        "User-centered design approach with focus on accessibility and inclusivity",
      decoratingPhilosophy:
        "Creating meaningful experiences that consider diverse user needs and preferences",
      inspirationSources: [
        "User research insights",
        "Design principles",
        "Accessibility guidelines",
        "Inclusive design practices",
      ],
      decorationStorage:
        "Organized system with clear user experience and accessibility considerations",
      decoratingResponsibilities:
        "Ensuring inclusive and accessible holiday experiences for all users",
      spendingHabits: {
        currentYear: "$150-$300 on inclusive items",
        previousYear: "Investment in accessible and user-friendly solutions",
        philosophy:
          "Value-driven purchases that enhance user experience for diverse audiences",
      },
      shoppingPreferences: {
        stores: [
          "Accessible design stores",
          "Inclusive retailers",
          "Local artisans",
          "Online accessibility-focused shops",
        ],
        timing: "Research-driven purchases based on user needs analysis",
        decisionMaking:
          "User-centered decisions focused on accessibility and inclusivity",
      },
    },
  },
  "6": {
    id: "6",
    name: "David Park",
    title: "Segment F, E-commerce Owner",
    location: "Chicago, IL",
    age: 38,
    experience: "12 years",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&q=80&w=400",
    profileImageName: "6.jpg",
    background:
      "David is a 38-year-old digital retail expert living in Chicago, IL. He focuses on optimizing online sales and enhancing customer experience through technology. He lives in a suburban home with a household income of $250-$299K.",
    goals: [
      "Increase online sales",
      "Optimize conversion rates",
      "Enhance customer service",
      "Expand product lines",
    ],
    painPoints: [
      "Inventory management",
      "Shipping logistics",
      "Customer retention",
      "Platform scalability",
    ],
    preferences: [
      "E-commerce platforms",
      "Analytics tools",
      "Customer service software",
      "Inventory systems",
    ],
    category: "Architect",
    personaTraits: {
      characteristics: [
        "Strategic",
        "Analytical",
        "Pragmatic",
        "Detail-Oriented",
        "Planner",
      ],
      personality: [
        "Introvert",
        "Logical",
        "Methodical",
        "Focused",
        "Independent",
        "Problem Solver",
      ],
      motivations: [
        "Efficiency",
        "Optimization",
        "Growth",
        "Innovation",
        "Success",
      ],
      behaviors: [
        "Research-Oriented",
        "Data-Driven",
        "Systematic",
        "Goal-Oriented",
        "Continuous Learner",
      ],
    },
    userDetails: {
      segment: "Segment F, E-commerce Owner",
      demographics: "38-year-old digital retail expert",
      household: "Lives in a suburban home",
      lifestyle:
        "Focuses on optimizing online sales and enhancing customer experience",
      celebrations: ["New Year", "Christmas", "Thanksgiving"],
    },
    holidayPreferences: {
      decoratingStyle:
        "Data-driven approach with focus on customer experience optimization",
      decoratingPhilosophy:
        "Strategic planning based on customer preferences and market trends",
      inspirationSources: [
        "Customer analytics",
        "Market research",
        "E-commerce trends",
        "Customer feedback",
      ],
      decorationStorage:
        "Efficient inventory management system with tracking and analytics",
      decoratingResponsibilities:
        "Data-driven decision making and customer experience optimization",
      spendingHabits: {
        currentYear: "$300-$500 on customer-preferred items",
        previousYear:
          "Investment in customer experience and conversion optimization",
        philosophy:
          "ROI-focused purchases that enhance customer satisfaction and sales",
      },
      shoppingPreferences: {
        stores: [
          "E-commerce platforms",
          "Bulk retailers",
          "Analytics-driven suppliers",
          "Customer-preferred vendors",
        ],
        timing:
          "Data-driven timing based on customer behavior and market trends",
        decisionMaking:
          "Analytics-driven decisions focused on customer satisfaction and business growth",
      },
    },
  },
  "7": {
    id: "7",
    name: "Margaret Williams",
    title: "Segment G, Elementary School Teacher",
    location: "Nashville, TN",
    age: 52,
    experience: "28 years",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?auto=format&fit=crop&q=80&w=400",
    profileImageName: "7.jpg",
    background:
      "Margaret is a 52-year-old elementary school teacher living in Nashville, TN. She has been teaching for 28 years and values tradition, family, and community. She lives in a comfortable suburban home with her husband and two grown children who visit frequently. Her household income is $80-$99K.",
    goals: [
      "Maintain family traditions",
      "Create meaningful holiday memories",
      "Pass down cultural heritage",
      "Build community connections",
    ],
    painPoints: [
      "Limited storage space for heirlooms",
      "Balancing tradition with modern needs",
      "Coordinating family schedules",
      "Preserving fragile decorations",
    ],
    preferences: [
      "Classic, timeless decorations",
      "Family heirlooms and antiques",
      "Traditional color schemes",
      "Handmade ornaments",
    ],
    category: "Traditionalist",
    personaTraits: {
      characteristics: [
        "Conservative",
        "Value-Driven",
        "Family-Oriented",
        "Stable",
        "Traditional",
      ],
      personality: [
        "Reliable",
        "Patient",
        "Loyal",
        "Practical",
        "Respectful",
        "Steady",
      ],
      motivations: [
        "Family Security",
        "Tradition",
        "Stability",
        "Quality",
        "Heritage",
      ],
      behaviors: [
        "Maintain Traditions",
        "Plan Ahead",
        "Invest in Quality",
        "Family-Focused",
        "Conservative Choices",
      ],
    },
    userDetails: {
      segment: "Segment G, Elementary School Teacher",
      demographics: "52-year-old educator",
      household: "Married with grown children",
      lifestyle: "Values tradition and community",
      celebrations: ["Christmas", "Thanksgiving", "Easter", "Fourth of July"],
    },
    holidayPreferences: {
      decoratingStyle:
        "Classic traditional approach with family heirlooms and timeless pieces",
      decoratingPhilosophy:
        "Preserving family traditions and creating meaningful memories through established customs",
      inspirationSources: [
        "Family traditions",
        "Cultural heritage",
        "Classic holiday magazines",
        "Community events",
      ],
      decorationStorage:
        "Carefully organized attic storage with climate control for heirlooms",
      decoratingResponsibilities:
        "Leading family traditions and preserving cultural heritage",
      spendingHabits: {
        currentYear: "$200-$400 on quality traditional items",
        previousYear: "Investment in family heirlooms and durable decorations",
        philosophy:
          "Quality over quantity, focusing on items that will last generations",
      },
      shoppingPreferences: {
        stores: [
          "Local antique shops",
          "Traditional department stores",
          "Craft fairs",
          "Family-owned businesses",
        ],
        timing:
          "Early planning and shopping to ensure quality and availability",
        decisionMaking:
          "Traditional choices based on family history and cultural values",
      },
    },
  },
  "8": {
    id: "8",
    name: "Robert Chen",
    title: "Segment H, Family Physician",
    location: "Salt Lake City, UT",
    age: 45,
    experience: "18 years",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1559839734-2b71ea197ec2?auto=format&fit=crop&q=80&w=400",
    profileImageName: "8.jpg",
    background:
      "Robert is a 45-year-old family physician practicing in Salt Lake City, UT. He values stability, quality care, and family traditions. He lives in a comfortable home with his wife and three children, ages 8, 12, and 15. His household income is $200-$249K.",
    goals: [
      "Provide stable family environment",
      "Invest in quality family experiences",
      "Maintain cultural traditions",
      "Build lasting family bonds",
    ],
    painPoints: [
      "Limited time for holiday preparation",
      "Balancing work and family traditions",
      "Managing family expectations",
      "Preserving cultural heritage",
    ],
    preferences: [
      "Durable, high-quality decorations",
      "Cultural and religious traditions",
      "Family-oriented activities",
      "Conservative, tasteful designs",
    ],
    category: "Traditionalist",
    personaTraits: {
      characteristics: [
        "Conservative",
        "Value-Driven",
        "Family-Oriented",
        "Stable",
        "Traditional",
      ],
      personality: [
        "Reliable",
        "Patient",
        "Loyal",
        "Practical",
        "Respectful",
        "Steady",
      ],
      motivations: [
        "Family Security",
        "Tradition",
        "Stability",
        "Quality",
        "Heritage",
      ],
      behaviors: [
        "Maintain Traditions",
        "Plan Ahead",
        "Invest in Quality",
        "Family-Focused",
        "Conservative Choices",
      ],
    },
    userDetails: {
      segment: "Segment H, Family Physician",
      demographics: "45-year-old healthcare professional",
      household: "Married with three children",
      lifestyle: "Balances medical practice with family life",
      celebrations: ["Christmas", "Thanksgiving", "Chinese New Year", "Easter"],
    },
    holidayPreferences: {
      decoratingStyle:
        "Conservative traditional approach blending cultural heritage with family values",
      decoratingPhilosophy:
        "Creating stable, meaningful family experiences through established traditions and cultural practices",
      inspirationSources: [
        "Cultural traditions",
        "Family history",
        "Religious practices",
        "Community values",
      ],
      decorationStorage:
        "Organized basement storage with proper preservation for cultural items",
      decoratingResponsibilities:
        "Leading family traditions and cultural celebrations",
      spendingHabits: {
        currentYear: "$300-$600 on quality cultural and traditional items",
        previousYear:
          "Investment in durable decorations and cultural artifacts",
        philosophy:
          "Premium investment in items that honor heritage and build family legacy",
      },
      shoppingPreferences: {
        stores: [
          "Cultural specialty stores",
          "Quality department stores",
          "Religious supply stores",
          "Traditional markets",
        ],
        timing: "Careful planning and early shopping for cultural celebrations",
        decisionMaking:
          "Traditional choices that honor cultural heritage and family values",
      },
    },
  },
  "9": {
    id: "9",
    name: "Sarah Kim",
    title: "Segment I, UX Consultant",
    location: "Seattle, WA",
    age: 31,
    experience: "8 years",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1544005313-94ddf0286df2?auto=format&fit=crop&q=80&w=400",
    profileImageName: "9.jpg",
    background:
      "Sarah is a 31-year-old UX consultant living in Seattle, WA. She values simplicity, functionality, and intentional living. She lives in a minimalist apartment and prefers quality over quantity in all aspects of life. Her household income is $120-$149K.",
    goals: [
      "Create peaceful living environment",
      "Invest in quality over quantity",
      "Reduce environmental impact",
      "Maintain organized spaces",
    ],
    painPoints: [
      "Limited storage in apartment",
      "Finding quality sustainable items",
      "Avoiding holiday clutter",
      "Maintaining minimalist aesthetic",
    ],
    preferences: [
      "Simple, functional decorations",
      "Sustainable materials",
      "Neutral color palettes",
      "Multi-purpose items",
    ],
    category: "Minimalist",
    personaTraits: {
      characteristics: [
        "Simple",
        "Practical",
        "Intentional",
        "Quality-Focused",
        "Sustainable",
      ],
      personality: [
        "Thoughtful",
        "Organized",
        "Mindful",
        "Efficient",
        "Independent",
        "Calm",
      ],
      motivations: [
        "Simplicity",
        "Quality",
        "Sustainability",
        "Peace of Mind",
        "Intentional Living",
      ],
      behaviors: [
        "Declutter Regularly",
        "Research Purchases",
        "Choose Quality",
        "Sustainable Choices",
        "Mindful Consumption",
      ],
    },
    userDetails: {
      segment: "Segment I, UX Consultant",
      demographics: "31-year-old tech professional",
      household: "Single, minimalist apartment",
      lifestyle: "Values simplicity and sustainability",
      celebrations: ["New Year", "Christmas", "Thanksgiving"],
    },
    holidayPreferences: {
      decoratingStyle:
        "Minimalist approach with focus on quality, functionality, and sustainability",
      decoratingPhilosophy:
        "Creating meaningful experiences through intentional, simple, and sustainable choices",
      inspirationSources: [
        "Minimalist design blogs",
        "Sustainable living resources",
        "Scandinavian design",
        "Zen philosophy",
      ],
      decorationStorage:
        "Minimal, organized storage with focus on multi-purpose and sustainable solutions",
      decoratingResponsibilities:
        "Creating peaceful, intentional holiday experiences",
      spendingHabits: {
        currentYear: "$100-$250 on quality sustainable items",
        previousYear: "Investment in durable, multi-purpose decorations",
        philosophy:
          "Quality over quantity, focusing on sustainable and functional items",
      },
      shoppingPreferences: {
        stores: [
          "Sustainable retailers",
          "Minimalist design stores",
          "Local artisans",
          "Online sustainable shops",
        ],
        timing: "Thoughtful, research-based purchases throughout the year",
        decisionMaking:
          "Intentional choices focused on quality, sustainability, and functionality",
      },
    },
  },
  "10": {
    id: "10",
    name: "Michael Rodriguez",
    title: "Segment J, Financial Advisor",
    location: "Austin, TX",
    age: 38,
    experience: "12 years",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&q=80&w=400",
    profileImageName: "10.jpg",
    background:
      "Michael is a 38-year-old financial advisor living in Austin, TX. He values simplicity, quality, and intentional living. He lives in a modern townhouse with his partner and prefers thoughtful, purposeful choices. His household income is $180-$219K.",
    goals: [
      "Maintain organized lifestyle",
      "Invest in quality experiences",
      "Reduce unnecessary consumption",
      "Create peaceful home environment",
    ],
    painPoints: [
      "Avoiding holiday overspending",
      "Finding quality over quantity",
      "Maintaining organized spaces",
      "Balancing celebration with simplicity",
    ],
    preferences: [
      "Clean, uncluttered designs",
      "Quality materials",
      "Neutral, sophisticated colors",
      "Functional decorations",
    ],
    category: "Minimalist",
    personaTraits: {
      characteristics: [
        "Simple",
        "Practical",
        "Intentional",
        "Quality-Focused",
        "Sustainable",
      ],
      personality: [
        "Thoughtful",
        "Organized",
        "Mindful",
        "Efficient",
        "Independent",
        "Calm",
      ],
      motivations: [
        "Simplicity",
        "Quality",
        "Sustainability",
        "Peace of Mind",
        "Intentional Living",
      ],
      behaviors: [
        "Declutter Regularly",
        "Research Purchases",
        "Choose Quality",
        "Sustainable Choices",
        "Mindful Consumption",
      ],
    },
    userDetails: {
      segment: "Segment J, Financial Advisor",
      demographics: "38-year-old financial professional",
      household: "Partnered, modern townhouse",
      lifestyle: "Values intentional living and quality",
      celebrations: ["New Year", "Christmas", "Thanksgiving"],
    },
    holidayPreferences: {
      decoratingStyle:
        "Sophisticated minimalist approach with focus on quality and functionality",
      decoratingPhilosophy:
        "Creating meaningful experiences through intentional, quality-focused choices",
      inspirationSources: [
        "Modern design magazines",
        "Minimalist lifestyle blogs",
        "Quality-focused retailers",
        "Sustainable living resources",
      ],
      decorationStorage:
        "Organized, minimal storage system with focus on quality preservation",
      decoratingResponsibilities:
        "Creating peaceful, intentional holiday experiences",
      spendingHabits: {
        currentYear: "$200-$400 on quality items",
        previousYear: "Investment in durable, high-quality decorations",
        philosophy:
          "Quality over quantity, focusing on items that enhance lifestyle",
      },
      shoppingPreferences: {
        stores: [
          "Quality department stores",
          "Minimalist design boutiques",
          "Sustainable retailers",
          "Local quality artisans",
        ],
        timing: "Thoughtful, planned purchases throughout the year",
        decisionMaking:
          "Intentional choices focused on quality, functionality, and lifestyle enhancement",
      },
    },
  },
  "11": {
    id: "11",
    name: "Jennifer Martinez",
    title: "Segment K, Sales Director",
    location: "Miami, FL",
    age: 35,
    experience: "14 years",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1544005313-94ddf0286df2?auto=format&fit=crop&q=80&w=400",
    profileImageName: "11.jpg",
    background:
      "Jennifer is a 35-year-old sales director living in Miami, FL. She is highly competitive, goal-oriented, and driven by success. She lives in a luxury condo and constantly strives to exceed expectations in all areas of life. Her household income is $300-$399K.",
    goals: [
      "Achieve top performance metrics",
      "Build impressive home environment",
      "Exceed all expectations",
      "Maintain competitive edge",
    ],
    painPoints: [
      "Finding premium quality items",
      "Staying ahead of trends",
      "Managing high expectations",
      "Balancing work and personal life",
    ],
    preferences: [
      "Premium, luxury decorations",
      "Trend-setting designs",
      "High-quality materials",
      "Status-enhancing items",
    ],
    category: "Over-Achiever",
    personaTraits: {
      characteristics: [
        "Goal-Oriented",
        "Competitive",
        "Success-Driven",
        "Ambitious",
        "Perfectionist",
      ],
      personality: [
        "Driven",
        "Focused",
        "Determined",
        "Confident",
        "Results-Oriented",
        "High Standards",
      ],
      motivations: [
        "Achievement",
        "Recognition",
        "Success",
        "Excellence",
        "Competition",
      ],
      behaviors: [
        "Set High Goals",
        "Work Hard",
        "Seek Recognition",
        "Compete",
        "Exceed Expectations",
      ],
    },
    userDetails: {
      segment: "Segment K, Sales Director",
      demographics: "35-year-old sales executive",
      household: "Single, luxury condo",
      lifestyle: "High-achieving, competitive professional",
      celebrations: ["New Year", "Christmas", "Thanksgiving", "Birthdays"],
    },
    holidayPreferences: {
      decoratingStyle:
        "Premium, luxury approach with focus on status and excellence",
      decoratingPhilosophy:
        "Creating impressive, trend-setting experiences that demonstrate success and achievement",
      inspirationSources: [
        "Luxury lifestyle magazines",
        "High-end design blogs",
        "Premium retailers",
        "Celebrity homes",
      ],
      decorationStorage:
        "Premium storage solutions with climate control and organization systems",
      decoratingResponsibilities:
        "Creating impressive, status-enhancing holiday experiences",
      spendingHabits: {
        currentYear: "$800-$1500 on premium items",
        previousYear:
          "Investment in luxury decorations and premium experiences",
        philosophy:
          "Premium investment in items that reflect success and achievement",
      },
      shoppingPreferences: {
        stores: [
          "Luxury department stores",
          "High-end boutiques",
          "Premium online retailers",
          "Exclusive designers",
        ],
        timing: "Early adoption of trends and premium seasonal items",
        decisionMaking:
          "Premium choices focused on status, quality, and competitive advantage",
      },
    },
  },
  "12": {
    id: "12",
    name: "Alex Thompson",
    title: "Segment L, Professional Athlete",
    location: "Denver, CO",
    age: 28,
    experience: "6 years",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&q=80&w=400",
    profileImageName: "12.jpg",
    background:
      "Alex is a 28-year-old professional athlete living in Denver, CO. He is highly competitive, driven by excellence, and constantly pushing his limits. He lives in a modern home and approaches everything with the same intensity as his athletic training. His household income is $500-$599K.",
    goals: [
      "Maintain peak performance",
      "Create winning environment",
      "Exceed all expectations",
      "Build legacy of success",
    ],
    painPoints: [
      "Finding premium quality items",
      "Staying ahead of competition",
      "Managing high performance standards",
      "Balancing training and personal life",
    ],
    preferences: [
      "High-performance decorations",
      "Premium quality materials",
      "Competitive edge items",
      "Excellence-focused designs",
    ],
    category: "Over-Achiever",
    personaTraits: {
      characteristics: [
        "Goal-Oriented",
        "Competitive",
        "Success-Driven",
        "Ambitious",
        "Perfectionist",
      ],
      personality: [
        "Driven",
        "Focused",
        "Determined",
        "Confident",
        "Results-Oriented",
        "High Standards",
      ],
      motivations: [
        "Achievement",
        "Recognition",
        "Success",
        "Excellence",
        "Competition",
      ],
      behaviors: [
        "Set High Goals",
        "Work Hard",
        "Seek Recognition",
        "Compete",
        "Exceed Expectations",
      ],
    },
    userDetails: {
      segment: "Segment L, Professional Athlete",
      demographics: "28-year-old professional athlete",
      household: "Single, modern home",
      lifestyle: "High-performance, competitive athlete",
      celebrations: [
        "New Year",
        "Christmas",
        "Thanksgiving",
        "Achievement milestones",
      ],
    },
    holidayPreferences: {
      decoratingStyle:
        "High-performance approach with focus on excellence and competitive advantage",
      decoratingPhilosophy:
        "Creating winning experiences that reflect peak performance and achievement",
      inspirationSources: [
        "Athletic performance resources",
        "Premium lifestyle magazines",
        "High-end design blogs",
        "Success-focused content",
      ],
      decorationStorage:
        "High-performance storage solutions with organization and preservation systems",
      decoratingResponsibilities:
        "Creating winning, excellence-focused holiday experiences",
      spendingHabits: {
        currentYear: "$1000-$2000 on premium performance items",
        previousYear:
          "Investment in high-performance decorations and premium experiences",
        philosophy:
          "Premium investment in items that reflect excellence and competitive advantage",
      },
      shoppingPreferences: {
        stores: [
          "Premium athletic stores",
          "High-end boutiques",
          "Luxury retailers",
          "Exclusive designers",
        ],
        timing:
          "Early adoption of premium trends and performance-focused items",
        decisionMaking:
          "Premium choices focused on excellence, performance, and competitive advantage",
      },
    },
  },
  "13": {
    id: "13",
    name: "Dr. Emily Foster",
    title: "Segment M, Research Scientist",
    location: "Boston, MA",
    age: 34,
    experience: "10 years",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&q=80&w=400",
    profileImageName: "13.jpg",
    background:
      "Emily is a 34-year-old research scientist living in Boston, MA. She is naturally curious, always seeking new knowledge, and loves exploring new ideas and technologies. She lives in a cozy apartment near the university and enjoys experimenting with new approaches. Her household income is $140-$169K.",
    goals: [
      "Discover new holiday trends",
      "Experiment with innovative decorations",
      "Learn about different cultures",
      "Explore creative solutions",
    ],
    painPoints: [
      "Finding unique experimental items",
      "Researching new trends thoroughly",
      "Balancing experimentation with practicality",
      "Managing information overload",
    ],
    preferences: [
      "Innovative, experimental decorations",
      "Cultural diversity in designs",
      "Technology-integrated items",
      "Unique, conversation-starting pieces",
    ],
    category: "Curious",
    personaTraits: {
      characteristics: [
        "Inquisitive",
        "Learning-Focused",
        "Exploration-Driven",
        "Open-Minded",
        "Knowledge-Seeking",
      ],
      personality: [
        "Curious",
        "Enthusiastic",
        "Adaptable",
        "Analytical",
        "Innovative",
        "Eager to Learn",
      ],
      motivations: [
        "Knowledge",
        "Discovery",
        "Understanding",
        "Innovation",
        "Growth",
      ],
      behaviors: [
        "Research Topics",
        "Ask Questions",
        "Explore New Ideas",
        "Try New Things",
        "Share Knowledge",
      ],
    },
    userDetails: {
      segment: "Segment M, Research Scientist",
      demographics: "34-year-old research professional",
      household: "Single, university area apartment",
      lifestyle: "Research-focused, knowledge-seeking",
      celebrations: [
        "New Year",
        "Christmas",
        "Thanksgiving",
        "Cultural celebrations",
      ],
    },
    holidayPreferences: {
      decoratingStyle:
        "Experimental approach with focus on innovation, cultural diversity, and new discoveries",
      decoratingPhilosophy:
        "Exploring new ideas and creating unique experiences through research and experimentation",
      inspirationSources: [
        "Research publications",
        "Cultural resources",
        "Innovation blogs",
        "Technology trends",
      ],
      decorationStorage:
        "Organized research-based storage with documentation and experimentation tracking",
      decoratingResponsibilities:
        "Exploring new trends and creating innovative holiday experiences",
      spendingHabits: {
        currentYear: "$200-$400 on experimental and innovative items",
        previousYear:
          "Investment in research tools and experimental decorations",
        philosophy:
          "Research-driven purchases focused on discovery and innovation",
      },
      shoppingPreferences: {
        stores: [
          "Innovation boutiques",
          "Cultural specialty stores",
          "Technology retailers",
          "Online research platforms",
        ],
        timing: "Research-driven timing based on new discoveries and trends",
        decisionMaking:
          "Curiosity-driven choices focused on learning and experimentation",
      },
    },
  },
  "14": {
    id: "14",
    name: "Marcus Johnson",
    title: "Segment N, Travel Journalist",
    location: "San Francisco, CA",
    age: 29,
    experience: "7 years",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&q=80&w=400",
    profileImageName: "14.jpg",
    background:
      "Marcus is a 29-year-old travel journalist living in San Francisco, CA. He is naturally curious, loves exploring different cultures, and is always seeking new experiences and stories. He lives in a vibrant neighborhood and enjoys bringing global influences into his home. His household income is $110-$139K.",
    goals: [
      "Explore global holiday traditions",
      "Discover unique cultural decorations",
      "Learn about different celebrations",
      "Share cultural experiences",
    ],
    painPoints: [
      "Finding authentic cultural items",
      "Researching global traditions",
      "Balancing travel with home life",
      "Managing diverse cultural influences",
    ],
    preferences: [
      "Global cultural decorations",
      "Authentic traditional items",
      "Story-rich pieces",
      "Conversation-starting designs",
    ],
    category: "Curious",
    personaTraits: {
      characteristics: [
        "Inquisitive",
        "Learning-Focused",
        "Exploration-Driven",
        "Open-Minded",
        "Knowledge-Seeking",
      ],
      personality: [
        "Curious",
        "Enthusiastic",
        "Adaptable",
        "Analytical",
        "Innovative",
        "Eager to Learn",
      ],
      motivations: [
        "Knowledge",
        "Discovery",
        "Understanding",
        "Innovation",
        "Growth",
      ],
      behaviors: [
        "Research Topics",
        "Ask Questions",
        "Explore New Ideas",
        "Try New Things",
        "Share Knowledge",
      ],
    },
    userDetails: {
      segment: "Segment N, Travel Journalist",
      demographics: "29-year-old travel professional",
      household: "Single, vibrant urban neighborhood",
      lifestyle: "Travel-focused, culturally curious",
      celebrations: [
        "New Year",
        "Christmas",
        "Thanksgiving",
        "Global celebrations",
      ],
    },
    holidayPreferences: {
      decoratingStyle:
        "Global cultural approach with focus on authentic traditions and diverse influences",
      decoratingPhilosophy:
        "Exploring and sharing cultural traditions through authentic, story-rich decorations",
      inspirationSources: [
        "Global travel experiences",
        "Cultural research",
        "International traditions",
        "Authentic artisans",
      ],
      decorationStorage:
        "Organized cultural storage with documentation of origins and stories",
      decoratingResponsibilities:
        "Exploring global traditions and creating culturally rich experiences",
      spendingHabits: {
        currentYear: "$150-$350 on authentic cultural items",
        previousYear: "Investment in global travel and cultural artifacts",
        philosophy:
          "Authenticity-focused purchases that preserve cultural heritage",
      },
      shoppingPreferences: {
        stores: [
          "Cultural specialty shops",
          "International markets",
          "Authentic artisans",
          "Global online platforms",
        ],
        timing:
          "Travel-driven timing based on cultural discoveries and authentic finds",
        decisionMaking:
          "Curiosity-driven choices focused on cultural authenticity and global learning",
      },
    },
  },

  "16": {
    id: "16",
    name: "David Chen",
    title: "Segment P, Marketing Creative Director",
    location: "New York, NY",
    age: 36,
    experience: "13 years",
    email: "<EMAIL>",
    phone: "+****************",
    profileImageUrl:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&q=80&w=400",
    profileImageName: "16.jpg",
    background:
      "David is a 36-year-old marketing creative director living in New York, NY. He is highly creative, innovative, and constantly pushing creative boundaries. He lives in a trendy loft and approaches everything with artistic vision and creative flair. His household income is $220-$249K.",
    goals: [
      "Create innovative holiday experiences",
      "Express artistic vision",
      "Push creative boundaries",
      "Inspire creative thinking",
    ],
    painPoints: [
      "Finding truly unique pieces",
      "Balancing creativity with trends",
      "Managing artistic vision",
      "Sourcing innovative materials",
    ],
    preferences: [
      "Innovative, cutting-edge designs",
      "Artistic expression pieces",
      "Creative technology integration",
      "Trend-setting decorations",
    ],
    category: "Creative",
    personaTraits: {
      characteristics: [
        "Artistic",
        "Innovative",
        "Expression-Driven",
        "Imaginative",
        "Original",
      ],
      personality: [
        "Creative",
        "Expressive",
        "Intuitive",
        "Passionate",
        "Visionary",
        "Artistic",
      ],
      motivations: [
        "Self-Expression",
        "Beauty",
        "Innovation",
        "Creativity",
        "Inspiration",
      ],
      behaviors: [
        "Create Art",
        "Design Spaces",
        "Express Ideas",
        "Seek Inspiration",
        "Innovate Solutions",
      ],
    },
    userDetails: {
      segment: "Segment P, Marketing Creative Director",
      demographics: "36-year-old creative executive",
      household: "Single, trendy loft",
      lifestyle: "Creative, trend-setting professional",
      celebrations: [
        "New Year",
        "Christmas",
        "Thanksgiving",
        "Creative events",
      ],
    },
    holidayPreferences: {
      decoratingStyle:
        "Innovative creative approach with focus on artistic expression and trend-setting design",
      decoratingPhilosophy:
        "Creating inspiring, innovative experiences through artistic vision and creative innovation",
      inspirationSources: [
        "Creative agencies",
        "Art galleries",
        "Innovation hubs",
        "Trend-setting blogs",
      ],
      decorationStorage:
        "Innovative storage solutions with creative organization and artistic preservation",
      decoratingResponsibilities:
        "Creating innovative, inspiring holiday experiences",
      spendingHabits: {
        currentYear: "$400-$800 on innovative and creative items",
        previousYear: "Investment in creative tools and innovative materials",
        philosophy:
          "Creative investment in items that push boundaries and inspire innovation",
      },
      shoppingPreferences: {
        stores: [
          "Innovation boutiques",
          "Art galleries",
          "Creative supply stores",
          "Trend-setting retailers",
        ],
        timing:
          "Creative timing based on artistic inspiration and trend innovation",
        decisionMaking:
          "Creative choices focused on innovation and artistic expression",
      },
    },
  },
};
