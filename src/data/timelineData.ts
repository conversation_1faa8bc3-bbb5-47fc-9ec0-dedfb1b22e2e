// Timeline and Progress data and utilities

// Function to get the Friday of a given week offset from today
export const getFridayOfWeek = (weekOffset: number): string => {
  const today = new Date();
  const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, ..., 5 = Friday, 6 = Saturday
  
  // Calculate days to next Friday (or today if it's Friday)
  const daysToFriday = currentDay <= 5 ? 5 - currentDay : 7 - currentDay + 5;
  
  // Get the target Friday by adding the week offset and days to Friday
  const targetFriday = new Date(today);
  targetFriday.setDate(today.getDate() + daysToFriday + (weekOffset * 7));
  
  // Format as "MMM DD, YYYY"
  return targetFriday.toLocaleDateString('en-US', {
    month: 'short',
    day: '2-digit',
    year: 'numeric'
  });
};

// Function to get today's date in the same format
export const getTodaysDate = (): string => {
  const today = new Date();
  return today.toLocaleDateString('en-US', {
    month: 'long',
    day: '2-digit',
    year: 'numeric'
  });
};

// Timeline events data
export const timelineEvents = [
  {
    phase: "Study Design & Participant Recruit",
    date: getFridayOfWeek(-5), // 5 weeks ago Friday
    completed: true,
    details: [
      "Research methodology defined",
      "Participant criteria established",
      "30 potential participants identified",
    ],
  },
  {
    phase: "Phase I: Real Life",
    date: getFridayOfWeek(-4), // 4 weeks ago Friday
    completed: true,
    details: [
      "In-depth interviews conducted",
      "Daily activity logs collected",
      "Behavioral patterns analyzed",
    ],
  },
  {
    phase: "Phase II: Holiday Prep",
    date: getFridayOfWeek(-3), // 3 weeks ago Friday
    completed: true,
    details: [
      "Shopping patterns documented",
      "Decision-making processes analyzed",
      "Pain points identified",
    ],
  },
  {
    phase: "Phase III: Holiday Experience",
    date: getFridayOfWeek(-2), // 2 weeks ago Friday
    completed: true,
    details: [
      "Experience mapping completed",
      "Key moments identified",
      "Satisfaction metrics collected",
    ],
  },
  {
    phase: "Phase IV: Depth Interviews",
    date: getFridayOfWeek(-1), // Last Friday
    completed: true,
    details: [
      "Follow-up interviews conducted",
      "Data synthesis completed",
      "Initial insights generated",
    ],
  },
  {
    phase: "Persona Workshop",
    date: "Today",
    current: true,
    details: [
      "Persona attributes definition",
      "Journey mapping",
      "Validation session",
    ],
  },
  {
    phase: "Completion",
    date: getFridayOfWeek(1), // Next Friday
    upcoming: true,
    details: [
      "Final report compilation",
      "Presentation preparation",
      "Stakeholder review",
    ],
  },
];

// Utility functions for timeline and progress calculations
export const getCompletedCount = () => {
  return timelineEvents.filter((event) => event.completed).length;
};

export const getProgressPercentage = () => {
  return (getCompletedCount() / timelineEvents.length) * 100;
};

// Timeline-specific utility functions
export const getFirstRowEvents = () => {
  return timelineEvents.slice(0, 4);
};

export const getSecondRowEvents = () => {
  return timelineEvents.slice(4);
};

// Get recent timeline events for Home page summary
export const getRecentTimelineEvents = () => {
  return [
    {
      phase: "Phase III: Holiday Experience",
      date: getFridayOfWeek(-2),
      completed: true,
    },
    {
      phase: "Phase IV: Depth Interviews",
      date: getFridayOfWeek(-1),
      completed: true,
    },
    {
      phase: "Today: Persona Workshop",
      date: "Today",
      current: true
    },
  ];
};

// Timeline event type definition for TypeScript
export interface TimelineEvent {
  phase: string;
  date: string;
  completed?: boolean;
  current?: boolean;
  upcoming?: boolean;
  details: string[];
}
