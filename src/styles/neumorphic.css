/* ==========================================================================
   MODAL POSITIONING & CAROUSEL OVERRIDES
   ========================================================================== */

/* Modal positioning that accounts for sidebar */
.modal-with-sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: var(--z-modal);
  left: var(--sidebar-width);
}

/* CSS variables for sidebar widths */
:root {
  --sidebar-width-expanded: 256px;
  --sidebar-width-collapsed: 64px;
}

/* ==========================================================================
   NEUMORPHIC ADMIN COMPONENTS
   ========================================================================== */

/* Neumorphic Container */
.neumorphic-container {
  background: var(--color-neumorphic-bg);
  border-radius: var(--radius-2xl);
  box-shadow: var(--neumorphic-shadow-light), var(--neumorphic-shadow-dark);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Neumorphic Inset (pressed effect) */
.neumorphic-inset {
  background: var(--color-neumorphic-bg);
  border-radius: var(--radius-lg);
  box-shadow: inset var(--neumorphic-shadow-light),
    inset var(--neumorphic-shadow-dark);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Neumorphic Icon */
.neumorphic-icon {
  background: var(--color-neumorphic-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--neumorphic-shadow-light), var(--neumorphic-shadow-dark);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Neumorphic Input */
.neumorphic-input {
  background: var(--color-neumorphic-bg);
  border-radius: var(--radius-lg);
  box-shadow: inset var(--neumorphic-shadow-light),
    inset var(--neumorphic-shadow-dark);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--color-text-inverse);
  transition: all var(--transition-base);
}

.neumorphic-input:focus {
  outline: none;
  box-shadow: inset var(--neumorphic-shadow-light),
    inset var(--neumorphic-shadow-dark), 0 0 0 3px rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.neumorphic-input::placeholder {
  color: rgba(0, 0, 0, 0.5);
}

/* Neumorphic Button Primary */
.neumorphic-button-primary {
  background: linear-gradient(
    135deg,
    var(--color-primary-500),
    var(--color-primary-600)
  );
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: 0.75rem 1.5rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-base);
  box-shadow: var(--neumorphic-shadow-light), var(--neumorphic-shadow-dark);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.neumorphic-button-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--neumorphic-shadow-light), var(--neumorphic-shadow-dark),
    0 8px 25px rgba(0, 0, 0, 0.15);
}

.neumorphic-button-primary:active:not(:disabled) {
  transform: translateY(0);
}

.neumorphic-button-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Neumorphic Button Secondary */
.neumorphic-button-secondary {
  background: var(--color-neumorphic-bg);
  color: var(--color-text-primary);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: 0.75rem 1.5rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-base);
  box-shadow: var(--neumorphic-shadow-light), var(--neumorphic-shadow-dark);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.neumorphic-button-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
  box-shadow: var(--neumorphic-shadow-light), var(--neumorphic-shadow-dark),
    0 4px 15px rgba(0, 0, 0, 0.1);
}

.neumorphic-button-secondary:active:not(:disabled) {
  transform: translateY(0);
}

.neumorphic-button-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Slick carousel dot styles to match Customer Personas section */
.slick-dots {
  bottom: -30px !important;
  position: relative !important;
}

.slick-dots li button {
  background-color: var(--color-gray-300) !important;
  border: 1px solid var(--color-gray-400) !important;
  width: 12px !important;
  height: 12px !important;
  border-radius: var(--radius-full) !important;
  text-indent: -9999px !important;
  font-size: 0 !important;
  line-height: 0 !important;
}

.slick-dots li button:before {
  display: none !important;
  content: "" !important;
}

.slick-dots li.slick-active button {
  background-color: var(--color-gray-900) !important;
  border: 1px solid var(--color-gray-400) !important;
}

.slick-dots li.slick-active button:before {
  display: none !important;
  content: "" !important;
}

/* Additional specificity for All Personas section dots */
.slick-slider .slick-dots {
  bottom: -30px !important;
  position: relative !important;
}

.slick-slider .slick-dots li button {
  background-color: var(--color-gray-300) !important;
  border: 1px solid var(--color-gray-400) !important;
  width: 12px !important;
  height: 12px !important;
  border-radius: var(--radius-full) !important;
  text-indent: -9999px !important;
  font-size: 0 !important;
  line-height: 0 !important;
}

.slick-slider .slick-dots li button:before {
  display: none !important;
  content: "" !important;
}

.slick-slider .slick-dots li.slick-active button {
  background-color: var(--color-gray-900) !important;
  border: 1px solid var(--color-gray-400) !important;
}

.slick-slider .slick-dots li.slick-active button:before {
  display: none !important;
  content: "" !important;
}
