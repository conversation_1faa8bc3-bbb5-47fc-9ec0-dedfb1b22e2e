/* Design System Tokens - CSS Module */
/* This module exports CSS custom properties that can be used across all components */

:export {
  /* Colors */
  colorPrimary50: #eff6ff;
  colorPrimary100: #dbeafe;
  colorPrimary200: #bfdbfe;
  colorPrimary300: #93c5fd;
  colorPrimary400: #60a5fa;
  colorPrimary500: #3b82f6;
  colorPrimary600: #2563eb;
  colorPrimary700: #1d4ed8;
  colorPrimary800: #1e40af;
  colorPrimary900: #1e3a8a;

  colorSecondary50: #faf5ff;
  colorSecondary100: #f3e8ff;
  colorSecondary200: #e9d5ff;
  colorSecondary300: #d8b4fe;
  colorSecondary400: #c084fc;
  colorSecondary500: #a855f7;
  colorSecondary600: #9333ea;
  colorSecondary700: #7c2d12;
  colorSecondary800: #6b21a8;
  colorSecondary900: #581c87;

  colorGray50: #f9fafb;
  colorGray100: #f3f4f6;
  colorGray200: #e5e7eb;
  colorGray300: #d1d5db;
  colorGray400: #9ca3af;
  colorGray500: #6b7280;
  colorGray600: #4b5563;
  colorGray700: #374151;
  colorGray800: #1f2937;
  colorGray900: #111827;

  colorSuccess50: #f0fdf4;
  colorSuccess500: #22c55e;
  colorSuccess600: #16a34a;

  colorWarning50: #fffbeb;
  colorWarning500: #f59e0b;
  colorWarning600: #d97706;

  colorError50: #fef2f2;
  colorError500: #ef4444;
  colorError600: #dc2626;

  colorNeumorphicBg: #e6ebf0;
  colorNeumorphicShadowLight: #ffffff;
  colorNeumorphicShadowDark: #c2c9d1;

  /* Typography */
  fontFamilySans: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  fontFamilyMono: "Menlo", "Monaco", "Courier New", monospace;

  fontSizeXs: 0.75rem;
  fontSizeSm: 0.875rem;
  fontSizeBase: 1rem;
  fontSizeLg: 1.125rem;
  fontSizeXl: 1.25rem;
  fontSize2xl: 1.5rem;
  fontSize3xl: 1.875rem;
  fontSize4xl: 2.25rem;
  fontSize5xl: 3rem;

  fontWeightLight: 300;
  fontWeightNormal: 400;
  fontWeightMedium: 500;
  fontWeightSemibold: 600;
  fontWeightBold: 700;

  lineHeightTight: 1.25;
  lineHeightNormal: 1.5;
  lineHeightRelaxed: 1.625;

  /* Spacing */
  spacing0: 0;
  spacingPx: 1px;
  spacing05: 0.125rem;
  spacing1: 0.25rem;
  spacing15: 0.375rem;
  spacing2: 0.5rem;
  spacing25: 0.625rem;
  spacing3: 0.75rem;
  spacing35: 0.875rem;
  spacing4: 1rem;
  spacing5: 1.25rem;
  spacing6: 1.5rem;
  spacing7: 1.75rem;
  spacing8: 2rem;
  spacing9: 2.25rem;
  spacing10: 2.5rem;
  spacing11: 2.75rem;
  spacing12: 3rem;
  spacing14: 3.5rem;
  spacing16: 4rem;
  spacing20: 5rem;
  spacing24: 6rem;
  spacing28: 7rem;
  spacing32: 8rem;
  spacing36: 9rem;
  spacing40: 10rem;
  spacing44: 11rem;
  spacing48: 12rem;
  spacing52: 13rem;
  spacing56: 14rem;
  spacing60: 15rem;
  spacing64: 16rem;
  spacing72: 18rem;
  spacing80: 20rem;
  spacing96: 24rem;

  /* Border Radius */
  radiusNone: 0;
  radiusSm: 0.125rem;
  radiusBase: 0.25rem;
  radiusMd: 0.375rem;
  radiusLg: 0.5rem;
  radiusXl: 0.75rem;
  radius2xl: 1rem;
  radius3xl: 1.5rem;
  radiusFull: 9999px;

  /* Box Shadow */
  shadowSm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  shadowBase: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  shadowMd: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  shadowLg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  shadowXl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  shadow2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  shadowInner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);

  shadowNeumorphicElevated: 6px 6px 12px #c2c9d1, -6px -6px 12px #ffffff;
  shadowNeumorphicInset: inset 6px 6px 12px #c2c9d1, inset -6px -6px 12px #ffffff;
  shadowNeumorphicContainer: 3px 3px 6px #c2c9d1, -3px -3px 6px #ffffff;

  /* Z-Index */
  zDropdown: 1000;
  zSticky: 1020;
  zFixed: 1030;
  zModalBackdrop: 1040;
  zModal: 1050;
  zPopover: 1060;
  zTooltip: 1070;

  /* Transitions */
  transitionFast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  transitionBase: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  transitionSlow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
  transitionSlower: 500ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Layout */
  containerMaxWidth: 1280px;
  sidebarWidthExpanded: 256px;
  sidebarWidthCollapsed: 64px;
  headerHeight: 64px;
}

/* Global CSS Custom Properties */
:root {
  /* Colors */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  --color-secondary-50: #faf5ff;
  --color-secondary-100: #f3e8ff;
  --color-secondary-200: #e9d5ff;
  --color-secondary-300: #d8b4fe;
  --color-secondary-400: #c084fc;
  --color-secondary-500: #a855f7;
  --color-secondary-600: #9333ea;
  --color-secondary-700: #7c2d12;
  --color-secondary-800: #6b21a8;
  --color-secondary-900: #581c87;

  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;

  --color-warning-50: #fffbeb;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;

  --color-error-50: #fef2f2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;

  --color-neumorphic-bg: #e6ebf0;
  --color-neumorphic-shadow-light: #ffffff;
  --color-neumorphic-shadow-dark: #c2c9d1;

  --color-text-primary: var(--color-gray-900);
  --color-text-secondary: var(--color-gray-600);
  --color-text-muted: var(--color-gray-500);
  --color-text-inverse: var(--color-gray-50);
  --color-text-accent: var(--color-primary-600);

  /* Typography */
  --font-family-sans: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Menlo", "Monaco", "Courier New", monospace;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;

  /* Spacing */
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0-5: 0.125rem;
  --spacing-1: 0.25rem;
  --spacing-1-5: 0.375rem;
  --spacing-2: 0.5rem;
  --spacing-2-5: 0.625rem;
  --spacing-3: 0.75rem;
  --spacing-3-5: 0.875rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-9: 2.25rem;
  --spacing-10: 2.5rem;
  --spacing-11: 2.75rem;
  --spacing-12: 3rem;
  --spacing-14: 3.5rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-28: 7rem;
  --spacing-32: 8rem;
  --spacing-36: 9rem;
  --spacing-40: 10rem;
  --spacing-44: 11rem;
  --spacing-48: 12rem;
  --spacing-52: 13rem;
  --spacing-56: 14rem;
  --spacing-60: 15rem;
  --spacing-64: 16rem;
  --spacing-72: 18rem;
  --spacing-80: 20rem;
  --spacing-96: 24rem;

  /* Border Radius */
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius-base: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Box Shadow */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);

  --shadow-neumorphic-elevated: 6px 6px 12px #c2c9d1, -6px -6px 12px #ffffff;
  --shadow-neumorphic-inset: inset 6px 6px 12px #c2c9d1, inset -6px -6px 12px #ffffff;
  --shadow-neumorphic-container: 3px 3px 6px #c2c9d1, -3px -3px 6px #ffffff;

  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slower: 500ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Layout */
  --container-max-width: 1280px;
  --sidebar-width-expanded: 256px;
  --sidebar-width-collapsed: 64px;
  --header-height: 64px;
}