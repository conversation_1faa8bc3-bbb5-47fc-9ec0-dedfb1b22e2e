/* Tour highlight styles */
.tour-highlight {
  position: relative;
  z-index: 45 !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3),
    0 0 0 8px rgba(59, 130, 246, 0.1), 0 8px 32px rgba(59, 130, 246, 0.2) !important;
  border-radius: 8px;
  transition: all 0.3s ease;
  /* Removed animation: tourPulse 2s infinite; */
}

.tour-highlight::before {
  content: "";
  position: absolute;
  inset: -4px;
  border-radius: 12px;
  background: linear-gradient(
    45deg,
    rgba(59, 130, 246, 0.1),
    rgba(147, 197, 253, 0.1)
  );
  z-index: -1;
  /* Removed animation: tourGlow 2s infinite; */
}

/* Tour pulse animation */
@keyframes tourPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

/* Tour glow animation */
@keyframes tourGlow {
  0%,
  100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

/* Tour tooltip animations */
.tour-tooltip-enter {
  opacity: 0;
  transform: scale(0.9) translateY(10px);
}

.tour-tooltip-enter-active {
  opacity: 1;
  transform: scale(1) translateY(0);
  transition: all 0.3s ease;
}

.tour-tooltip-exit {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.tour-tooltip-exit-active {
  opacity: 0;
  transform: scale(0.9) translateY(10px);
  transition: all 0.3s ease;
}

/* Tour overlay styles */
.tour-overlay {
  backdrop-filter: blur(2px);
  background: rgba(0, 0, 0, 0.3);
}

/* Tour step indicator */
.tour-step-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Tour progress bar */
.tour-progress {
  width: 100%;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 16px;
}

.tour-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Tour button styles */
.tour-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  outline: none;
}

.tour-button-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.tour-button-primary:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.tour-button-secondary {
  background: rgba(0, 0, 0, 0.05);
  color: #6b7280;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.tour-button-secondary:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #374151;
}

.tour-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Tour skip button */
.tour-skip-button {
  position: absolute;
  top: 16px;
  right: 16px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  color: #6b7280;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.tour-skip-button:hover {
  background: rgba(255, 255, 255, 1);
  color: #374151;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive tour adjustments */
@media (max-width: 768px) {
  .tour-highlight {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3),
      0 0 0 6px rgba(59, 130, 246, 0.1), 0 4px 16px rgba(59, 130, 246, 0.2) !important;
  }

  .tour-tooltip {
    max-width: 280px !important;
    margin: 0 16px;
  }
}
