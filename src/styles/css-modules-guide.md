# CSS Modules Implementation Guide

## Overview

This project has been updated to use CSS Modules alongside the existing design system. This approach provides:

- **Scoped styles** - No more class name conflicts
- **Better maintainability** - Styles are co-located with components
- **Type safety** - CSS class names are typed (with TypeScript)
- **Design consistency** - Still uses the same design tokens
- **Performance** - Only loads styles for used components

## File Structure

```
src/
├── styles/
│   ├── design-tokens.module.css  # Shared design tokens
│   ├── index.css                # Global styles & CSS custom properties
│   ├── components.css            # Legacy component classes (being phased out)
│   └── css-modules-guide.md     # This guide
├── components/
│   ├── Button.module.css        # Button component styles
│   ├── Button.tsx               # Button component
│   ├── Header.module.css        # Header component styles
│   ├── Header.tsx               # Header component
│   └── [Component].module.css   # Other component modules
```

## CSS Modules Naming Convention

### File Naming
- Component CSS modules: `ComponentName.module.css`
- Shared modules: `name.module.css`

### Class Naming in CSS Modules
- Use camelCase: `.primaryButton`, `.cardHeader`, `.isActive`
- Use descriptive names: `.submitButton` not `.btn1`
- Use state modifiers: `.button.loading`, `.card.expanded`

### Example Component Structure

```tsx
// Button.tsx
import React from 'react';
import styles from './Button.module.css';

interface ButtonProps {
  variant?: 'primary' | 'secondary';
  size?: 'small' | 'medium' | 'large';
}

export function Button({ variant = 'primary', size = 'medium' }) {
  return (
    <button className={`${styles.btn} ${styles[variant]} ${styles[size]}`}>
      Click me
    </button>
  );
}
```

```css
/* Button.module.css */
.btn {
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-base);
  border: none;
  cursor: pointer;
}

.primary {
  background: var(--color-primary-600);
  color: white;
}

.primary:hover {
  background: var(--color-primary-700);
}

.secondary {
  background: var(--color-secondary-600);
  color: white;
}

.small {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
}

.medium {
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
}

.large {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-lg);
}
```

## Design Token Integration

### Using CSS Custom Properties
CSS Modules work seamlessly with CSS custom properties:

```css
/* Component.module.css */
.component {
  background: var(--color-primary-500);
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}
```

### Accessing Design Tokens in JavaScript
For dynamic styles, import the design tokens module:

```tsx
import tokens from '../styles/design-tokens.module.css';

// Use in component
const dynamicStyle = {
  backgroundColor: tokens.colorPrimary500,
  padding: tokens.spacing4
};
```

## Migration Strategy

### Phase 1: Core Components ✅
- [x] Header
- [x] Button
- [x] GlassyButton
- [x] Modal
- [x] PersonaCard

### Phase 2: Layout Components
- [ ] Sidebar (partially done)
- [ ] Layout
- [ ] Navigation components

### Phase 3: Page Components
- [ ] PersonaList
- [ ] PersonaDetail
- [ ] Admin pages
- [ ] Form components

### Phase 4: Utility Components
- [ ] Loading states
- [ ] Error boundaries
- [ ] Tooltips and popovers

## Best Practices

### 1. Use Composition Over Large Classes
```css
/* Good */
.card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.cardHeader {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--color-gray-200);
}

.cardBody {
  padding: var(--spacing-4);
}
```

```css
/* Avoid */
.cardWithHeaderAndBodyAndFooter {
  /* too specific */
}
```

### 2. Use Variant Classes
```css
.button {
  /* base styles */
}

.primary {
  background: var(--color-primary-600);
}

.secondary {
  background: var(--color-secondary-600);
}

.small {
  padding: var(--spacing-2);
}

.large {
  padding: var(--spacing-4);
}
```

### 3. Handle State Classes
```css
.button {
  /* base styles */
}

.button:hover {
  transform: translateY(-1px);
}

.button:disabled {
  opacity: 0.5;
  pointer-events: none;
}

.loading {
  position: relative;
}

.loading::after {
  /* loading spinner */
}
```

### 4. Responsive Design
```css
.component {
  padding: var(--spacing-4);
}

@media (max-width: 768px) {
  .component {
    padding: var(--spacing-2);
  }
}
```

## TypeScript Integration

### Enabling CSS Modules Types
Vite automatically generates types for CSS modules. For better IntelliSense:

```typescript
// vite-env.d.ts
declare module '*.module.css' {
  const classes: { readonly [key: string]: string };
  export default classes;
}
```

### Using with TypeScript
```tsx
import styles from './Component.module.css';

// TypeScript will provide autocomplete for class names
const className = styles.primaryButton; // ✅ Typed
```

## Debugging CSS Modules

### Development Class Names
In development, class names include the component name:
- `.Button_primary_abc123`
- `.Header_title_def456`

### Production Class Names
In production, class names are minified:
- `.a`
- `.b`

### Using Browser DevTools
1. Inspect element to see the generated class name
2. Use the source maps to find the original CSS file
3. Use React DevTools to see component props and state

## Performance Considerations

### Code Splitting
CSS modules are automatically code-split by component:
- Only used components load their styles
- Unused styles are eliminated
- Better caching (styles change independently)

### Bundle Size
- CSS modules can reduce bundle size
- Dead code elimination works on CSS
- Shared styles are extracted automatically

## Common Patterns

### Conditional Classes
```tsx
const buttonClasses = [
  styles.btn,
  variant && styles[variant],
  size && styles[size],
  loading && styles.loading,
  disabled && styles.disabled,
  className
].filter(Boolean).join(' ');
```

### Dynamic Styles
```tsx
// For truly dynamic values, use inline styles
<div 
  className={styles.progressBar}
  style={{ width: `${percentage}%` }}
/>
```

### Combining with Tailwind (if needed)
```tsx
// CSS modules for component structure
// Tailwind for utilities
<div className={`${styles.card} flex items-center justify-between`}>
```

## Migration Checklist

When migrating a component to CSS modules:

- [ ] Create `Component.module.css` file
- [ ] Move styles from global CSS to module
- [ ] Update component to import styles
- [ ] Replace string class names with `styles.className`
- [ ] Test component in different states
- [ ] Update any tests that rely on class names
- [ ] Remove old global styles (if no longer used)

## Tools and Extensions

### VS Code Extensions
- CSS Modules: Provides IntelliSense for CSS modules
- CSS Navigation: Jump between CSS and TypeScript files

### Build Tools
- Vite handles CSS modules out of the box
- PostCSS processes the CSS modules
- TypeScript generates type definitions

## Examples

See the following components for complete examples:
- `src/components/Button.tsx` - Full-featured button with variants
- `src/components/Header.tsx` - Complex component with multiple elements
- `src/components/Modal.tsx` - Component with animations and states
- `src/components/PersonaCard.tsx` - Card component with dynamic content