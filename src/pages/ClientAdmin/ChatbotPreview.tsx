import React from 'react';
import styles from './ClientAdmin.module.css';

export function ChatbotPreview() {
  return (
    <div className={styles.chatInterface}>
      <div className={styles.chatHeader}>
        <div className={styles.chatTitle}>Research Assistant</div>
        <div className={styles.chatStatus}>● Online</div>
      </div>
      <div className={styles.chatMessages}>
        <div className={styles.messageUser}>
          <div className={styles.messageContent}>Show me sales data for Q3 2024</div>
        </div>
        <div className={styles.messageBot}>
          <div className={styles.messageContent}>
            I found 3 relevant datasets for Q3 2024 sales. Here's a summary:
            <br />• Total revenue: $2.4M (+12% vs Q2)
            <br />• Top product: Analytics Pro (45% of sales)
            <br />• Growth regions: West Coast (+18%), Northeast (+9%)
          </div>
        </div>
        <div className={styles.messageUser}>
          <div className={styles.messageContent}>What about customer satisfaction scores?</div>
        </div>
        <div className={styles.messageBot}>
          <div className={styles.messageContent}>
            Customer satisfaction for Q3 2024:
            <br />• Overall CSAT: 4.2/5 (up from 3.9 in Q2)
            <br />• Support response time: 2.3 hours avg
            <br />• Net Promoter Score: 67 (+5 points)
            <br />• Top complaint: Feature request backlog
          </div>
        </div>
      </div>
      <div className={styles.chatInput}>
        <input placeholder="Ask about your research data..." className={styles.inputField} />
        <button className={styles.sendButton}>Send</button>
      </div>
    </div>
  );
}
