import React from 'react';
import { TrendingUp, TrendingDown, Users, DollarSign, Activity, Eye } from 'lucide-react';
import styles from './ClientAdmin.module.css';

export function AnalyticsPreview() {
  return (
    <div className={styles.analyticsInterface}>
      <div className={styles.analyticsHeader}>
        <div className={styles.headerTitle}>Dashboard Overview</div>
        <div className={styles.timeFilter}>
          <select className={styles.filterSelect}>
            <option>Last 30 days</option>
            <option>Last 90 days</option>
            <option>Last year</option>
          </select>
        </div>
      </div>
      
      <div className={styles.analyticsGrid}>
        <div className={styles.analyticsCard}>
          <div className={styles.cardHeader}>
            <DollarSign className={styles.cardIcon} />
            <div className={styles.cardTitle}>Revenue</div>
          </div>
          <div className={styles.cardValue}>$2.4M</div>
          <div className={styles.cardChange}>
            <TrendingUp className={styles.changeIcon} />
            <span>+12% vs last month</span>
          </div>
        </div>
        
        <div className={styles.analyticsCard}>
          <div className={styles.cardHeader}>
            <Users className={styles.cardIcon} />
            <div className={styles.cardTitle}>Active Users</div>
          </div>
          <div className={styles.cardValue}>1,247</div>
          <div className={styles.cardChange}>
            <TrendingUp className={styles.changeIcon} />
            <span>+8% vs last month</span>
          </div>
        </div>
        
        <div className={styles.analyticsCard}>
          <div className={styles.cardHeader}>
            <Activity className={styles.cardIcon} />
            <div className={styles.cardTitle}>Conversion Rate</div>
          </div>
          <div className={styles.cardValue}>3.2%</div>
          <div className={styles.cardChange}>
            <TrendingDown className={styles.changeIcon} />
            <span>-2% vs last month</span>
          </div>
        </div>
        
        <div className={styles.analyticsCard}>
          <div className={styles.cardHeader}>
            <Eye className={styles.cardIcon} />
            <div className={styles.cardTitle}>Page Views</div>
          </div>
          <div className={styles.cardValue}>45.2K</div>
          <div className={styles.cardChange}>
            <TrendingUp className={styles.changeIcon} />
            <span>+15% vs last month</span>
          </div>
        </div>
      </div>
      
      <div className={styles.chartSection}>
        <div className={styles.chartHeader}>
          <div className={styles.chartTitle}>Revenue Trend</div>
          <div className={styles.chartPeriod}>Last 6 months</div>
        </div>
        <div className={styles.chartContainer}>
          <div className={styles.chartBars}>
            <div className={styles.chartBar} style={{height: '45%'}}>
              <div className={styles.barLabel}>Jul</div>
            </div>
            <div className={styles.chartBar} style={{height: '60%'}}>
              <div className={styles.barLabel}>Aug</div>
            </div>
            <div className={styles.chartBar} style={{height: '55%'}}>
              <div className={styles.barLabel}>Sep</div>
            </div>
            <div className={styles.chartBar} style={{height: '80%'}}>
              <div className={styles.barLabel}>Oct</div>
            </div>
            <div className={styles.chartBar} style={{height: '90%'}}>
              <div className={styles.barLabel}>Nov</div>
            </div>
            <div className={styles.chartBar} style={{height: '100%'}}>
              <div className={styles.barLabel}>Dec</div>
            </div>
          </div>
          <div className={styles.chartValues}>
            <span>$1.8M</span>
            <span>$2.1M</span>
            <span>$2.0M</span>
            <span>$2.3M</span>
            <span>$2.4M</span>
            <span>$2.6M</span>
          </div>
        </div>
      </div>
      
      <div className={styles.insightsList}>
        <div className={styles.insightItem}>
          <div className={styles.insightIcon}>💡</div>
          <div className={styles.insightText}>
            <strong>Peak Performance:</strong> December shows highest revenue growth at 15%
          </div>
        </div>
        <div className={styles.insightItem}>
          <div className={styles.insightIcon}>📈</div>
          <div className={styles.insightText}>
            <strong>User Engagement:</strong> Mobile users increased by 23% this quarter
          </div>
        </div>
        <div className={styles.insightItem}>
          <div className={styles.insightIcon}>🎯</div>
          <div className={styles.insightText}>
            <strong>Opportunity:</strong> Conversion rate has room for improvement in Q1
          </div>
        </div>
      </div>
    </div>
  );
}
