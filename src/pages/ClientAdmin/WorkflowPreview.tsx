import React from 'react';
import { Play, Pause, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import styles from './ClientAdmin.module.css';

export function WorkflowPreview() {
  return (
    <div className={styles.workflowInterface}>
      <div className={styles.workflowHeader}>
        <div className={styles.headerTitle}>Active Workflows</div>
        <div className={styles.workflowStats}>
          <span className={styles.statBadge}>3 Active</span>
          <span className={styles.statBadge}>1 Pending</span>
        </div>
      </div>
      
      <div className={styles.workflowList}>
        <div className={styles.workflowItem}>
          <div className={styles.workflowTitle}>Market Research Analysis</div>
          <div className={styles.workflowProgress}>
            <div className={styles.progressBar}>
              <div className={styles.progressFill} style={{width: '66%'}}></div>
            </div>
            <span className={styles.progressText}>2 of 3 steps complete</span>
          </div>
          <div className={styles.workflowSteps}>
            <div className={styles.workflowStep}>
              <div className={`${styles.stepIndicator} ${styles.stepCompleted}`}>
                <CheckCircle className={styles.stepIcon} />
              </div>
              <div className={styles.stepContent}>
                <div className={styles.stepTitle}>Data Collection</div>
                <div className={styles.stepStatus}>Completed by Alice Davis</div>
                <div className={styles.stepTime}>2 hours ago</div>
              </div>
            </div>
            
            <div className={styles.workflowStep}>
              <div className={`${styles.stepIndicator} ${styles.stepActive}`}>
                <Play className={styles.stepIcon} />
              </div>
              <div className={styles.stepContent}>
                <div className={styles.stepTitle}>Analysis & Processing</div>
                <div className={styles.stepStatus}>In Progress - John Smith</div>
                <div className={styles.stepTime}>Started 30 min ago</div>
              </div>
            </div>
            
            <div className={styles.workflowStep}>
              <div className={styles.stepIndicator}>
                <Clock className={styles.stepIcon} />
              </div>
              <div className={styles.stepContent}>
                <div className={styles.stepTitle}>Review & Approval</div>
                <div className={styles.stepStatus}>Pending</div>
                <div className={styles.stepTime}>Estimated: 1 hour</div>
              </div>
            </div>
          </div>
        </div>
        
        <div className={styles.workflowItem}>
          <div className={styles.workflowTitle}>Customer Feedback Survey</div>
          <div className={styles.workflowProgress}>
            <div className={styles.progressBar}>
              <div className={styles.progressFill} style={{width: '100%'}}></div>
            </div>
            <span className={styles.progressText}>Complete</span>
          </div>
          <div className={styles.workflowActions}>
            <button className={styles.actionButton}>View Results</button>
            <button className={styles.actionButton}>Export Data</button>
          </div>
        </div>
        
        <div className={`${styles.workflowItem} ${styles.workflowOverdue}`}>
          <div className={styles.workflowTitle}>
            <AlertCircle className={styles.alertIcon} />
            Quarterly Report Generation
          </div>
          <div className={styles.workflowProgress}>
            <div className={styles.progressBar}>
              <div className={styles.progressFill} style={{width: '25%'}}></div>
            </div>
            <span className={styles.progressText}>Overdue by 2 days</span>
          </div>
        </div>
      </div>
    </div>
  );
}
