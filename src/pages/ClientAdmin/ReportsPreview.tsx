import React from 'react';
import { FileText, Calendar, Mail, Download, Clock, CheckCircle } from 'lucide-react';
import styles from './ClientAdmin.module.css';

export function ReportsPreview() {
  return (
    <div className={styles.reportsInterface}>
      <div className={styles.reportsHeader}>
        <div className={styles.headerTitle}>Scheduled Reports</div>
        <div className={styles.headerActions}>
          <button className={styles.addButton}>+ New Report</button>
        </div>
      </div>
      
      <div className={styles.reportsList}>
        <div className={styles.reportItem}>
          <div className={styles.reportIcon}>
            <FileText className={styles.iconSvg} />
          </div>
          <div className={styles.reportInfo}>
            <div className={styles.reportName}>Weekly Sales Summary</div>
            <div className={styles.reportDetails}>
              <Calendar className={styles.detailIcon} />
              <span>Every Monday at 9:00 AM</span>
            </div>
            <div className={styles.reportDetails}>
              <Mail className={styles.detailIcon} />
              <span>5 recipients</span>
            </div>
          </div>
          <div className={styles.reportStatus}>
            <div className={styles.statusBadge}>Next: Tomorrow</div>
            <div className={styles.reportActions}>
              <button className={styles.actionBtn}>
                <Download className={styles.actionIcon} />
              </button>
              <button className={styles.actionBtn}>
                <Mail className={styles.actionIcon} />
              </button>
            </div>
          </div>
        </div>
        
        <div className={styles.reportItem}>
          <div className={styles.reportIcon}>
            <FileText className={styles.iconSvg} />
          </div>
          <div className={styles.reportInfo}>
            <div className={styles.reportName}>Monthly Analytics Dashboard</div>
            <div className={styles.reportDetails}>
              <Calendar className={styles.detailIcon} />
              <span>1st of each month at 8:00 AM</span>
            </div>
            <div className={styles.reportDetails}>
              <Mail className={styles.detailIcon} />
              <span>12 recipients</span>
            </div>
          </div>
          <div className={styles.reportStatus}>
            <div className={styles.statusBadge}>Next: Dec 1</div>
            <div className={styles.reportActions}>
              <button className={styles.actionBtn}>
                <Download className={styles.actionIcon} />
              </button>
              <button className={styles.actionBtn}>
                <Mail className={styles.actionIcon} />
              </button>
            </div>
          </div>
        </div>
        
        <div className={styles.reportItem}>
          <div className={styles.reportIcon}>
            <FileText className={styles.iconSvg} />
          </div>
          <div className={styles.reportInfo}>
            <div className={styles.reportName}>Customer Satisfaction Report</div>
            <div className={styles.reportDetails}>
              <Calendar className={styles.detailIcon} />
              <span>Quarterly - Next: Jan 15</span>
            </div>
            <div className={styles.reportDetails}>
              <Mail className={styles.detailIcon} />
              <span>8 recipients</span>
            </div>
          </div>
          <div className={styles.reportStatus}>
            <div className={`${styles.statusBadge} ${styles.statusComplete}`}>
              <CheckCircle className={styles.statusIcon} />
              Last sent: Nov 15
            </div>
          </div>
        </div>
      </div>
      
      <div className={styles.reportsStats}>
        <div className={styles.statCard}>
          <div className={styles.statValue}>24</div>
          <div className={styles.statLabel}>Reports Sent This Month</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>98%</div>
          <div className={styles.statLabel}>Delivery Success Rate</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>3.2s</div>
          <div className={styles.statLabel}>Avg Generation Time</div>
        </div>
      </div>
    </div>
  );
}
