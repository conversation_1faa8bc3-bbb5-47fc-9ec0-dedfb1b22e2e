/* Container and layout */
.container {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  min-height: 600px;
}

/* Fade animation */
.fadeSection {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

/* Typography */
.sectionTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  max-width: 250px;
}

.sectionSubtitle {
  color: white;
  margin: 0;
}

/* Icon containers and icons */
.iconContainer {
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.icon {
  height: 1.5rem;
  width: 1.5rem;
}

/* Color variants for icons */
.iconContainerBlue {
  background-color: #eff6ff;
}

.iconBlue {
  color: #2563eb;
}

.iconContainerGreen {
  background-color: #f0fdf4;
}

.iconGreen {
  color: #16a34a;
}

.iconContainerPurple {
  background-color: #faf5ff;
}

.iconPurple {
  color: #9333ea;
}

.iconContainerOrange {
  background-color: #fff7ed;
}

.iconOrange {
  color: #ea580c;
}

.iconContainerRed {
  background-color: #fef2f2;
}

.iconRed {
  color: #dc2626;
}

.sectionGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  align-items: start;
}

@media (min-width: 1024px) {
  .sectionGrid {
    grid-template-columns: repeat(2, 1fr);
    align-items: center;
  }
}

/* Section header */
.sectionHeader {
  margin-bottom: 1.5rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

/* Features */
.featuresContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  color: white;
}

.featureParagraph {
  line-height: 1.6;
  margin: 0;
  color: white;
}

/* UI Preview section */
.statusTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.uiPreview {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  background-color: #ffffff;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Basic styles for preview components */
.chatInterface,
.teamInterface,
.workflowInterface,
.reportsInterface,
.analyticsInterface {
  max-width: 450px;
  margin: 0 auto;
  font-family: inherit;
}

/* Common header styles */
.headerTitle {
  font-weight: 600;
  color: #111827;
  font-size: 0.875rem;
}

.headerActions {
  display: flex;
  gap: 0.5rem;
}

.addButton {
  padding: 0.375rem 0.75rem;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  cursor: pointer;
}

/* Common list styles */
.teamList,
.workflowList,
.reportsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Common item styles */
.teamMember,
.workflowItem,
.reportItem {
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

/* Common status styles */
.memberStatus,
.statusBadge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  background-color: #dcfce7;
  color: #16a34a;
}
.container {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Typography */
.sectionTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  
}

.sectionSubtitle {
  color: white;
  margin-top: 0.25rem;
}

/* Icon containers and icons */
.iconContainer {
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.icon {
  height: 1.5rem;
  width: 1.5rem;
}

/* Color variants for icons */
.iconContainerBlue {
  background-color: #eff6ff;
}

.iconBlue {
  color: #2563eb;
}

.iconContainerGreen {
  background-color: #f0fdf4;
}

.iconGreen {
  color: #16a34a;
}

.iconContainerPurple {
  background-color: #faf5ff;
}

.iconPurple {
  color: #9333ea;
}

.iconContainerOrange {
  background-color: #fff7ed;
}

.iconOrange {
  color: #ea580c;
}

.iconContainerRed {
  background-color: #fef2f2;
}

.iconRed {
  color: #dc2626;
}

/* Section card */
/* Section card */
.sectionCard {
  background: #ffffff20;
  backdrop-filter: blur(6px);
  border-radius: 0.75rem;
  /* box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); */
  padding: 1.5rem;
}

.sectionGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 1024px) {
  .sectionGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Features */
.featuresContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  color: #374151;
}

.featureParagraph {
  line-height: 1.6;
  margin: 0;
}

/* UI Preview section */
.statusTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
}

.uiPreview {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  background-color: #ffffff;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Chat Interface */
.chatInterface {
  max-width: 400px;
  margin: 0 auto;
}

.chatHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1rem;
}

.chatTitle {
  font-weight: 600;
  color: #111827;
}

.chatStatus {
  font-size: 0.875rem;
  color: #16a34a;
}

.chatMessages {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
  min-height: 120px;
}

.messageUser {
  display: flex;
  justify-content: flex-end;
}

.messageBot {
  display: flex;
  justify-content: flex-start;
}

.messageUser .messageContent {
  background-color: #2563eb;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 1rem 1rem 0.25rem 1rem;
  max-width: 80%;
  font-size: 0.875rem;
}

.messageBot .messageContent {
  background-color: #f3f4f6;
  color: #374151;
  padding: 0.5rem 0.75rem;
  border-radius: 1rem 1rem 1rem 0.25rem;
  max-width: 80%;
  font-size: 0.875rem;
  line-height: 1.4;
}

.chatInput {
  display: flex;
  gap: 0.5rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e5e7eb;
}

.inputField {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.sendButton {
  padding: 0.5rem 1rem;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
}

/* Team Interface */
.teamInterface {
  max-width: 450px;
  margin: 0 auto;
}

.teamHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.headerTitle {
  font-weight: 600;
  color: #111827;
}

.headerActions {
  display: flex;
  gap: 0.5rem;
}

.addButton {
  padding: 0.375rem 0.75rem;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  cursor: pointer;
}

.teamList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.teamMember {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.memberAvatar {
  width: 2.5rem;
  height: 2.5rem;
  background-color: #2563eb;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.memberInfo {
  flex: 1;
}

.memberName {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
}

.memberRole {
  color: #6b7280;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.memberPermissions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.permissionIcon {
  width: 0.75rem;
  height: 0.75rem;
}

.memberStats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.statIcon {
  width: 0.75rem;
  height: 0.75rem;
}

.memberStatus {
  font-size: 0.75rem;
  color: #16a34a;
  background-color: #dcfce7;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
}

.statusAway {
  color: #ea580c;
  background-color: #fff7ed;
}

.teamSummary {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem;
  background-color: #f3f4f6;
  border-radius: 0.5rem;
  border-top: 1px solid #e5e7eb;
}

.summaryItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.summaryLabel {
  font-size: 0.75rem;
  color: #6b7280;
}

.summaryValue {
  font-weight: 600;
  color: #111827;
}

/* Workflow Interface */
.workflowInterface {
  max-width: 450px;
  margin: 0 auto;
}

.workflowHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.workflowStats {
  display: flex;
  gap: 0.5rem;
}

.statBadge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  background-color: #dbeafe;
  color: #1d4ed8;
  border-radius: 0.375rem;
}

.workflowList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.workflowItem {
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.workflowOverdue {
  border-color: #fca5a5;
  background-color: #fef2f2;
}

.workflowTitle {
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.alertIcon {
  width: 1rem;
  height: 1rem;
  color: #dc2626;
}

.workflowProgress {
  margin-bottom: 0.75rem;
}

.progressBar {
  width: 100%;
  height: 0.5rem;
  background-color: #e5e7eb;
  border-radius: 0.25rem;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.progressFill {
  height: 100%;
  background-color: #2563eb;
  transition: width 0.3s ease;
}

.progressText {
  font-size: 0.75rem;
  color: #6b7280;
}

.workflowSteps {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.workflowStep {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.stepIndicator {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  background-color: #e5e7eb;
  color: #6b7280;
  flex-shrink: 0;
}

.stepCompleted {
  background-color: #16a34a;
  color: white;
}

.stepActive {
  background-color: #2563eb;
  color: white;
}

.stepIcon {
  width: 1rem;
  height: 1rem;
}

.stepContent {
  flex: 1;
  padding-top: 0.125rem;
}

.stepTitle {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.stepStatus {
  color: #6b7280;
  font-size: 0.75rem;
  margin-bottom: 0.125rem;
}

.stepTime {
  color: #9ca3af;
  font-size: 0.75rem;
}

.workflowActions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.actionButton {
  padding: 0.375rem 0.75rem;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  cursor: pointer;
}

/* Reports Interface */
.reportsInterface {
  max-width: 450px;
  margin: 0 auto;
}

.reportsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.reportsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.reportItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.reportIcon {
  width: 2.5rem;
  height: 2.5rem;
  background-color: #dbeafe;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.iconSvg {
  width: 1.25rem;
  height: 1.25rem;
  color: #2563eb;
}

.reportInfo {
  flex: 1;
}

.reportName {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.reportDetails {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.125rem;
}

.detailIcon {
  width: 0.75rem;
  height: 0.75rem;
}

.reportStatus {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.statusBadge {
  font-size: 0.75rem;
  color: #ea580c;
  background-color: #fff7ed;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.statusComplete {
  color: #16a34a;
  background-color: #dcfce7;
}

.statusIcon {
  width: 0.75rem;
  height: 0.75rem;
}

.reportActions {
  display: flex;
  gap: 0.25rem;
}

.actionBtn {
  padding: 0.25rem;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  cursor: pointer;
}

.actionIcon {
  width: 0.875rem;
  height: 0.875rem;
  color: #6b7280;
}

.reportsStats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #f3f4f6;
  border-radius: 0.5rem;
}

.statCard {
  text-align: center;
}

.statValue {
  font-size: 1.125rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.25rem;
}

.statLabel {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Analytics Interface */
.analyticsInterface {
  max-width: 450px;
  margin: 0 auto;
}

.analyticsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.timeFilter {
  display: flex;
  align-items: center;
}

.filterSelect {
  padding: 0.25rem 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  background-color: white;
}

.analyticsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.analyticsCard {
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.cardHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.cardIcon {
  width: 1rem;
  height: 1rem;
  color: #6b7280;
}

.cardTitle {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

.cardValue {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.25rem;
}

.cardChange {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.changeIcon {
  width: 0.75rem;
  height: 0.75rem;
}

.cardChange:has(.changeIcon[data-trend="up"]) {
  color: #16a34a;
}

.cardChange:has(.changeIcon[data-trend="down"]) {
  color: #dc2626;
}

.chartSection {
  margin-bottom: 1rem;
}

.chartHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.chartTitle {
  font-weight: 600;
  color: #111827;
  font-size: 0.875rem;
}

.chartPeriod {
  font-size: 0.75rem;
  color: #6b7280;
}

.chartContainer {
  background-color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

.chartBars {
  display: flex;
  align-items: end;
  justify-content: space-between;
  gap: 0.5rem;
  height: 80px;
  margin-bottom: 0.5rem;
}

.chartBar {
  flex: 1;
  background-color: #2563eb;
  border-radius: 0.125rem 0.125rem 0 0;
  min-height: 0.25rem;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.barLabel {
  font-size: 0.75rem;
  color: #6b7280;
  text-align: center;
  margin-top: 0.25rem;
}

.chartValues {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #6b7280;
}

.insightsList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: #f3f4f6;
  border-radius: 0.5rem;
}

.insightItem {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.insightIcon {
  font-size: 1rem;
  flex-shrink: 0;
}

.insightText {
  font-size: 0.75rem;
  color: #374151;
  line-height: 1.4;
}
