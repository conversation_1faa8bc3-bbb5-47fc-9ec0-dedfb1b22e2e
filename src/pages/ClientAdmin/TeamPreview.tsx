import React from 'react';
import { <PERSON>, <PERSON>, Clock, CheckCircle } from 'lucide-react';
import styles from './ClientAdmin.module.css';

export function TeamPreview() {
  return (
    <div className={styles.teamInterface}>
      <div className={styles.teamHeader}>
        <div className={styles.headerTitle}>Team Members</div>
        <div className={styles.headerActions}>
          <button className={styles.addButton}>+ Add Member</button>
        </div>
      </div>
      
      <div className={styles.teamList}>
        <div className={styles.teamMember}>
          <div className={styles.memberAvatar}>JS</div>
          <div className={styles.memberInfo}>
            <div className={styles.memberName}><PERSON></div>
            <div className={styles.memberRole}>Senior Researcher</div>
            <div className={styles.memberPermissions}>
              <Shield className={styles.permissionIcon} />
              <span>Admin Access</span>
            </div>
          </div>
          <div className={styles.memberStats}>
            <div className={styles.statItem}>
              <CheckCircle className={styles.statIcon} />
              <span>12 tasks</span>
            </div>
            <div className={styles.memberStatus}>Active</div>
          </div>
        </div>
        
        <div className={styles.teamMember}>
          <div className={styles.memberAvatar}>AD</div>
          <div className={styles.memberInfo}>
            <div className={styles.memberName}>Alice Davis</div>
            <div className={styles.memberRole}>Data Analyst</div>
            <div className={styles.memberPermissions}>
              <Users className={styles.permissionIcon} />
              <span>Editor Access</span>
            </div>
          </div>
          <div className={styles.memberStats}>
            <div className={styles.statItem}>
              <Clock className={styles.statIcon} />
              <span>8 tasks</span>
            </div>
            <div className={styles.memberStatus}>Active</div>
          </div>
        </div>
        
        <div className={styles.teamMember}>
          <div className={styles.memberAvatar}>MJ</div>
          <div className={styles.memberInfo}>
            <div className={styles.memberName}>Mike Johnson</div>
            <div className={styles.memberRole}>Research Assistant</div>
            <div className={styles.memberPermissions}>
              <Users className={styles.permissionIcon} />
              <span>Viewer Access</span>
            </div>
          </div>
          <div className={styles.memberStats}>
            <div className={styles.statItem}>
              <CheckCircle className={styles.statIcon} />
              <span>5 tasks</span>
            </div>
            <div className={`${styles.memberStatus} ${styles.statusAway}`}>Away</div>
          </div>
        </div>
      </div>
      
      <div className={styles.teamSummary}>
        <div className={styles.summaryItem}>
          <span className={styles.summaryLabel}>Total Members:</span>
          <span className={styles.summaryValue}>3</span>
        </div>
        <div className={styles.summaryItem}>
          <span className={styles.summaryLabel}>Active Tasks:</span>
          <span className={styles.summaryValue}>25</span>
        </div>
      </div>
    </div>
  );
}
