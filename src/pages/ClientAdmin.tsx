import React from 'react';
import { Bot, Users, Workflow, FileText, BarChart3 } from 'lucide-react';

// Admin sections data
const adminSections = [
  {
    id: 'ai-chatbot',
    title: 'AI Chatbot',
    description: 'Intelligent assistant for research insights and data analysis',
    icon: Bot,
    color: 'blue',
    content: {
      features: [
        'Natural language queries for research data',
        'Automated insight generation',
        'Real-time data analysis',
        'Customizable response templates',
        'Integration with research databases'
      ],
      status: 'Active',
      lastUpdated: '2 hours ago'
    }
  },
  {
    id: 'team-management',
    title: 'Team Management',
    description: 'Manage research team members, roles, and permissions',
    icon: Users,
    color: 'green',
    content: {
      features: [
        'Team member profiles and roles',
        'Permission management',
        'Collaboration tools',
        'Task assignment and tracking',
        'Performance analytics'
      ],
      status: 'Active',
      lastUpdated: '1 day ago'
    }
  },
  {
    id: 'workflow',
    title: 'Workflow',
    description: 'Streamline research processes and automate workflows',
    icon: Workflow,
    color: 'purple',
    content: {
      features: [
        'Custom workflow creation',
        'Automated task routing',
        'Progress tracking',
        'Approval processes',
        'Integration with external tools'
      ],
      status: 'Active',
      lastUpdated: '3 days ago'
    }
  },
  {
    id: 'auto-reports',
    title: 'Auto-Reports',
    description: 'Automated report generation and scheduling',
    icon: FileText,
    color: 'orange',
    content: {
      features: [
        'Scheduled report generation',
        'Custom report templates',
        'Data visualization',
        'Email distribution',
        'Report analytics'
      ],
      status: 'Active',
      lastUpdated: '1 week ago'
    }
  },
  {
    id: 'analytics',
    title: 'Analytics',
    description: 'Comprehensive analytics and insights dashboard',
    icon: BarChart3,
    color: 'red',
    content: {
      features: [
        'Real-time data visualization',
        'Custom dashboards',
        'Trend analysis',
        'Performance metrics',
        'Export capabilities'
      ],
      status: 'Active',
      lastUpdated: '5 minutes ago'
    }
  }
];

export function ClientAdmin() {
  return (
    <div className="space-y-16">
      {/* Admin Overview Header */}
      <section id="admin-overview" className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Admin Dashboard</h2>
          <p className="text-gray-500 mt-1">Manage your research operations and insights</p>
        </div>

        {/* Admin Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {adminSections.map((section, index) => {
            const Icon = section.icon;
            return (
              <div key={index} className="bg-white rounded-xl shadow-sm p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-2 bg-${section.color}-50 rounded-lg`}>
                    <Icon className={`h-6 w-6 text-${section.color}-600`} />
                  </div>
                  <span className="text-sm font-medium px-2.5 py-0.5 rounded-full bg-green-50 text-green-600">
                    {section.content.status}
                  </span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">{section.title}</h3>
                <p className="text-sm text-gray-500 mb-3">{section.description}</p>
                <p className="text-xs text-gray-400">Updated {section.content.lastUpdated}</p>
              </div>
            );
          })}
        </div>
      </section>

      {/* Individual Admin Sections */}
      {adminSections.map((section, index) => {
        const Icon = section.icon;
        return (
          <section key={section.id} id={section.id} className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <div className={`p-2 bg-${section.color}-50 rounded-lg`}>
                  <Icon className={`h-6 w-6 text-${section.color}-600`} />
                </div>
                {section.title}
              </h2>
              <p className="text-gray-500 mt-1">{section.description}</p>
            </div>

            <div className="bg-white rounded-xl shadow-sm p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Features List */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Features</h3>
                  <ul className="space-y-3">
                    {section.content.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start gap-3">
                        <div className={`w-2 h-2 bg-${section.color}-500 rounded-full mt-2 flex-shrink-0`}></div>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Status and Info */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">System Status</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <span className="text-gray-700">Status</span>
                      <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                        {section.content.status}
                      </span>
                    </div>
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <span className="text-gray-700">Last Updated</span>
                      <span className="text-gray-900 font-medium">{section.content.lastUpdated}</span>
                    </div>
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <span className="text-gray-700">Version</span>
                      <span className="text-gray-900 font-medium">v1.0.0</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex gap-3">
                  <button className={`px-4 py-2 bg-${section.color}-600 text-white rounded-lg hover:bg-${section.color}-700 transition-colors`}>
                    Configure {section.title}
                  </button>
                  <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    View Documentation
                  </button>
                  <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Support
                  </button>
                </div>
              </div>
            </div>
          </section>
        );
      })}
    </div>
  );
}
