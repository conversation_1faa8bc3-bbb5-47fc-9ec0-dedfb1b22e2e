import React from 'react';
import { Bot, Users, Workflow, FileText, BarChart3 } from 'lucide-react';
import styles from './ClientAdmin.module.css';

// Admin sections data
const adminSections = [
  {
    id: 'ai-chatbot',
    title: 'AI Chatbot',
    description: 'Intelligent assistant for research insights and data analysis',
    icon: Bot,
    color: 'blue',
    content: {
      features: [
        'Natural language queries for research data',
        'Automated insight generation',
        'Real-time data analysis',
        'Customizable response templates',
        'Integration with research databases'
      ],
      status: 'Active',
      lastUpdated: '2 hours ago'
    }
  },
  {
    id: 'team-management',
    title: 'Team Management',
    description: 'Manage research team members, roles, and permissions',
    icon: Users,
    color: 'green',
    content: {
      features: [
        'Team member profiles and roles',
        'Permission management',
        'Collaboration tools',
        'Task assignment and tracking',
        'Performance analytics'
      ],
      status: 'Active',
      lastUpdated: '1 day ago'
    }
  },
  {
    id: 'workflow',
    title: 'Workflow',
    description: 'Streamline research processes and automate workflows',
    icon: Workflow,
    color: 'purple',
    content: {
      features: [
        'Custom workflow creation',
        'Automated task routing',
        'Progress tracking',
        'Approval processes',
        'Integration with external tools'
      ],
      status: 'Active',
      lastUpdated: '3 days ago'
    }
  },
  {
    id: 'auto-reports',
    title: 'Auto-Reports',
    description: 'Automated report generation and scheduling',
    icon: FileText,
    color: 'orange',
    content: {
      features: [
        'Scheduled report generation',
        'Custom report templates',
        'Data visualization',
        'Email distribution',
        'Report analytics'
      ],
      status: 'Active',
      lastUpdated: '1 week ago'
    }
  },
  {
    id: 'analytics',
    title: 'Analytics',
    description: 'Comprehensive analytics and insights dashboard',
    icon: BarChart3,
    color: 'red',
    content: {
      features: [
        'Real-time data visualization',
        'Custom dashboards',
        'Trend analysis',
        'Performance metrics',
        'Export capabilities'
      ],
      status: 'Active',
      lastUpdated: '5 minutes ago'
    }
  }
];

export function ClientAdmin() {
  return (
    <div className={styles.container}>

      {/* Individual Admin Sections */}
      {adminSections.map((section) => {
        const Icon = section.icon;
        return (
          <section key={section.id} id={section.id} className={styles.section}>
            <div>
              <h2 className={styles.sectionTitle}>
                <div className={`${styles.iconContainer} ${styles[`iconContainer${section.color.charAt(0).toUpperCase() + section.color.slice(1)}`]}`}>
                  <Icon className={`${styles.icon} ${styles[`icon${section.color.charAt(0).toUpperCase() + section.color.slice(1)}`]}`} />
                </div>
                {section.title}
              </h2>
              <p className={styles.sectionSubtitle}>{section.description}</p>
            </div>

            <div className={styles.sectionCard}>
              <div className={styles.sectionGrid}>
                {/* Features List */}
                <div>
                  <h3 className={styles.featuresTitle}>Key Features</h3>
                  <ul className={styles.featuresList}>
                    {section.content.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className={styles.featureItem}>
                        <div className={`${styles.featureBullet} ${styles[`featureBullet${section.color.charAt(0).toUpperCase() + section.color.slice(1)}`]}`}></div>
                        <span className={styles.featureText}>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Status and Info */}
                <div>
                  <h3 className={styles.statusTitle}>System Status</h3>
                  <div className={styles.statusList}>
                    <div className={styles.statusItem}>
                      <span className={styles.statusLabel}>Status</span>
                      <span className={styles.statusValue}>
                        {section.content.status}
                      </span>
                    </div>
                    <div className={styles.statusItem}>
                      <span className={styles.statusLabel}>Last Updated</span>
                      <span className={styles.statusValueBold}>{section.content.lastUpdated}</span>
                    </div>
                    <div className={styles.statusItem}>
                      <span className={styles.statusLabel}>Version</span>
                      <span className={styles.statusValueBold}>v1.0.0</span>
                    </div>
                  </div>
                </div>
              </div>


            </div>
          </section>
        );
      })}
    </div>
  );
}
