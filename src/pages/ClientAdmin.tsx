import React, { useEffect, useRef } from 'react';
import { Bot, Users, Workflow, FileText, BarChart3 } from 'lucide-react';
import styles from './ClientAdmin/ClientAdmin.module.css';
import { ChatbotPreview } from './ClientAdmin/ChatbotPreview';
import { TeamPreview } from './ClientAdmin/TeamPreview';
import { WorkflowPreview } from './ClientAdmin/WorkflowPreview';
import { ReportsPreview } from './ClientAdmin/ReportsPreview';
import { AnalyticsPreview } from './ClientAdmin/AnalyticsPreview';

// Admin sections data
const adminSections = [
  {
    id: 'ai-chatbot',
    title: 'AI Chatbot',
    description: 'Intelligent assistant for research insights and data analysis',
    icon: Bot,
    color: 'blue',
    content: {
      features: [
        'Users can query research data using natural language instead of complex database syntax. The system processes these queries and returns relevant data in an understandable format.',
        'The AI automatically analyzes data patterns and generates insights without manual intervention. Response formats can be customized to match specific reporting needs and preferences.'
      ],
      status: 'Active',
      lastUpdated: '2 hours ago'
    }
  },
  {
    id: 'team-management',
    title: 'Team Management',
    description: 'Manage research team members, roles, and permissions',
    icon: Users,
    color: 'green',
    content: {
      features: [
        'Each team member has a profile containing their role, responsibilities, and access permissions. Access to different system areas is controlled through granular permission settings.',
        'Team members can share findings, comment on research, and collaborate on projects within the platform. Tasks are assigned to specific team members with deadlines and progress tracking.'
      ],
      status: 'Active',
      lastUpdated: '1 day ago'
    }
  },
  {
    id: 'workflow',
    title: 'Workflow',
    description: 'Streamline research processes and automate workflows',
    icon: Workflow,
    color: 'purple',
    content: {
      features: [
        'Administrators can create custom workflows that define how research tasks move through different stages. Tasks are automatically assigned to appropriate team members based on predefined rules and current workloads.',
        'Real-time dashboards show the status of all active workflows and individual tasks. Certain workflow steps require approval from designated reviewers before proceeding to the next stage.'
      ],
      status: 'Active',
      lastUpdated: '3 days ago'
    }
  },
  {
    id: 'auto-reports',
    title: 'Auto-Reports',
    description: 'Automated report generation and scheduling',
    icon: FileText,
    color: 'orange',
    content: {
      features: [
        'Reports are generated automatically according to predefined schedules, such as daily, weekly, or monthly intervals. Report layouts and content can be customized using templates that define data sources, formatting, and presentation style.',
        'Charts, graphs, and tables are automatically created from the underlying data to illustrate key findings. Completed reports are automatically sent to designated recipients via email with customizable subject lines and messages.'
      ],
      status: 'Active',
      lastUpdated: '1 week ago'
    }
  },
  {
    id: 'analytics',
    title: 'Analytics',
    description: 'Comprehensive analytics and insights dashboard',
    icon: BarChart3,
    color: 'red',
    content: {
      features: [
        'Charts and graphs update immediately as new data enters the system, providing current information at all times. Users can create personalized dashboards by selecting relevant metrics and arranging visual components according to their preferences.',
        'Historical data is analyzed to identify patterns, trends, and anomalies over specified time periods. Key performance indicators are calculated and displayed with benchmarks and target values for easy assessment.'
      ],
      status: 'Active',
      lastUpdated: '5 minutes ago'
    }
  }
];

export function ClientAdmin() {
  const sectionRefs = useRef<(HTMLElement | null)[]>([]);

  useEffect(() => {
    const observerOptions = {
      threshold: [0, 0.1, 0.5, 0.9, 1],
      rootMargin: '-10% 0px -10% 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        const element = entry.target as HTMLElement;
        const ratio = entry.intersectionRatio;

        // Calculate opacity based on intersection ratio
        let opacity = 0;
        if (ratio > 0.1) {
          opacity = Math.min(1, (ratio - 0.1) / 0.4); // Fade in from 10% to 50% visibility
        }

        // Apply opacity and transform
        element.style.opacity = opacity.toString();
        element.style.transform = `translateY(${(1 - opacity) * 20}px)`;
      });
    }, observerOptions);

    // Observe all sections
    sectionRefs.current.forEach((section) => {
      if (section) {
        observer.observe(section);
      }
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <div className={styles.container}>

      {/* Individual Admin Sections */}
      {adminSections.map((section, index) => {
        const Icon = section.icon;
        return (
          <section
            key={section.id}
            id={section.id}
            className={`${styles.section} ${styles.fadeSection}`}
            ref={(el) => (sectionRefs.current[index] = el)}
          >
            <div className={styles.sectionCard}>
              <div className={styles.sectionGrid}>
                {/* Features Description */}
                <div>
                  <div className={styles.sectionHeader}>
                    <h2 className={styles.sectionTitle}>
                      <div className={`${styles.iconContainer} ${styles[`iconContainer${section.color.charAt(0).toUpperCase() + section.color.slice(1)}`]}`}>
                        <Icon className={`${styles.icon} ${styles[`icon${section.color.charAt(0).toUpperCase() + section.color.slice(1)}`]}`} />
                      </div>
                      {section.title}
                    </h2>
                    <p className={styles.sectionSubtitle}>{section.description}</p>
                  </div>

                  <div className={styles.featuresContainer}>
                    {section.content.features.map((feature, featureIndex) => (
                      <p key={featureIndex} className={styles.featureParagraph}>
                        {feature}
                      </p>
                    ))}
                  </div>
                </div>

                {/* Example UI */}
                <div>
                  <h3 className={styles.statusTitle}>Example UI</h3>
                  <div className={styles.uiPreview}>
                    {section.id === 'ai-chatbot' && <ChatbotPreview />}
                    {section.id === 'team-management' && <TeamPreview />}
                    {section.id === 'workflow' && <WorkflowPreview />}
                    {section.id === 'auto-reports' && <ReportsPreview />}
                    {section.id === 'analytics' && <AnalyticsPreview />}
                  </div>
                </div>
              </div>
            </div>
          </section>
        );
      })}
    </div>
  );
}
