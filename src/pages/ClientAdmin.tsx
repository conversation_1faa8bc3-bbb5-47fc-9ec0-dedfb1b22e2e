import React from 'react';
import { Bo<PERSON>, Users, Workflow, FileText, BarChart3 } from 'lucide-react';
import styles from './ClientAdmin.module.css';

// Admin sections data
const adminSections = [
  {
    id: 'ai-chatbot',
    title: 'AI Chatbot',
    description: 'Intelligent assistant for research insights and data analysis',
    icon: Bot,
    color: 'blue',
    content: {
      features: [
        'Users can query research data using natural language instead of complex database syntax. The system processes these queries and returns relevant data in an understandable format.',
        'The AI automatically analyzes data patterns and generates insights without manual intervention. These insights are presented as summaries with supporting data points.',
        'Data analysis occurs as new information enters the system, providing immediate results. Users receive updated analysis within seconds of data changes.',
        'Response formats can be customized to match specific reporting needs and preferences. Templates ensure consistent output across different types of queries.',
        'The chatbot connects directly to existing research databases and data sources. This integration allows seamless access to all available research information.'
      ],
      status: 'Active',
      lastUpdated: '2 hours ago'
    }
  },
  {
    id: 'team-management',
    title: 'Team Management',
    description: 'Manage research team members, roles, and permissions',
    icon: Users,
    color: 'green',
    content: {
      features: [
        'Each team member has a profile containing their role, responsibilities, and access permissions. Administrators can modify these profiles and assign specific roles as needed.',
        'Access to different system areas is controlled through granular permission settings. Permissions can be assigned individually or through role-based groups.',
        'Team members can share findings, comment on research, and collaborate on projects within the platform. Communication tools include messaging, file sharing, and project discussions.',
        'Tasks are assigned to specific team members with deadlines and priority levels. Progress tracking shows completion status and identifies potential bottlenecks.',
        'System generates reports on team productivity, task completion rates, and individual performance metrics. These analytics help identify areas for improvement and resource allocation.'
      ],
      status: 'Active',
      lastUpdated: '1 day ago'
    }
  },
  {
    id: 'workflow',
    title: 'Workflow',
    description: 'Streamline research processes and automate workflows',
    icon: Workflow,
    color: 'purple',
    content: {
      features: [
        'Administrators can create custom workflows that define how research tasks move through different stages. These workflows specify required actions, responsible parties, and completion criteria for each step.',
        'Tasks are automatically assigned to appropriate team members based on predefined rules and current workloads. The system considers factors like expertise, availability, and priority when routing tasks.',
        'Real-time dashboards show the status of all active workflows and individual tasks. Progress indicators highlight completed steps, current bottlenecks, and upcoming deadlines.',
        'Certain workflow steps require approval from designated reviewers before proceeding to the next stage. The approval process includes notification systems and escalation procedures for overdue approvals.',
        'Workflows can trigger actions in external systems like project management tools, databases, or communication platforms. These integrations ensure data consistency across all connected systems.'
      ],
      status: 'Active',
      lastUpdated: '3 days ago'
    }
  },
  {
    id: 'auto-reports',
    title: 'Auto-Reports',
    description: 'Automated report generation and scheduling',
    icon: FileText,
    color: 'orange',
    content: {
      features: [
        'Reports are generated automatically according to predefined schedules, such as daily, weekly, or monthly intervals. The system runs these reports during off-peak hours to minimize performance impact.',
        'Report layouts and content can be customized using templates that define data sources, formatting, and presentation style. Templates ensure consistent formatting across all generated reports.',
        'Charts, graphs, and tables are automatically created from the underlying data to illustrate key findings. Visualization types are selected based on data characteristics and reporting requirements.',
        'Completed reports are automatically sent to designated recipients via email with customizable subject lines and messages. Distribution lists can be managed centrally and updated as needed.',
        'The system tracks report generation times, delivery success rates, and recipient engagement metrics. These analytics help optimize report timing and content for maximum effectiveness.'
      ],
      status: 'Active',
      lastUpdated: '1 week ago'
    }
  },
  {
    id: 'analytics',
    title: 'Analytics',
    description: 'Comprehensive analytics and insights dashboard',
    icon: BarChart3,
    color: 'red',
    content: {
      features: [
        'Charts and graphs update immediately as new data enters the system, providing current information at all times. Visual elements refresh automatically without requiring manual page reloads or data requests.',
        'Users can create personalized dashboards by selecting relevant metrics and arranging visual components according to their preferences. Multiple dashboard configurations can be saved and switched between as needed.',
        'Historical data is analyzed to identify patterns, trends, and anomalies over specified time periods. Trend analysis includes statistical calculations and predictive modeling based on historical patterns.',
        'Key performance indicators are calculated and displayed with benchmarks and target values for easy assessment. Metrics can be filtered by time period, team, project, or other relevant dimensions.',
        'All charts, reports, and data tables can be exported in multiple formats including PDF, Excel, and CSV. Export functions preserve formatting and include metadata about the data source and generation time.'
      ],
      status: 'Active',
      lastUpdated: '5 minutes ago'
    }
  }
];

export function ClientAdmin() {
  return (
    <div className={styles.container}>

      {/* Individual Admin Sections */}
      {adminSections.map((section) => {
        const Icon = section.icon;
        return (
          <section key={section.id} id={section.id} className={styles.section}>
            <div>
              <h2 className={styles.sectionTitle}>
                <div className={`${styles.iconContainer} ${styles[`iconContainer${section.color.charAt(0).toUpperCase() + section.color.slice(1)}`]}`}>
                  <Icon className={`${styles.icon} ${styles[`icon${section.color.charAt(0).toUpperCase() + section.color.slice(1)}`]}`} />
                </div>
                {section.title}
              </h2>
              <p className={styles.sectionSubtitle}>{section.description}</p>
            </div>

            <div className={styles.sectionCard}>
              <div className={styles.sectionGrid}>
                {/* Features List */}
                <div>
                  <h3 className={styles.featuresTitle}>Key Features</h3>
                  <div className={styles.featuresContainer}>
                    {section.content.features.map((feature, featureIndex) => (
                      <p key={featureIndex} className={styles.featureParagraph}>
                        {feature}
                      </p>
                    ))}
                  </div>
                </div>

                {/* Status and Info */}
                <div>
                  <h3 className={styles.statusTitle}>System Status</h3>
                  <div className={styles.statusList}>
                    <div className={styles.statusItem}>
                      <span className={styles.statusLabel}>Status</span>
                      <span className={styles.statusValue}>
                        {section.content.status}
                      </span>
                    </div>
                    <div className={styles.statusItem}>
                      <span className={styles.statusLabel}>Last Updated</span>
                      <span className={styles.statusValueBold}>{section.content.lastUpdated}</span>
                    </div>
                    <div className={styles.statusItem}>
                      <span className={styles.statusLabel}>Version</span>
                      <span className={styles.statusValueBold}>v1.0.0</span>
                    </div>
                  </div>
                </div>
              </div>


            </div>
          </section>
        );
      })}
    </div>
  );
}
