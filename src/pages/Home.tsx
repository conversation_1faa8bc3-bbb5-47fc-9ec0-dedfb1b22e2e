import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>R<PERSON>, <PERSON>, Clock, BarChart2, Check } from "lucide-react";
import { Videos } from "./Videos";
import { Podcasts } from "./Podcasts";
import styles from "./Home.module.css";

// Import timeline data
import { getRecentTimelineEvents } from "../data/timelineData";

export function Home() {
  const stats = [
    {
      label: "Interviews",
      value: "24/24",
      change: "+100%",
      changeType: "positive",
    },
    {
      label: "Goals Met",
      value: "8/10",
      change: "+80%",
      changeType: "positive",
    },
    {
      label: "Days Left",
      value: "2",
      change: "On Track",
      changeType: "neutral",
    },
  ];

  const timelineEvents = getRecentTimelineEvents();

  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Research Lead",
      imageUrl:
        "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&q=80&w=200&h=200",
    },
    {
      name: "<PERSON>",
      role: "Senior Analyst",
      imageUrl:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&q=80&w=200&h=200",
    },
    {
      name: "Emily Rodriguez",
      role: "UX Researcher",
      imageUrl:
        "https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&q=80&w=200&h=200",
    },
  ];

  return (
    <div className={styles.container}>
      {/* Progress Section */}
      <section className={styles.progressSection}>
        <div className={styles.sectionHeader}>
          <div>
            <h2 className={styles.sectionTitle}>
              Progress Overview
            </h2>
            <p className={styles.sectionDescription}>
              Research project status and metrics
            </p>
          </div>
          <Link
            to='/home/<USER>'
            className={styles.viewDetailsLink}
          >
            View Details <ArrowRight className={styles.viewDetailsIcon} />
          </Link>
        </div>
        <div className={styles.statsGrid}>
          {stats.map((stat, index) => (
            <div key={index} className={styles.statCard}>
              <div className={styles.statCardHeader}>
                <div className={styles.statIconContainer}>
                  {index === 0 ? (
                    <Users className={styles.statIcon} />
                  ) : index === 1 ? (
                    <BarChart2 className={styles.statIcon} />
                  ) : (
                    <Clock className={styles.statIcon} />
                  )}
                </div>
                <span
                  className={`${styles.statChange} ${
                    stat.changeType === "positive"
                      ? styles.statChangePositive
                      : styles.statChangeNeutral
                  }`}
                >
                  {stat.change}
                </span>
              </div>
              <h3 className={styles.statValue}>
                {stat.value}
              </h3>
              <p className={styles.statLabel}>{stat.label}</p>
            </div>
          ))}
        </div>
      </section>

      <div className={styles.mainGrid}>
        {/* Timeline Section */}
        <section className={styles.sectionCard}>
          <div className={styles.sectionCardContent}>
            <div className={styles.sectionHeader}>
              <div>
                <h2 className={styles.sectionTitle}>
                  Recent Timeline
                </h2>
                <p className={styles.sectionDescription}>
                  Latest project milestones
                </p>
              </div>
              <Link
                to='/home/<USER>'
                className={styles.viewDetailsLink}
              >
                Full Timeline <ArrowRight className={styles.viewDetailsIcon} />
              </Link>
            </div>
            <div className={styles.timelineList}>
              {timelineEvents.map((event, index) => (
                <div key={index} className={styles.timelineItem}>
                  <div
                    className={`${styles.timelineIndicator} ${
                      event.current
                        ? styles.timelineIndicatorCurrent
                        : styles.timelineIndicatorCompleted
                    }`}
                  >
                    {!event.current && <Check className='h-3 w-3 text-white' />}
                  </div>
                  <div className={styles.timelineContent}>
                    <p className={styles.timelinePhase}>
                      {event.phase}
                    </p>
                    <p className={styles.timelineDate}>{event.date}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className={styles.sectionCard}>
          <div className={styles.sectionCardContent}>
            <div className={styles.sectionHeader}>
              <div>
                <h2 className={styles.sectionTitle}>
                  Research Team
                </h2>
                <p className={styles.sectionDescription}>Key team members</p>
              </div>
              <Link
                to='/home/<USER>'
                className={styles.viewDetailsLink}
              >
                Meet the Team <ArrowRight className={styles.viewDetailsIcon} />
              </Link>
            </div>
            <div className={styles.teamList}>
              {teamMembers.map((member, index) => (
                <div key={index} className={styles.teamMember}>
                  <img
                    src={member.imageUrl}
                    alt={member.name}
                    className={styles.memberAvatar}
                  />
                  <div>
                    <h3 className={styles.memberName}>
                      {member.name}
                    </h3>
                    <p className={styles.memberRole}>{member.role}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>

      {/* Videos and Podcasts sections */}
      <div className={styles.mediaGrid}>
        <Videos />
        <Podcasts />
      </div>

      {/* Background Image Credit */}
      <div className={styles.footer}>
        <p className={styles.footerText}>
          Background image by{" "}
          <a
            href='https://unsplash.com/@martz90'
            target='_blank'
            rel='noopener noreferrer'
            className={styles.footerLink}
          >
            Martin Martz
          </a>
          {" "}on Unsplash
        </p>
      </div>
    </div>
  );
}
