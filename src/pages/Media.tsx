import React, { useState } from "react";
import { Video, Headphones } from "lucide-react";
import { Videos } from "./Videos";
import { Podcasts } from "./Podcasts";

export function Media() {
  const [activeSection, setActiveSection] = useState<"videos" | "podcasts">("videos");

  return (
    <div className="space-y-8 p-2">
      {/* Header */}
      <div>
        <h2 className="text-3xl font-bold text-text-primary">
          Research Media
        </h2>
        <p className="text-text-secondary mt-2 text-lg">
          Videos and audio recordings from research sessions
        </p>
      </div>

      {/* Section Navigation */}
      <div className="flex space-x-4 border-b border-gray-200">
        <button
          onClick={() => setActiveSection("videos")}
          className={`flex items-center space-x-2 px-4 py-2 border-b-2 transition-colors duration-200 ${
            activeSection === "videos"
              ? "border-blue-500 text-blue-600"
              : "border-transparent text-gray-500 hover:text-gray-700"
          }`}
        >
          <Video className="h-5 w-5" />
          <span>Videos</span>
        </button>
        <button
          onClick={() => setActiveSection("podcasts")}
          className={`flex items-center space-x-2 px-4 py-2 border-b-2 transition-colors duration-200 ${
            activeSection === "podcasts"
              ? "border-blue-500 text-blue-600"
              : "border-transparent text-gray-500 hover:text-gray-700"
          }`}
        >
          <Headphones className="h-5 w-5" />
          <span>Podcasts</span>
        </button>
      </div>

      {/* Content Sections */}
      <div className="min-h-[70vh]">
        {activeSection === "videos" && (
          <div className="animate-fadeIn">
            <Videos />
          </div>
        )}
        {activeSection === "podcasts" && (
          <div className="animate-fadeIn">
            <Podcasts />
          </div>
        )}
      </div>
    </div>
  );
}
