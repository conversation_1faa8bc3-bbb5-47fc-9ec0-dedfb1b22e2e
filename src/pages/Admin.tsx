import React, { useState, useEffect, useId } from "react";
import { useNavigate } from "react-router-dom";
import {
  Plus,
  Edit,
  Trash2,
  ExternalLink,
  ArrowRight,
  LogOut,
  Settings,
  X,
  Upload,
  RotateCcw,
  Save,
} from "lucide-react";
import { useApi } from "../hooks/useApi";
import {
  clientApiService,
  brandApiService,
  uploadService,
  type ClientApi as Client,
  type Brand,
  type CreateClientApiData as CreateClientData,
  type CreateBrandData,
} from "../services";
import { formatApiError } from "../utils/apiUtils";
import { API_CONFIG } from "../config/apiConfig";
import { clearAdminAuthentication } from "../utils/auth";
import { ClientCarousel } from "../components/ClientCarousel";
import { ClientModal } from "../components/ClientModal";
import { AdminFooter } from "../components/AdminFooter";
import { useScrollManager } from "../utils/scrollManager";
import btLogo from "../assets/BT.png";

export function Admin() {
  const navigate = useNavigate();
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);

  // State for API data
  const [clients, setClients] = useState<Client[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Brand modal state
  const [isBrandModalOpen, setIsBrandModalOpen] = useState(false);
  const brandModalId = useId(); // Generate unique ID for brand modal instance
  const [editingBrand, setEditingBrand] = useState<Brand | null>(null);
  const [isCreatingBrand, setIsCreatingBrand] = useState(false);
  const [brandFormData, setBrandFormData] = useState({
    name: "",
    logo: "",
    clientId: "",
  });
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string>("");
  const [validationError, setValidationError] = useState<string>("");

  // Client modal state
  const [isClientModalOpen, setIsClientModalOpen] = useState(false);
  const [editingClient, setEditingClient] = useState<Client | null>(null);
  const [isCreatingClient, setIsCreatingClient] = useState(false);
  const [clientFormData, setClientFormData] = useState({
    name: "",
    logo: "",
    description: "",
  });

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  // Set first client as selected when clients load
  useEffect(() => {
    if (clients && clients.length > 0 && !selectedClientId) {
      setSelectedClientId(clients[0].id);
    }
  }, [clients, selectedClientId]);

  // Use the scroll manager to preserve scroll position for brand modal
  useScrollManager(brandModalId, isBrandModalOpen, 'Admin');

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      const [clientsData, brandsData] = await Promise.all([
        clientApiService.getAllClients(),
        brandApiService.getAllBrands(),
      ]);
      setClients(clientsData);
      setBrands(brandsData);
    } catch (error) {
      console.error("Error loading data:", error);
      setError("Failed to load data. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    clearAdminAuthentication();
    navigate("/");
  };

  const handleOpenBrandModal = (brand?: Brand) => {
    if (brand) {
      setEditingBrand(brand);
      setBrandFormData({
        name: brand.name,
        logo: brand.logo,
        clientId: brand.clientId,
      });
      setLogoPreview(brand.logo);
      setIsCreatingBrand(false);
    } else {
      setEditingBrand(null);
      setBrandFormData({
        name: "",
        logo: "",
        clientId: selectedClientId || "",
      });
      setLogoPreview("");
      setIsCreatingBrand(true);
    }
    setValidationError("");
    setIsBrandModalOpen(true);
  };

  const handleCloseBrandModal = () => {
    setIsBrandModalOpen(false);
    setEditingBrand(null);
    setBrandFormData({ name: "", logo: "", clientId: "" });
    setLogoFile(null);
    setLogoPreview("");
    setValidationError("");
  };

  const handleOpenClientModal = (client?: Client) => {
    if (client) {
      setEditingClient(client);
      setClientFormData({
        name: client.name,
        logo: client.logo,
        description: client.description || "",
      });
      setIsCreatingClient(false);
    } else {
      setEditingClient(null);
      setClientFormData({ name: "", logo: "", description: "" });
      setIsCreatingClient(true);
    }
    setIsClientModalOpen(true);
  };

  const handleCloseClientModal = () => {
    setIsClientModalOpen(false);
    setEditingClient(null);
    setClientFormData({ name: "", logo: "", description: "" });
  };

  const handleLogoChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    const validation = uploadService.validateFile(file);
    if (!validation.isValid) {
      setValidationError(validation.error || "Invalid file");
      return;
    }

    setLogoFile(file);
    setValidationError("");

    try {
      // Upload to S3
      const uploadResult = await uploadService.uploadLogo(file, "brand");
      setLogoPreview(uploadResult.url);
      setBrandFormData((prev) => ({ ...prev, logo: uploadResult.url }));
    } catch (error) {
      console.error("Error uploading logo:", error);
      setValidationError("Failed to upload logo. Please try again.");
    }
  };

  const handleResetLogo = () => {
    setLogoFile(null);
    setLogoPreview("");
    setBrandFormData((prev) => ({ ...prev, logo: "" }));
  };

  const handleBrandSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!brandFormData.name.trim()) {
      setValidationError("Brand name is required");
      return;
    }

    if (!brandFormData.clientId) {
      setValidationError("Please select a client");
      return;
    }

    try {
      if (isCreatingBrand) {
        // Generate endpoint from name
        const endpoint = await brandApiService.generateUniqueEndpoint(
          brandFormData.name
        );

        await brandApiService.createBrand({
          name: brandFormData.name,
          logo: brandFormData.logo,
          endpoint,
          clientId: brandFormData.clientId,
          isDefault: false, // New brands are not default by default
        });
        handleCloseBrandModal();
        await loadData(); // Refresh data
        alert(API_CONFIG.SUCCESS.BRAND_CREATED);
      } else if (editingBrand) {
        await brandApiService.updateBrand(editingBrand.id, {
          name: brandFormData.name,
          logo: brandFormData.logo,
          clientId: brandFormData.clientId,
          // Preserve the isDefault status when editing
          isDefault: editingBrand.isDefault,
        });
        handleCloseBrandModal();
        await loadData(); // Refresh data
        alert(API_CONFIG.SUCCESS.BRAND_UPDATED);
      }
    } catch (error) {
      setValidationError(formatApiError(error as any));
    }
  };

  const handleClientSubmit = async (formData: {
    name: string;
    firstName?: string;
    logo: string;
    description: string;
  }) => {
    if (!formData.name.trim()) {
      alert("Client name is required");
      return;
    }

    try {
      if (isCreatingClient) {
        await clientApiService.createClient({
          name: formData.name,
          firstName: formData.firstName,
          logo: formData.logo,
          description: formData.description,
        });
        await loadData(); // Refresh data
        alert(API_CONFIG.SUCCESS.CLIENT_CREATED);
      } else if (editingClient) {
        await clientApiService.updateClient(editingClient.id, {
          name: formData.name,
          firstName: formData.firstName,
          logo: formData.logo,
          description: formData.description,
        });
        await loadData(); // Refresh data
        alert(API_CONFIG.SUCCESS.CLIENT_UPDATED);
      }
    } catch (error) {
      alert(`Error: ${formatApiError(error as any)}`);
    }
  };

  const handleDeleteBrand = async (brand: Brand) => {
    if (brand.isDefault) {
      alert(
        "Cannot delete the default brand. Please set another brand as default first."
      );
      return;
    }

    if (window.confirm(`Are you sure you want to delete "${brand.name}"?`)) {
      try {
        await brandApiService.deleteBrand(brand.id);
        await loadData(); // Refresh data
        alert(API_CONFIG.SUCCESS.BRAND_DELETED);
      } catch (error) {
        alert(`Error deleting brand: ${formatApiError(error as any)}`);
      }
    }
  };

  const handleDeleteClient = async (client: Client) => {
    if (window.confirm(`Are you sure you want to delete "${client.name}"?`)) {
      try {
        await clientApiService.deleteClient(client.id);
        await loadData(); // Refresh data
        alert(API_CONFIG.SUCCESS.CLIENT_DELETED);
      } catch (error) {
        alert(`Error deleting client: ${formatApiError(error as any)}`);
      }
    }
  };

  const handleNavigateToBrand = (brand: Brand) => {
    navigate(`/${brand.endpoint}`);
  };

  // Filter brands by selected client
  const filteredBrands = selectedClientId
    ? brands.filter((brand) => brand.clientId === selectedClientId)
    : brands;

  // Show loading state
  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto'></div>
          <p className='mt-4 text-gray-600'>Loading admin panel...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen p-8 flex flex-col'>
      <div className='max-w-6xl mx-auto flex-1'>
        {/* Blood and Treasure Header with Logout */}
        <div className='flex items-center justify-between mb-8'>
          <div className='flex items-center space-x-4'>
            {/* Blood and Treasure Logo */}
            <div className='flex-shrink-0'>
              <img
                src={btLogo}
                alt='Blood and Treasure logo'
                className='w-16 h-16 object-contain rounded-lg'
                onError={(e) => {
                  // Fallback to gradient div if image fails to load
                  const target = e.target as HTMLImageElement;
                  target.style.display = "none";
                  const fallback = target.nextElementSibling as HTMLElement;
                  if (fallback) fallback.style.display = "flex";
                }}
              />
              <div className='w-16 h-16 bg-gradient-to-br from-red-500 via-yellow-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-xs hidden'>
                B&T
              </div>
            </div>

            {/* Blood and Treasure Name */}
            <div>
              <h1 className='text-2xl font-bold text-primary'>
                Blood and Treasure
              </h1>
              <p className='text-sm text-gray-200 mt-1'>Admin Dashboard</p>
            </div>
          </div>

          {/* Logout Button */}
          <button
            onClick={handleLogout}
            className='flex items-center space-x-2 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors'
          >
            <LogOut className='h-4 w-4' />
            <span>Logout</span>
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className='mb-6 p-4 bg-red-50 border border-red-200 rounded-lg'>
            <p className='text-red-600'>{error}</p>
          </div>
        )}

        {/* Clients Section */}
        <div className='mb-12'>
          <div className='flex items-center justify-between mb-6'>
            <h2 className='text-2xl font-semibold text-primary'>Clients</h2>
            <button
              onClick={() => handleOpenClientModal()}
              className='inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 shadow-lg hover:shadow-xl'
            >
              <Plus className='w-5 h-5 mr-2' />
              Add New Client
            </button>
          </div>

          {/* Client Carousel */}
          <ClientCarousel
            selectedClientId={selectedClientId}
            onClientSelect={setSelectedClientId}
            onAddClient={() => handleOpenClientModal()}
            onEditClient={handleOpenClientModal}
            clients={clients}
            onDeleteClient={handleDeleteClient}
          />
        </div>

        {/* Brands Section */}
        <div className='mb-8'>
          <div className='flex items-center justify-between mb-6'>
            <h2 className='text-2xl font-semibold text-primary'>
              {selectedClientId
                ? `Brands - ${
                    clients.find((c: Client) => c.id === selectedClientId)
                      ?.name || "Unknown Client"
                  }`
                : "All Brands"}
            </h2>
          </div>

          {/* Brands Grid */}
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {filteredBrands.map((brand) => (
              <div
                key={brand.id}
                className='neumorphic-container p-12 transition-all duration-300 overflow-hidden'
              >
                {/* Brand Header */}
                <div className='flex items-center justify-between mb-6'>
                  <div className='flex items-center space-x-3'>
                    <img
                      src={brand.logo}
                      alt={brand.name}
                      className='w-12 h-12 object-contain rounded-lg bg-gray-50 p-2'
                    />
                    <div>
                      <h3 className='text-lg font-semibold text-primary'>
                        {brand.name}
                      </h3>
                      <p className='text-sm text-gray-500'>/{brand.endpoint}</p>
                    </div>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <button
                      onClick={() => handleOpenBrandModal(brand)}
                      className='p-2 neumorphic-button-secondary rounded-lg'
                      title='Edit Brand Settings'
                    >
                      <Settings className='w-5 h-5' />
                    </button>
                    <button
                      onClick={() => handleDeleteBrand(brand)}
                      className='p-2 neumorphic-button-secondary text-red-600 hover:text-red-700 rounded-lg'
                      title='Delete Brand'
                    >
                      <Trash2 className='w-5 h-5' />
                    </button>
                  </div>
                </div>

                {/* Brand Actions */}
                <div className='space-y-3'>
                  {/* Navigate to Brand */}
                  <button
                    onClick={() => handleNavigateToBrand(brand)}
                    className='w-full flex items-center justify-center px-4 py-2 neumorphic-button-primary'
                  >
                    <ExternalLink className='w-4 h-4 mr-2' />
                    Go to {brand.name}
                    <ArrowRight className='w-4 h-4 ml-2' />
                  </button>
                </div>
              </div>
            ))}

            {/* Add Brand Card */}
            <div
              onClick={() => handleOpenBrandModal()}
              className={`neumorphic-container p-12 transition-all duration-300 cursor-pointer flex flex-col items-center justify-center border-2 border-dashed ${
                selectedClientId
                  ? "border-gray-300 hover:border-blue-400 hover:scale-105"
                  : "border-gray-200 cursor-not-allowed opacity-50"
              }`}
              style={{ minHeight: "200px" }}
            >
              <Plus className='w-12 h-12 text-white mb-4' />
              <span className='text-lg text-white font-medium text-center'>
                Add Brand
              </span>
              {!selectedClientId && (
                <p className='text-sm text-gray-400 mt-2 text-center'>
                  Select a client first
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Brand Modal */}
        {isBrandModalOpen && (
          <div className='fixed inset-0 z-50 overflow-y-auto'>
            <div className='flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0'>
              <div
                className='fixed inset-0 transition-opacity bg-black bg-opacity-30 backdrop-blur-sm'
                onClick={handleCloseBrandModal}
              ></div>

              <div className='inline-block align-bottom neumorphic-container text-left overflow-hidden transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full'>
                <div className='px-4 pt-5 pb-4 sm:p-6 sm:pb-4'>
                  <div className='flex items-center justify-between mb-4'>
                    <h3 className='text-lg font-medium text-primary'>
                      {isCreatingBrand ? "Add New Brand" : "Edit Brand"}
                    </h3>
                    <button
                      onClick={handleCloseBrandModal}
                      className='text-gray-400 hover:text-gray-600 transition-colors'
                    >
                      <X className='w-5 h-5' />
                    </button>
                  </div>

                  <form onSubmit={handleBrandSubmit} className='space-y-4'>
                    {/* Brand Name */}
                    <div>
                      <label className='block text-sm font-medium text-primary mb-2'>
                        Brand Name *
                      </label>
                      <input
                        type='text'
                        value={brandFormData.name}
                        onChange={(e) =>
                          setBrandFormData((prev) => ({
                            ...prev,
                            name: e.target.value,
                          }))
                        }
                        className='w-full px-3 py-2 neumorphic-input font-bold'
                        style={{ color: "var(--color-text-primary)" }}
                        placeholder='Enter brand name'
                        required
                      />
                    </div>

                    {/* Client Selection */}
                    <div>
                      <label className='block text-sm font-medium text-primary mb-2'>
                        Client *
                      </label>
                      <select
                        value={brandFormData.clientId}
                        onChange={(e) =>
                          setBrandFormData((prev) => ({
                            ...prev,
                            clientId: e.target.value,
                          }))
                        }
                        className='w-full px-3 py-2 neumorphic-input'
                        required
                      >
                        <option value=''>Select a client</option>
                        {clients.map((client: Client) => (
                          <option key={client.id} value={client.id}>
                            {client.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Logo Upload */}
                    <div>
                      <label className='block text-sm font-medium text-primary mb-2'>
                        Brand Logo
                      </label>
                      <div className='space-y-3'>
                        {/* Logo Preview */}
                        {logoPreview && (
                          <div className='flex items-center space-x-3'>
                            <img
                              src={logoPreview}
                              alt='Logo preview'
                              className='w-12 h-12 object-contain rounded-lg bg-gray-50 p-2 border'
                            />
                            <button
                              type='button'
                              onClick={handleResetLogo}
                              className='flex items-center text-sm text-primary hover:text-accent'
                            >
                              <RotateCcw className='w-4 h-4 mr-1' />
                              Reset
                            </button>
                          </div>
                        )}

                        {/* File Upload */}
                        <div className='flex items-center space-x-3'>
                          <label className='flex items-center px-4 py-2 neumorphic-button-secondary cursor-pointer'>
                            <Upload className='w-4 h-4 mr-2' />
                            Choose File
                            <input
                              type='file'
                              accept='image/jpeg,image/jpg,image/png,image/gif'
                              onChange={handleLogoChange}
                              className='hidden'
                            />
                          </label>
                          {logoFile && (
                            <span className='text-sm text-primary'>
                              {logoFile.name}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Validation Error */}
                    {validationError && (
                      <div className='text-red-600 text-sm'>
                        {validationError}
                      </div>
                    )}

                    {/* Form Actions */}
                    <div className='flex justify-between pt-4'>
                      <div>
                        {!isCreatingBrand && editingBrand && (
                          <button
                            type='button'
                            onClick={() => {
                              handleDeleteBrand(editingBrand);
                              handleCloseBrandModal();
                            }}
                            className='p-3 neumorphic-button-secondary text-red-600 hover:text-red-700'
                            title='Delete Brand'
                          >
                            <Trash2 className='w-5 h-5' />
                          </button>
                        )}
                      </div>
                      <div className='flex space-x-3'>
                        <button
                          type='button'
                          onClick={handleCloseBrandModal}
                          className='px-4 py-2 neumorphic-button-secondary'
                        >
                          Cancel
                        </button>
                        <button
                          type='submit'
                          className='p-3 neumorphic-button-primary'
                          title={
                            isCreatingBrand ? "Create Brand" : "Update Brand"
                          }
                        >
                          <Save className='w-5 h-5' />
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Client Modal */}
        <ClientModal
          isOpen={isClientModalOpen}
          onClose={handleCloseClientModal}
          editingClient={editingClient}
          isCreating={isCreatingClient}
          onSubmit={handleClientSubmit}
          onDelete={handleDeleteClient}
        />
      </div>

      {/* Admin Footer */}
      <div className='max-w-6xl mx-auto w-full mt-8 pl-24'>
        <AdminFooter />
      </div>
    </div>
  );
}
