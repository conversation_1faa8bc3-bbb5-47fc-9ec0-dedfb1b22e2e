/* Home Page Styles */

.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
}

/* Progress Section */
.progressSection {
  margin-bottom: var(--spacing-8);
}

.sectionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-6);
}

.sectionTitle {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
}

.sectionDescription {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
  margin-top: var(--spacing-1);
}

.viewDetailsLink {
  display: inline-flex;
  align-items: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary-600);
  text-decoration: none;
  transition: color var(--transition-base);
}

.viewDetailsLink:hover {
  color: var(--color-primary-700);
}

.viewDetailsIcon {
  margin-left: var(--spacing-1);
  height: var(--spacing-4);
  width: var(--spacing-4);
}

.statsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-6);
}

@media (min-width: 768px) {
  .statsGrid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.statCard {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-6);
}

.statCardHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.statIconContainer {
  padding: var(--spacing-2);
  background: var(--color-primary-50);
  border-radius: var(--radius-lg);
}

.statIcon {
  height: var(--spacing-5);
  width: var(--spacing-5);
  color: var(--color-primary-600);
}

.statChange {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-1) var(--spacing-2-5);
  border-radius: var(--radius-full);
}

.statChangePositive {
  background: var(--color-success-50);
  color: var(--color-success-600);
}

.statChangeNeutral {
  background: var(--color-gray-50);
  color: var(--color-gray-600);
}

.statValue {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-1);
}

.statLabel {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

/* Main Content Grid */
.mainGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-8);
}

@media (min-width: 1024px) {
  .mainGrid {
    grid-template-columns: 1fr 1fr;
  }
}

/* Section Cards */
.sectionCard {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.sectionCardContent {
  padding: var(--spacing-6);
}

/* Timeline Section */
.timelineList {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.timelineItem {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.timelineIndicator {
  height: var(--spacing-3);
  width: var(--spacing-3);
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.timelineIndicatorCurrent {
  background: var(--color-primary-500);
  box-shadow: 0 0 0 4px var(--color-primary-100);
}

.timelineIndicatorCompleted {
  background: var(--color-success-500);
}

.timelineContent {
  flex: 1;
  min-width: 0;
}

.timelinePhase {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.timelineDate {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

/* Team Section */
.teamList {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.teamMember {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.memberAvatar {
  width: var(--spacing-10);
  height: var(--spacing-10);
  border-radius: var(--radius-full);
  object-fit: cover;
}

.memberName {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
}

.memberRole {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

/* Videos and Podcasts Grid */
.mediaGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 1024px) {
  .mediaGrid {
    grid-template-columns: 1fr 1fr;
  }
}

/* Footer */
.footer {
  margin-top: var(--spacing-8);
  padding-top: var(--spacing-6);
  border-top: 1px solid var(--color-gray-200);
}

.footerText {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  text-align: center;
}

.footerLink {
  color: var(--color-primary-600);
  text-decoration: underline;
  transition: color var(--transition-base);
}

.footerLink:hover {
  color: var(--color-primary-700);
}
