/* Container and layout */
.container {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Typography */
.mainTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
}

.subtitle {
  color: white;
  margin-top: 0.25rem;
}

.sectionTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.sectionSubtitle {
  color: #6b7280;
  margin-top: 0.25rem;
}

/* Stats grid */
.statsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  color: black;
}

@media (min-width: 768px) {
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .statsGrid {
    grid-template-columns: repeat(5, 1fr);
  }
}

.statsCard {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

.cardHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.cardTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.25rem;
}

.cardDescription {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.75rem;
}

.lastUpdated {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Icon containers and icons */
.iconContainer {
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.icon {
  height: 1.5rem;
  width: 1.5rem;
}

/* Color variants for icons */
.iconContainerBlue {
  background-color: #eff6ff;
}

.iconBlue {
  color: #2563eb;
}

.iconContainerGreen {
  background-color: #f0fdf4;
}

.iconGreen {
  color: #16a34a;
}

.iconContainerPurple {
  background-color: #faf5ff;
}

.iconPurple {
  color: #9333ea;
}

.iconContainerOrange {
  background-color: #fff7ed;
}

.iconOrange {
  color: #ea580c;
}

.iconContainerRed {
  background-color: #fef2f2;
}

.iconRed {
  color: #dc2626;
}

/* Status badge */
.statusBadge {
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.125rem 0.625rem;
  border-radius: 9999px;
  background-color: #f0fdf4;
  color: #16a34a;
}

/* Section card */
.sectionCard {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

.sectionGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 1024px) {
  .sectionGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Features */
.featuresTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.featuresList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.featureItem {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.featuresContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  color: #374151;
}

.featureBullet {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  margin-top: 0.5rem;
  flex-shrink: 0;
}

.featureText {
  color: #374151;
}

/* Feature bullet colors */
.featureBulletBlue {
  background-color: #3b82f6;
}

.featureBulletGreen {
  background-color: #10b981;
}

.featureBulletPurple {
  background-color: #8b5cf6;
}

.featureBulletOrange {
  background-color: #f59e0b;
}

.featureBulletRed {
  background-color: #ef4444;
}

/* Status section */
.statusTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.statusList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.statusItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
}

.statusLabel {
  color: #374151;
}

.statusValue {
  padding: 0.25rem 0.75rem;
  background-color: #dcfce7;
  color: #166534;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.statusValueBold {
  color: #111827;
  font-weight: 500;
}

/* Action buttons */
.actionSection {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.actionButtons {
  display: flex;
  gap: 0.75rem;
}

.primaryButton {
  padding: 0.5rem 1rem;
  color: white;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.primaryButton:hover {
  opacity: 0.9;
}

/* Primary button colors */
.primaryButtonBlue {
  background-color: #2563eb;
}

.primaryButtonBlue:hover {
  background-color: #1d4ed8;
}

.primaryButtonGreen {
  background-color: #16a34a;
}

.primaryButtonGreen:hover {
  background-color: #15803d;
}

.primaryButtonPurple {
  background-color: #9333ea;
}

.primaryButtonPurple:hover {
  background-color: #7c3aed;
}

.primaryButtonOrange {
  background-color: #ea580c;
}

.primaryButtonOrange:hover {
  background-color: #c2410c;
}

.primaryButtonRed {
  background-color: #dc2626;
}

.primaryButtonRed:hover {
  background-color: #b91c1c;
}

.secondaryButton {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  color: #374151;
  border-radius: 0.5rem;
  background: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.secondaryButton:hover {
  background-color: #f9fafb;
}
