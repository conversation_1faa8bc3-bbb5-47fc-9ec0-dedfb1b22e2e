/* Container and layout */
.container {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Typography */
.sectionTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.sectionSubtitle {
  color: white;
  margin-top: 0.25rem;
}

/* Icon containers and icons */
.iconContainer {
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.icon {
  height: 1.5rem;
  width: 1.5rem;
}

/* Color variants for icons */
.iconContainerBlue {
  background-color: #eff6ff;
}

.iconBlue {
  color: #2563eb;
}

.iconContainerGreen {
  background-color: #f0fdf4;
}

.iconGreen {
  color: #16a34a;
}

.iconContainerPurple {
  background-color: #faf5ff;
}

.iconPurple {
  color: #9333ea;
}

.iconContainerOrange {
  background-color: #fff7ed;
}

.iconOrange {
  color: #ea580c;
}

.iconContainerRed {
  background-color: #fef2f2;
}

.iconRed {
  color: #dc2626;
}

/* Section card */
.sectionCard {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

.sectionGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 1024px) {
  .sectionGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Features */
.featuresContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  color: #374151;
}

.featureParagraph {
  line-height: 1.6;
  margin: 0;
}

/* UI Preview section */
.statusTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.uiPreview {
  border: 2px dashed #e5e7eb;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  background-color: #f9fafb;
}

.uiPlaceholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.uiText {
  font-weight: 500;
  color: #6b7280;
}

.uiDescription {
  font-size: 0.875rem;
  color: #9ca3af;
  margin: 0;
}
