/* Container and layout */
.container {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Typography */
.sectionTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.sectionSubtitle {
  color: white;
  margin-top: 0.25rem;
}

/* Icon containers and icons */
.iconContainer {
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.icon {
  height: 1.5rem;
  width: 1.5rem;
}

/* Color variants for icons */
.iconContainerBlue {
  background-color: #eff6ff;
}

.iconBlue {
  color: #2563eb;
}

.iconContainerGreen {
  background-color: #f0fdf4;
}

.iconGreen {
  color: #16a34a;
}

.iconContainerPurple {
  background-color: #faf5ff;
}

.iconPurple {
  color: #9333ea;
}

.iconContainerOrange {
  background-color: #fff7ed;
}

.iconOrange {
  color: #ea580c;
}

.iconContainerRed {
  background-color: #fef2f2;
}

.iconRed {
  color: #dc2626;
}

/* Section card */
.sectionCard {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

.sectionGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 1024px) {
  .sectionGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Features */
.featuresContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  color: #374151;
}

.featureParagraph {
  line-height: 1.6;
  margin: 0;
}

/* UI Preview section */
.statusTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.uiPreview {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  background-color: #ffffff;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Chat Interface */
.chatInterface {
  max-width: 400px;
  margin: 0 auto;
}

.chatHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1rem;
}

.chatTitle {
  font-weight: 600;
  color: #111827;
}

.chatStatus {
  font-size: 0.875rem;
  color: #16a34a;
}

.chatMessages {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
  min-height: 120px;
}

.messageUser {
  display: flex;
  justify-content: flex-end;
}

.messageBot {
  display: flex;
  justify-content: flex-start;
}

.messageUser .messageContent {
  background-color: #2563eb;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 1rem 1rem 0.25rem 1rem;
  max-width: 80%;
  font-size: 0.875rem;
}

.messageBot .messageContent {
  background-color: #f3f4f6;
  color: #374151;
  padding: 0.5rem 0.75rem;
  border-radius: 1rem 1rem 1rem 0.25rem;
  max-width: 80%;
  font-size: 0.875rem;
  line-height: 1.4;
}

.chatInput {
  display: flex;
  gap: 0.5rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e5e7eb;
}

.inputField {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.sendButton {
  padding: 0.5rem 1rem;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
}

/* Team Interface */
.teamInterface {
  max-width: 400px;
  margin: 0 auto;
}

.teamHeader {
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.teamList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.teamMember {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
}

.memberAvatar {
  width: 2.5rem;
  height: 2.5rem;
  background-color: #2563eb;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.memberInfo {
  flex: 1;
}

.memberName {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
}

.memberRole {
  color: #6b7280;
  font-size: 0.75rem;
}

.memberStatus {
  font-size: 0.75rem;
  color: #16a34a;
  background-color: #dcfce7;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
}

/* Workflow Interface */
.workflowInterface {
  max-width: 400px;
  margin: 0 auto;
}

.workflowHeader {
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.workflowSteps {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.workflowStep {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.stepIndicator {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  background-color: #e5e7eb;
  color: #6b7280;
}

.stepCompleted {
  background-color: #16a34a;
  color: white;
}

.stepActive {
  background-color: #2563eb;
  color: white;
}

.stepContent {
  flex: 1;
}

.stepTitle {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
}

.stepStatus {
  color: #6b7280;
  font-size: 0.75rem;
}

/* Reports Interface */
.reportsInterface {
  max-width: 400px;
  margin: 0 auto;
}

.reportsHeader {
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.reportsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.reportItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
}

.reportIcon {
  font-size: 1.5rem;
}

.reportInfo {
  flex: 1;
}

.reportName {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
}

.reportSchedule {
  color: #6b7280;
  font-size: 0.75rem;
}

.reportStatus {
  font-size: 0.75rem;
  color: #ea580c;
  background-color: #fff7ed;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
}

/* Analytics Interface */
.analyticsInterface {
  max-width: 400px;
  margin: 0 auto;
}

.analyticsHeader {
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.analyticsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.analyticsCard {
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  text-align: center;
}

.cardTitle {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.cardValue {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.25rem;
}

.cardChange {
  font-size: 0.75rem;
  color: #16a34a;
  font-weight: 500;
}

.chartPlaceholder {
  height: 80px;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1rem;
  display: flex;
  align-items: end;
  justify-content: center;
}

.chartBars {
  display: flex;
  align-items: end;
  gap: 0.25rem;
  height: 100%;
}

.chartBar {
  width: 1rem;
  background-color: #2563eb;
  border-radius: 0.125rem 0.125rem 0 0;
  min-height: 0.25rem;
}
