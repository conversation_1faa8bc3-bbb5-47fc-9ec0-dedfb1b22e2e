import React, { useState, useId } from "react";
import { Play, Clock, Download, X, Eye, Calendar } from "lucide-react";
import { NeumorphicContainer } from "../components/NeumorphicContainer";
import { useOverlayState } from "../utils/overlayState";
import { useScrollManager } from "../utils/scrollManager";

const videos = [
  {
    id: 1,
    title: "User Interview Highlights",
    description:
      "Key moments from our customer interviews showing primary pain points and needs.",
    duration: "12:34",
    thumbnail:
      "https://images.unsplash.com/photo-1515378960530-7c0da6231fb1?auto=format&fit=crop&q=80&w=800",
    date: "2024-03-15",
    url: "https://example.com/video1.mp4",
    views: "1.2K",
    author: "Research Team",
  },
  {
    id: 2,
    title: "Persona Workshop Session",
    description:
      "Full recording of the team workshop where we defined our key personas.",
    duration: "45:21",
    thumbnail:
      "https://images.unsplash.com/photo-1531498860502-7c67cf02f657?auto=format&fit=crop&q=80&w=800",
    date: "2024-03-14",
    url: "https://example.com/video2.mp4",
    views: "856",
    author: "UX Research",
  },
  {
    id: 3,
    title: "Research Methodology Overview",
    description:
      "Detailed explanation of our research approach and methodology.",
    duration: "08:45",
    thumbnail:
      "https://images.unsplash.com/photo-1552664730-d307ca884978?auto=format&fit=crop&q=80&w=800",
    date: "2024-03-13",
    url: "https://example.com/video3.mp4",
    views: "2.1K",
    author: "Research Team",
  },
];

export function Videos() {
  const [selectedVideo, setSelectedVideo] = useState<(typeof videos)[0] | null>(
    null
  );
  const modalId = useId(); // Generate unique ID for this modal instance
  
  // Register video modal with overlay system
  useOverlayState(selectedVideo !== null);
  
  // Use the scroll manager to preserve scroll position (Videos modal is detected as regular)
  useScrollManager(modalId, selectedVideo !== null, 'Videos');

  return (
    <div className='space-y-8 p-2'>
      <div>
        <h2 className='text-3xl font-bold text-text-primary'>
          Research Videos
        </h2>
        <p className='text-text-secondary mt-2 text-lg'>
          Recorded sessions and interviews
        </p>
      </div>
      <NeumorphicContainer>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {videos.map((video) => (
            <div
              key={video.id}
              className='group cursor-pointer transition-all duration-300 hover:scale-[1.02]'
              onClick={() => setSelectedVideo(video)}
            >
              {/* Thumbnail Container - Rounded with background/border */}
              <div className='relative aspect-video bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 mb-3'>
                <img
                  src={video.thumbnail}
                  alt={video.title}
                  className='w-full h-full object-cover'
                />

                {/* Play Button Overlay */}
                <div className='absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200'>
                  <div className='bg-white/90 backdrop-blur-sm rounded-full p-3 transform scale-90 group-hover:scale-100 transition-transform duration-200'>
                    <Play className='h-6 w-6 text-gray-800 fill-current' />
                  </div>
                </div>

                {/* Duration Badge */}
                <div className='absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded'>
                  {video.duration}
                </div>

                {/* Download Button */}
                <button
                  className='absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-white'
                  onClick={(e) => {
                    e.stopPropagation();
                    // Download logic here
                  }}
                >
                  <Download className='h-4 w-4 text-gray-700' />
                </button>
              </div>

              {/* Video Info - Clean text without background/border */}
              <div className='space-y-2'>
                {/* Title */}
                <h3 className='font-bold text-white text-base leading-tight line-clamp-2 group-hover:text-blue-300 transition-colors duration-200'>
                  {video.title}
                </h3>

                {/* Author */}
                <p className='text-base font-semibold text-gray-300'>
                  {video.author}
                </p>

                {/* Meta Info */}
                <div className='flex items-center text-sm font-medium text-gray-400 space-x-3'>
                  <div className='flex items-center'>
                    <Eye className='h-4 w-4 mr-1' />
                    <span>{video.views} views</span>
                  </div>
                  <div className='flex items-center'>
                    <Calendar className='h-4 w-4 mr-1' />
                    <span>
                      {new Date(video.date).toLocaleDateString("en-US", {
                        month: "short",
                        day: "numeric",
                      })}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </NeumorphicContainer>

      {/* Video Modal - Redesigned from scratch */}
      {selectedVideo && (
        <div className='modal-with-sidebar z-50 flex items-center justify-center p-4'>
          {/* Backdrop */}
          <div
            className='absolute inset-0 bg-black/80 backdrop-blur-sm'
            onClick={() => setSelectedVideo(null)}
          />

          {/* Modal Container */}
          <div
            className='relative w-full max-w-[800px] bg-black rounded-2xl overflow-hidden shadow-2xl animate-fade-in'
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <button
              onClick={() => setSelectedVideo(null)}
              className='absolute top-4 right-4 z-20 bg-black/50 backdrop-blur-sm rounded-full p-3 hover:bg-black/70 transition-all duration-200 group'
            >
              <X className='h-6 w-6 text-white group-hover:text-gray-300' />
            </button>

            {/* Video Player Section */}
            <div className='relative aspect-video bg-gray-900'>
              {/* Video Thumbnail/Player */}
              <div className='w-full h-full flex items-center justify-center relative'>
                <img
                  src={selectedVideo.thumbnail}
                  alt={selectedVideo.title}
                  className='w-full h-full object-cover'
                />

                {/* Play Button Overlay */}
                <div className='absolute inset-0 bg-black/30 flex items-center justify-center'>
                  <div className='bg-white/90 backdrop-blur-sm rounded-full p-4 transform scale-110 hover:scale-125 transition-transform duration-200 cursor-pointer'>
                    <Play className='h-8 w-8 text-black fill-current' />
                  </div>
                </div>

                {/* Duration Badge */}
                <div className='absolute bottom-4 right-4 bg-black/80 text-white text-sm px-3 py-1 rounded-full'>
                  {selectedVideo.duration}
                </div>
              </div>
            </div>

            {/* Video Info Section */}
            <div className='bg-white p-6'>
              <div className='flex items-start justify-between mb-4'>
                <div className='flex-1'>
                  {/* Title */}
                  <h2 className='text-2xl font-bold text-gray-900 mb-3 leading-tight'>
                    {selectedVideo.title}
                  </h2>

                  {/* Meta Info Row */}
                  <div className='flex items-center space-x-6 text-sm text-gray-600 mb-4'>
                    <div className='flex items-center'>
                      <Eye className='h-4 w-4 mr-2' />
                      <span className='font-medium'>
                        {selectedVideo.views} views
                      </span>
                    </div>
                    <div className='flex items-center'>
                      <Calendar className='h-4 w-4 mr-2' />
                      <span className='font-medium'>
                        {new Date(selectedVideo.date).toLocaleDateString(
                          "en-US",
                          {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          }
                        )}
                      </span>
                    </div>
                    <div className='flex items-center'>
                      <Clock className='h-4 w-4 mr-2' />
                      <span className='font-medium'>
                        {selectedVideo.duration}
                      </span>
                    </div>
                  </div>

                  {/* Author */}
                  <div className='flex items-center mb-4'>
                    <div className='w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-3'>
                      <span className='text-gray-600 font-semibold text-sm'>
                        {selectedVideo.author
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </span>
                    </div>
                    <div>
                      <p className='font-semibold text-gray-900'>
                        {selectedVideo.author}
                      </p>
                      <p className='text-sm text-gray-600'>Research Team</p>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className='flex items-center space-x-3 ml-6'>
                  <button className='bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-full font-medium transition-colors duration-200 flex items-center'>
                    <Play className='h-4 w-4 mr-2' />
                    Play
                  </button>
                  <button className='bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-full font-medium transition-colors duration-200 flex items-center'>
                    <Download className='h-4 w-4 mr-2' />
                    Download
                  </button>
                </div>
              </div>

              {/* Description */}
              <div className='border-t border-gray-200 pt-4'>
                <h3 className='font-semibold text-gray-900 mb-2'>
                  Description
                </h3>
                <p className='text-gray-700 leading-relaxed'>
                  {selectedVideo.description}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
