import React, { useState, useId } from "react";
import {
  Headphones,
  Clock,
  Download,
  Play,
  Pause,
  X,
  Calendar,
} from "lucide-react";
import { NeumorphicContainer } from "../components/NeumorphicContainer";
import { useOverlayState } from "../utils/overlayState";
import { useScrollManager } from "../utils/scrollManager";

const podcasts = [
  {
    id: 1,
    title: "Understanding User Behaviors",
    description:
      "Deep dive into the patterns we observed during the research phase.",
    duration: "32:15",
    date: "2024-03-15",
    thumbnail:
      "https://images.unsplash.com/photo-1508700115892-45ecd05ae2ad?auto=format&fit=crop&q=80&w=400",
    url: "https://example.com/podcast1.mp3",
  },
  {
    id: 2,
    title: "Customer Journey Insights",
    description:
      "Analysis of key touchpoints and emotional responses in the customer journey.",
    duration: "28:45",
    date: "2024-03-14",
    thumbnail:
      "https://images.unsplash.com/photo-1478737270239-2f02b77fc618?auto=format&fit=crop&q=80&w=400",
    url: "https://example.com/podcast2.mp3",
  },
  {
    id: 3,
    title: "Research Team Discussion",
    description:
      "Team conversation about findings and next steps in our research.",
    duration: "45:30",
    date: "2024-03-13",
    thumbnail:
      "https://images.unsplash.com/photo-1516280440614-37939bbacd81?auto=format&fit=crop&q=80&w=400",
    url: "https://example.com/podcast3.mp3",
  },
];

export function Podcasts() {
  const [playing, setPlaying] = useState<number | null>(null);
  const [selectedPodcast, setSelectedPodcast] = useState<
    (typeof podcasts)[0] | null
  >(null);
  const modalId = useId(); // Generate unique ID for this modal instance
  
  // Register podcast modal with overlay system
  useOverlayState(selectedPodcast !== null);
  
  // Use the scroll manager to preserve scroll position (Podcasts modal is detected as regular)
  useScrollManager(modalId, selectedPodcast !== null, 'Podcasts');

  return (
    <div className='space-y-8 p-2'>
      <div>
        <h2 className='text-3xl font-bold text-text-primary'>
          Research Podcasts
        </h2>
        <p className='text-text-secondary mt-2 text-lg'>
          Audio recordings and team discussions
        </p>
      </div>

      <NeumorphicContainer>
        <div className='space-y-4'>
          {podcasts.map((podcast) => (
            <div
              key={podcast.id}
              className='neumorphic-elevated rounded-xl overflow-hidden cursor-pointer transition-all duration-200 hover:scale-[1.02]'
              onClick={() => setSelectedPodcast(podcast)}
            >
              <div className='flex items-center p-6'>
                <img
                  src={podcast.thumbnail}
                  alt={podcast.title}
                  className='w-20 h-20 rounded-lg object-cover'
                />
                <div className='ml-6 flex-1'>
                  <div className='flex items-start justify-between mb-2'>
                    <div>
                      <h3 className='font-semibold text-text-primary'>
                        {podcast.title}
                      </h3>
                      <p className='text-sm text-text-secondary'>
                        {podcast.description}
                      </p>
                    </div>
                    <button
                      className='text-gray-400 hover:text-gray-600 transition-colors duration-200'
                      onClick={(e) => {
                        e.stopPropagation();
                        // Download logic here
                      }}
                    >
                      <Download className='h-5 w-5' />
                    </button>
                  </div>
                  <div className='flex items-center justify-between mt-4'>
                    <div className='flex items-center space-x-4'>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setPlaying(
                            playing === podcast.id ? null : podcast.id
                          );
                        }}
                        className='p-2 bg-blue-50 rounded-full text-blue-600 hover:bg-blue-100 transition-colors duration-200'
                      >
                        {playing === podcast.id ? (
                          <Pause className='h-5 w-5' />
                        ) : (
                          <Play className='h-5 w-5' />
                        )}
                      </button>
                      <div className='flex items-center text-sm text-text-muted'>
                        <Clock className='h-4 w-4 mr-1' />
                        <span>{podcast.duration}</span>
                        <span className='mx-2'>•</span>
                        <span>
                          {new Date(podcast.date).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    <div className='flex items-center'>
                      <div className='w-48 h-1 bg-gray-200 rounded-full'>
                        <div
                          className='h-full bg-blue-600 rounded-full transition-all duration-200'
                          style={{
                            width: playing === podcast.id ? "45%" : "0%",
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </NeumorphicContainer>

      {/* Podcast Modal - Redesigned from scratch */}
      {selectedPodcast && (
        <div className='modal-with-sidebar z-50 flex items-center justify-center p-4'>
          {/* Backdrop */}
          <div
            className='absolute inset-0 bg-black/80 backdrop-blur-sm'
            onClick={() => setSelectedPodcast(null)}
          />

          {/* Modal Container */}
          <div
            className='relative w-full max-w-4xl bg-white rounded-2xl overflow-hidden shadow-2xl animate-fade-in'
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <button
              onClick={() => setSelectedPodcast(null)}
              className='absolute top-4 right-4 z-20 bg-white/90 backdrop-blur-sm rounded-full p-3 hover:bg-white transition-all duration-200 group shadow-lg'
            >
              <X className='h-6 w-6 text-gray-700 group-hover:text-gray-900' />
            </button>

            {/* Podcast Header Section */}
            <div className='bg-gradient-to-br from-blue-50 to-indigo-100 p-8'>
              <div className='flex items-start space-x-6'>
                {/* Podcast Cover Art */}
                <div className='flex-shrink-0'>
                  <div className='w-32 h-32 rounded-2xl overflow-hidden shadow-xl bg-white'>
                    <img
                      src={selectedPodcast.thumbnail}
                      alt={selectedPodcast.title}
                      className='w-full h-full object-cover'
                    />
                  </div>
                </div>

                {/* Podcast Info */}
                <div className='flex-1 min-w-0'>
                  <h2 className='text-3xl font-bold text-gray-900 mb-3 leading-tight'>
                    {selectedPodcast.title}
                  </h2>

                  {/* Meta Info Row */}
                  <div className='flex items-center space-x-6 text-sm text-gray-600 mb-4'>
                    <div className='flex items-center'>
                      <Headphones className='h-4 w-4 mr-2' />
                      <span className='font-medium'>Research Podcast</span>
                    </div>
                    <div className='flex items-center'>
                      <Calendar className='h-4 w-4 mr-2' />
                      <span className='font-medium'>
                        {new Date(selectedPodcast.date).toLocaleDateString(
                          "en-US",
                          {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          }
                        )}
                      </span>
                    </div>
                    <div className='flex items-center'>
                      <Clock className='h-4 w-4 mr-2' />
                      <span className='font-medium'>
                        {selectedPodcast.duration}
                      </span>
                    </div>
                  </div>

                  {/* Description */}
                  <p className='text-gray-700 leading-relaxed text-lg'>
                    {selectedPodcast.description}
                  </p>
                </div>
              </div>
            </div>

            {/* Audio Player Section */}
            <div className='p-8 bg-white'>
              {/* Play Controls */}
              <div className='flex items-center justify-between mb-6'>
                <div className='flex items-center space-x-4'>
                  <button
                    onClick={() =>
                      setPlaying(
                        playing === selectedPodcast.id
                          ? null
                          : selectedPodcast.id
                      )
                    }
                    className='bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105'
                  >
                    {playing === selectedPodcast.id ? (
                      <Pause className='h-8 w-8' />
                    ) : (
                      <Play className='h-8 w-8' />
                    )}
                  </button>

                  <div className='flex flex-col'>
                    <span className='text-sm text-gray-500'>Duration</span>
                    <span className='text-lg font-semibold text-gray-900'>
                      {selectedPodcast.duration}
                    </span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className='flex items-center space-x-3'>
                  <button className='bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-full font-medium transition-colors duration-200 flex items-center'>
                    <Download className='h-4 w-4 mr-2' />
                    Download
                  </button>
                  <button className='bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-full font-medium transition-colors duration-200 flex items-center'>
                    <Headphones className='h-4 w-4 mr-2' />
                    Share
                  </button>
                </div>
              </div>

              {/* Progress Bar */}
              <div className='space-y-2'>
                <div className='flex justify-between text-sm text-gray-500'>
                  <span>0:00</span>
                  <span>{selectedPodcast.duration}</span>
                </div>
                <div className='relative h-3 bg-gray-200 rounded-full overflow-hidden'>
                  <div
                    className='absolute left-0 top-0 h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-300 ease-out'
                    style={{
                      width: playing === selectedPodcast.id ? "45%" : "0%",
                    }}
                  />
                  {/* Progress Handle */}
                  <div
                    className='absolute top-1/2 transform -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-lg border-2 border-blue-500 transition-all duration-300 ease-out'
                    style={{
                      left: playing === selectedPodcast.id ? "45%" : "0%",
                    }}
                  />
                </div>
              </div>

              {/* Audio Waveform Placeholder */}
              <div className='mt-8 p-6 bg-gray-50 rounded-xl'>
                <div className='flex items-center justify-center space-x-1'>
                  {Array.from({ length: 50 }).map((_, i) => (
                    <div
                      key={i}
                      className='w-1 bg-gray-300 rounded-full transition-all duration-200'
                      style={{
                        height: `${Math.random() * 40 + 10}px`,
                        backgroundColor:
                          playing === selectedPodcast.id && i < 22
                            ? "#3B82F6"
                            : "#D1D5DB",
                      }}
                    />
                  ))}
                </div>
                <p className='text-center text-sm text-gray-500 mt-3'>
                  Audio waveform visualization
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
