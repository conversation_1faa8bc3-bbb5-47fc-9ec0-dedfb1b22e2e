import React, { useEffect, useState } from "react";
import {
  ArrowLeft,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  Heart,
  Target,
  AlertCircle,
  User,
} from "lucide-react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import { Persona } from "../types/persona";
import { personaService } from "../services/personaService";
import { CATEGORY_STYLES } from "../data/mockPersonas";

export function PersonaDetail({ personaId }: { personaId: string }) {
  const [persona, setPersona] = useState<Persona | null>(null);
  const [loading, setLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [activeTab, setActiveTab] = useState<"overview" | "details">(
    "overview"
  );

  useEffect(() => {
    const loadPersona = async () => {
      setLoading(true);
      setImageError(false); // Reset image error when loading new persona
      try {
        const data = await personaService.getPersona(personaId);
        setPersona(data);
      } catch (error) {
        console.error("Error loading persona:", error);
      } finally {
        setLoading(false);
      }
    };

    loadPersona();
  }, [personaId]);

  if (loading) {
    return (
      <div className='flex items-center justify-center h-full'>Loading...</div>
    );
  }

  if (!persona) {
    return (
      <div className='flex items-center justify-center h-full'>
        <div className='text-center'>
          <h2 className='text-2xl font-bold text-text-primary mb-2'>
            Persona Not Found
          </h2>
          <p className='text-text-secondary mb-4'>
            The requested persona could not be found.
          </p>
          <Link
            to='/home/<USER>'
            className='inline-flex items-center text-blue-600 hover:text-blue-700'
          >
            <ArrowLeft className='h-4 w-4 mr-1' />
            Back to Personas
          </Link>
        </div>
      </div>
    );
  }

  const categoryStyle = CATEGORY_STYLES[persona.category];

  // Image fallback system: Unsplash URL -> Local Image -> Default Icon
  const getProfileImage = (): string | undefined => {
    if (!imageError && persona.profileImageUrl) {
      return persona.profileImageUrl;
    }

    if (persona.profileImageName) {
      try {
        // Dynamic import for local images
        return new URL(
          `../assets/personas/${persona.profileImageName}`,
          import.meta.url
        ).href;
      } catch {
        return undefined;
      }
    }

    return undefined;
  };

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div className='space-y-6 p-2'>
      {/* Navigation Tabs - Top of Modal */}
      <div className='flex justify-center -mt-6'>
        <div className='neumorphic-elevated p-1 rounded-xl inline-flex'>
          <button
            onClick={() => setActiveTab("overview")}
            className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 ${
              activeTab === "overview"
                ? `${categoryStyle.badgeBg} ${categoryStyle.badgeText} shadow-md`
                : "text-text-secondary hover:text-text-primary hover:bg-gray-50"
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab("details")}
            className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 ${
              activeTab === "details"
                ? `${categoryStyle.badgeBg} ${categoryStyle.badgeText} shadow-md`
                : "text-text-secondary hover:text-text-primary hover:bg-gray-50"
            }`}
          >
            Details
          </button>
        </div>
      </div>

      <div className='neumorphic-elevated p-6 rounded-2xl'>
        <div className='flex items-center justify-between mb-2'>
          <h2 className='text-3xl font-bold text-text-primary'>
            {persona.name}
          </h2>
          <span
            className={`px-4 py-2 rounded-full ${categoryStyle.badgeBg} ${categoryStyle.badgeText} text-sm font-medium border ${categoryStyle.containerBorder}`}
          >
            {persona.category}
          </span>
        </div>
        <p className='text-text-secondary text-lg'>{persona.title}</p>
      </div>

      {activeTab === "overview" && (
        <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
          {/* Profile Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className='neumorphic-elevated rounded-2xl overflow-hidden'
          >
            <div
              className={`relative h-48 bg-gradient-to-br ${categoryStyle.gradient}`}
            >
              <div className='absolute inset-0 flex items-center justify-center'>
                <motion.div
                  initial={{ scale: 0.9 }}
                  animate={{ scale: 1 }}
                  className='w-32 h-32 rounded-full overflow-hidden ring-4 ring-white shadow-lg'
                >
                  {getProfileImage() ? (
                    <img
                      src={getProfileImage()}
                      alt={persona.name}
                      className='w-full h-full object-cover'
                      onError={handleImageError}
                    />
                  ) : (
                    <div className='w-full h-full bg-gray-100 flex items-center justify-center'>
                      <User className='h-16 w-16 text-gray-400' />
                    </div>
                  )}
                </motion.div>
              </div>
            </div>
            <div className='p-8 pt-20'>
              <div className='space-y-4'>
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 }}
                  className='flex items-center text-text-secondary'
                >
                  <MapPin className={`h-5 w-5 mr-3 ${categoryStyle.accent}`} />
                  <span>{persona.location}</span>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                  className='flex items-center text-text-secondary'
                >
                  <Briefcase
                    className={`h-5 w-5 mr-3 ${categoryStyle.accent}`}
                  />
                  <span>{persona.experience}</span>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                  className='flex items-center text-text-secondary'
                >
                  <Mail className={`h-5 w-5 mr-3 ${categoryStyle.accent}`} />
                  <span>{persona.email}</span>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                  className='flex items-center text-text-secondary'
                >
                  <Phone className={`h-5 w-5 mr-3 ${categoryStyle.accent}`} />
                  <span>{persona.phone}</span>
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Main Content */}
          <div className='lg:col-span-2 space-y-6'>
            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className='neumorphic-elevated rounded-2xl p-8'
            >
              <h4 className='text-xl font-semibold text-text-primary mb-4'>
                Persona Information
              </h4>
              <p className='text-text-secondary leading-relaxed'>
                {persona.background}
              </p>
            </motion.section>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className='neumorphic-elevated rounded-2xl p-8'
              >
                <div className='flex items-center mb-6'>
                  <Target className='h-6 w-6 text-blue-500 mr-3' />
                  <h4 className='text-xl font-semibold text-text-primary'>
                    Goals
                  </h4>
                </div>
                <ul className='space-y-4'>
                  {persona.goals.map((goal, index) => (
                    <motion.li
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className='flex items-start'
                    >
                      <div className='h-2 w-2 rounded-full bg-blue-500 mt-2 mr-3' />
                      <span className='text-text-secondary'>{goal}</span>
                    </motion.li>
                  ))}
                </ul>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className='neumorphic-elevated rounded-2xl p-8'
              >
                <div className='flex items-center mb-6'>
                  <AlertCircle className='h-6 w-6 text-red-500 mr-3' />
                  <h4 className='text-xl font-semibold text-text-primary'>
                    Pain Points
                  </h4>
                </div>
                <ul className='space-y-4'>
                  {persona.painPoints.map((point, index) => (
                    <motion.li
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className='flex items-start'
                    >
                      <div className='h-2 w-2 rounded-full bg-red-500 mt-2 mr-3' />
                      <span className='text-text-secondary'>{point}</span>
                    </motion.li>
                  ))}
                </ul>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className='neumorphic-elevated rounded-2xl p-8'
              >
                <div className='flex items-center mb-6'>
                  <Heart className='h-6 w-6 text-purple-500 mr-3' />
                  <h4 className='text-xl font-semibold text-text-primary'>
                    Preferences
                  </h4>
                </div>
                <ul className='space-y-4'>
                  {persona.preferences.map((pref, index) => (
                    <motion.li
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className='flex items-start'
                    >
                      <div className='h-2 w-2 rounded-full bg-purple-500 mt-2 mr-3' />
                      <span className='text-text-secondary'>{pref}</span>
                    </motion.li>
                  ))}
                </ul>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className='neumorphic-elevated rounded-2xl p-8'
              >
                <h4
                  className={`text-xl font-semibold ${categoryStyle.accent} mb-4`}
                >
                  Persona Traits
                </h4>
                <div className='space-y-4'>
                  <div>
                    <h5 className='font-medium text-text-primary mb-2'>
                      Characteristics
                    </h5>
                    <div className='flex flex-wrap gap-2'>
                      {persona.personaTraits.characteristics.map(
                        (trait, index) => (
                          <span
                            key={index}
                            className={`px-3 py-1 rounded-full text-sm ${categoryStyle.highlight} ${categoryStyle.accent}`}
                          >
                            {trait}
                          </span>
                        )
                      )}
                    </div>
                  </div>
                  <div>
                    <h5 className='font-medium text-text-primary mb-2'>
                      Personality
                    </h5>
                    <p className='text-text-secondary'>
                      {persona.personaTraits.personality.join(", ")}
                    </p>
                  </div>
                  <div>
                    <h5 className='font-medium text-text-primary mb-2'>
                      Motivated by
                    </h5>
                    <p className='text-text-secondary'>
                      {persona.personaTraits.motivations.join(", ")}
                    </p>
                  </div>
                  <div>
                    <h5 className='font-medium text-text-primary mb-2'>
                      Behaviors
                    </h5>
                    <p className='text-text-secondary'>
                      {persona.personaTraits.behaviors.join(", ")}
                    </p>
                  </div>
                </div>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className='neumorphic-elevated rounded-2xl p-8'
              >
                <h4
                  className={`text-xl font-semibold ${categoryStyle.accent} mb-4`}
                >
                  User Summary
                </h4>
                <div className='space-y-4'>
                  <div>
                    <h5 className='font-medium text-text-primary mb-2'>
                      Segment
                    </h5>
                    <p className='text-text-secondary'>
                      {persona.userDetails.segment}
                    </p>
                  </div>
                  <div>
                    <h5 className='font-medium text-text-primary mb-2'>
                      Demographics
                    </h5>
                    <p className='text-text-secondary'>
                      {persona.userDetails.demographics}
                    </p>
                  </div>
                  <div>
                    <h5 className='font-medium text-text-primary mb-2'>
                      Household
                    </h5>
                    <p className='text-text-secondary'>
                      {persona.userDetails.household}
                    </p>
                  </div>
                  <div>
                    <h5 className='font-medium text-text-primary mb-2'>
                      Lifestyle
                    </h5>
                    <p className='text-text-secondary'>
                      {persona.userDetails.lifestyle}
                    </p>
                  </div>
                  <div>
                    <h5 className='font-medium text-text-primary mb-2'>
                      Celebrations
                    </h5>
                    <div className='flex flex-wrap gap-2'>
                      {persona.userDetails.celebrations.map(
                        (celebration, index) => (
                          <span
                            key={index}
                            className={`px-3 py-1 rounded-full text-sm ${categoryStyle.highlight} ${categoryStyle.accent}`}
                          >
                            {celebration}
                          </span>
                        )
                      )}
                    </div>
                  </div>
                </div>
              </motion.section>
            </div>
          </div>
        </div>
      )}

      {activeTab === "details" && (
        <div className='space-y-6'>
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className='neumorphic-elevated rounded-2xl p-8'
          >
            <h4
              className={`text-xl font-semibold ${categoryStyle.accent} mb-6`}
            >
              Detailed Background
            </h4>
            <div className='prose prose-lg max-w-none'>
              <p className='text-text-secondary leading-relaxed mb-6'>
                {persona.background}
              </p>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
                <div>
                  <h5 className='font-semibold text-text-primary mb-3'>
                    Professional Journey
                  </h5>
                  <p className='text-text-secondary leading-relaxed'>
                    {persona.experience} - {persona.title}
                  </p>
                </div>
                <div>
                  <h5 className='font-semibold text-text-primary mb-3'>
                    Location & Demographics
                  </h5>
                  <p className='text-text-secondary leading-relaxed'>
                    Based in {persona.location}, {persona.age} years old
                  </p>
                </div>
              </div>
            </div>
          </motion.section>

          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className='neumorphic-elevated rounded-2xl p-8'
          >
            <h4
              className={`text-xl font-semibold ${categoryStyle.accent} mb-6`}
            >
              In-Depth Analysis
            </h4>
            <div className='space-y-6'>
              <div>
                <h5 className='font-semibold text-text-primary mb-3'>
                  Goals & Aspirations
                </h5>
                <ul className='space-y-3'>
                  {persona.goals.map((goal, index) => (
                    <li key={index} className='flex items-start'>
                      <div
                        className={`h-2 w-2 rounded-full mt-2 mr-3 ${categoryStyle.badgeBg}`}
                      />
                      <span className='text-text-secondary leading-relaxed'>
                        {goal}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h5 className='font-semibold text-text-primary mb-3'>
                  Pain Points & Challenges
                </h5>
                <ul className='space-y-3'>
                  {persona.painPoints.map((point, index) => (
                    <li key={index} className='flex items-start'>
                      <div className='h-2 w-2 rounded-full bg-red-500 mt-2 mr-3' />
                      <span className='text-text-secondary leading-relaxed'>
                        {point}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h5 className='font-semibold text-text-primary mb-3'>
                  Preferences & Interests
                </h5>
                <ul className='space-y-3'>
                  {persona.preferences.map((pref, index) => (
                    <li key={index} className='flex items-start'>
                      <div className='h-2 w-2 rounded-full bg-purple-500 mt-2 mr-3' />
                      <span className='text-text-secondary leading-relaxed'>
                        {pref}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </motion.section>

          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className='neumorphic-elevated rounded-2xl p-8'
          >
            <h4
              className={`text-xl font-semibold ${categoryStyle.accent} mb-6`}
            >
              Contact Information
            </h4>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='flex items-center'>
                <Mail className={`h-5 w-5 mr-3 ${categoryStyle.accent}`} />
                <span className='text-text-secondary'>{persona.email}</span>
              </div>
              <div className='flex items-center'>
                <Phone className={`h-5 w-5 mr-3 ${categoryStyle.accent}`} />
                <span className='text-text-secondary'>{persona.phone}</span>
              </div>
              <div className='flex items-center'>
                <MapPin className={`h-5 w-5 mr-3 ${categoryStyle.accent}`} />
                <span className='text-text-secondary'>{persona.location}</span>
              </div>
              <div className='flex items-center'>
                <Briefcase className={`h-5 w-5 mr-3 ${categoryStyle.accent}`} />
                <span className='text-text-secondary'>
                  {persona.experience}
                </span>
              </div>
            </div>
          </motion.section>
        </div>
      )}
    </div>
  );
}
