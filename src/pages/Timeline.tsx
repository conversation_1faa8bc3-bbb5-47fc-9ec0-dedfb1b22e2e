import React, { useState, useId } from "react";
import { Check, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { NeumorphicContainer } from "../components/NeumorphicContainer";
import { useScrollManager } from "../utils/scrollManager";
import styles from "./Timeline.module.css";

// Import timeline data and utilities
import {
  timelineEvents,
  getFirstRowEvents,
  getSecondRowEvents,
} from "../data/timelineData";



export function Timeline() {
  const [selectedEvent, setSelectedEvent] = useState<number | null>(null);
  const modalId = useId(); // Generate unique ID for this modal instance
  const firstRow = getFirstRowEvents();
  const secondRow = getSecondRowEvents();
  
  // Use the scroll manager to preserve scroll position (Timeline modal is detected as regular)
  useScrollManager(modalId, selectedEvent !== null, 'Timeline');

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2 className={styles.title}>
          Research Timeline
        </h2>
        <p className={styles.subtitle}>
          Project Completion: <b>71%</b>
        </p>
      </div>

      <NeumorphicContainer>
        <div className={styles.timelineContainer}>
          {/* First row - Left to right */}
          <div className={styles.firstRow}>
            {firstRow.map((event, index) => (
              <React.Fragment key={event.phase}>
                <motion.div
                  className={styles.timelineEvent}
                  whileHover={{ scale: 1.05 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div
                    className={`${styles.eventNode} ${
                      event.completed
                        ? styles.eventNodeCompleted
                        : event.current
                        ? styles.eventNodeCurrent
                        : styles.eventNodeUpcoming
                    }`}
                  >
                    {event.completed && (
                      <Check className={styles.eventIcon} />
                    )}
                    {event.current && (
                      <div className={styles.eventCurrentDot} />
                    )}
                  </div>
                  <button
                    onClick={() => setSelectedEvent(index)}
                    className={styles.eventButton}
                  >
                    <p className={styles.eventPhase}>
                      {event.phase}
                    </p>
                    <p className={styles.eventDate}>{event.date}</p>
                  </button>
                </motion.div>
                {index < firstRow.length - 1 && (
                  <div
                    className={
                      event.current && firstRow[index + 1]?.upcoming
                        ? styles.connectorDotted
                        : styles.connector
                    }
                  />
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Second row - Right to left */}
          <div className={styles.secondRow}>
            <div className={styles.verticalConnector} />
            {secondRow.map((event, index) => (
              <React.Fragment key={event.phase}>
                <motion.div
                  className={styles.timelineEvent}
                  whileHover={{ scale: 1.05 }}
                  initial={{ opacity: 0, y: 0 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: (index + firstRow.length) * 0.1 }}
                >
                  <div
                    className={`${styles.eventNode} ${
                      event.completed
                        ? styles.eventNodeCompleted
                        : event.current
                        ? styles.eventNodeCurrent
                        : styles.eventNodeUpcoming
                    }`}
                  >
                    {event.completed && (
                      <Check className={styles.eventIcon} />
                    )}
                    {event.current && (
                      <div className={styles.eventCurrentDot} />
                    )}
                  </div>
                  <button
                    onClick={() => setSelectedEvent(index + firstRow.length)}
                    className={styles.secondRowEventButton}
                  >
                    <p className={styles.eventPhase}>
                      {event.phase}
                    </p>
                    <p className={styles.eventDate}>{event.date}</p>
                  </button>
                </motion.div>
                {index < secondRow.length - 1 && (
                  <div
                    className={
                      event.current && secondRow[index + 1]?.upcoming
                        ? styles.connectorDotted
                        : styles.connector
                    }
                  />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      </NeumorphicContainer>

      {/* Modal */}
      <AnimatePresence>
        {selectedEvent !== null && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className={styles.modalBackdrop}
            onClick={() => setSelectedEvent(null)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className={styles.modal}
            >
              <div className={styles.modalContent}>
                <button
                  onClick={() => setSelectedEvent(null)}
                  className={styles.modalCloseButton}
                >
                  <X className={styles.modalCloseIcon} />
                </button>

                <h3 className={styles.modalTitle}>
                  {timelineEvents[selectedEvent].phase}
                </h3>
                <p className={styles.modalDate}>
                  {timelineEvents[selectedEvent].date}
                </p>

                <div className={styles.modalDetails}>
                  {timelineEvents[selectedEvent].details.map(
                    (detail, index) => (
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        key={index}
                        className={styles.modalDetailItem}
                      >
                        <div className={styles.modalDetailBullet} />
                        <span className={styles.modalDetailText}>{detail}</span>
                      </motion.div>
                    )
                  )}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
