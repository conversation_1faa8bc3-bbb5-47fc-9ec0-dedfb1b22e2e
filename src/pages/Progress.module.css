/* Progress Page Styles */

.container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
  padding: var(--spacing-2);
}

/* Header Section */
.header {
  /* Header styles */
}

.headerContent {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.lastUpdated {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-muted);
}

.subtitle {
  color: var(--color-text-secondary);
  margin-top: var(--spacing-2);
  font-size: var(--font-size-lg);
}

/* Progress Bar Section */
.progressSection {
  /* Styles handled by NeumorphicContainer */
}

.progressBar {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.progressTrack {
  height: var(--spacing-2);
  flex: 1;
  border-radius: var(--radius-full);
  background: var(--color-gray-200);
}

.progressFill {
  height: 100%;
  background: linear-gradient(to right, var(--color-primary-500), var(--color-success-500));
  border-radius: var(--radius-full);
  transition: width 500ms ease-in-out;
}

.progressPercentage {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

/* Content Section */
.content {
  max-width: none;
  position: relative;
}

.mainText {
  font-size: var(--font-size-lg);
  color: var(--color-text-primary);
  line-height: var(--line-height-relaxed);
}

.secondaryText {
  color: var(--color-text-secondary);
}

.spacer {
  margin: var(--spacing-4) 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .headerContent {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }
  
  .title {
    font-size: var(--font-size-2xl);
  }
  
  .lastUpdated {
    font-size: var(--font-size-lg);
  }
  
  .mainText {
    font-size: var(--font-size-base);
  }
}
