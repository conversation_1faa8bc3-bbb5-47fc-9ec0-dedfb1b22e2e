import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { LogOut, Plus, Edit, Trash2, ExternalLink } from "lucide-react";
import { PinAccess } from "../components/PinAccess";
import { AdminFooter } from "../components/AdminFooter";
import { ClientCarousel } from "../components/ClientCarousel";
import { ClientModal } from "../components/ClientModal";
import { Modal } from "../components/Modal";
import { useModal } from "../contexts/ModalContext";
import { useApi } from "../hooks/useApi";
import { formatApiError } from "../utils/apiUtils";
import { API_CONFIG } from "../config/apiConfig";
import { clearAdminAuthentication } from "../utils/auth";
import {
  clientApiService,
  brandApiService,
  uploadService,
  type ClientApi,
  type CreateClientApiData,
  type Brand,
  type CreateBrandData,
} from "../services";
import btLogo from "../assets/BT.png";

export function AdminNew() {
  const navigate = useNavigate();
  const { openModal, isModalOpen, closeModal } = useModal();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [clients, setClients] = useState<ClientApi[]>([]);
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);

  // API hooks
  const [clientsState, clientsActions] = useApi(clientApiService.getAllClients);
  const [brandsState, brandsActions] = useApi(brandApiService.getAllBrands);
  const [createClientState, createClientActions] = useApi(
    clientApiService.createClient
  );
  const [createBrandState, createBrandActions] = useApi(
    brandApiService.createBrand
  );

  // Brand modal state
  const [editingBrand, setEditingBrand] = useState<Brand | null>(null);
  const [isCreatingBrand, setIsCreatingBrand] = useState(false);
  const [brandFormData, setBrandFormData] = useState({
    name: "",
    logo: "",
    clientId: "",
  });
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string>("");
  const [validationError, setValidationError] = useState<string>("");

  // Client modal state
  const [editingClient, setEditingClient] = useState<ClientApi | null>(null);
  const [isCreatingClient, setIsCreatingClient] = useState(false);
  const [clientFormData, setClientFormData] = useState({
    name: "",
    logo: "",
    description: "",
  });

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([clientsActions.execute(), brandsActions.execute()]);
    } catch (error) {
      console.error("Error loading data:", error);
    }
  };

  // Update local state when API data changes
  useEffect(() => {
    if (clientsState.data) {
      setClients(clientsState.data);
      // Set first client as selected if none selected
      if (!selectedClientId && clientsState.data.length > 0) {
        setSelectedClientId(clientsState.data[0].id);
      }
    }
  }, [clientsState.data, selectedClientId]);

  useEffect(() => {
    if (brandsState.data) {
      setBrands(brandsState.data);
    }
  }, [brandsState.data]);

  const handleLogout = () => {
    clearAdminAuthentication();
    // Redirect to home page after logout
    navigate("/");
  };

  const handleOpenBrandModal = (brand?: Brand) => {
    if (brand) {
      setEditingBrand(brand);
      setBrandFormData({
        name: brand.name,
        logo: brand.logo,
        clientId: brand.clientId,
      });
      setLogoPreview(brand.logo);
      setIsCreatingBrand(false);
    } else {
      setEditingBrand(null);
      setBrandFormData({
        name: "",
        logo: "",
        clientId: selectedClientId || "",
      });
      setLogoPreview("");
      setIsCreatingBrand(true);
    }
    openModal("brand");
  };

  const handleCloseBrandModal = () => {
    closeModal();
    setEditingBrand(null);
    setBrandFormData({ name: "", logo: "", clientId: "" });
    setLogoFile(null);
    setLogoPreview("");
    setValidationError("");
  };

  const handleOpenClientModal = (client?: ClientApi) => {
    if (client) {
      setEditingClient(client);
      setClientFormData({
        name: client.name,
        logo: client.logo,
        description: client.description || "",
      });
      setIsCreatingClient(false);
    } else {
      setEditingClient(null);
      setClientFormData({ name: "", logo: "", description: "" });
      setIsCreatingClient(true);
    }
    openModal("client");
  };

  const handleCloseClientModal = () => {
    closeModal();
    setEditingClient(null);
    setClientFormData({ name: "", logo: "", description: "" });
  };

  const handleLogoChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    const validation = uploadService.validateFile(file);
    if (!validation.isValid) {
      setValidationError(validation.error || "Invalid file");
      return;
    }

    setLogoFile(file);
    setValidationError("");

    try {
      // Upload to S3
      const uploadResult = await uploadService.uploadLogo(file, "brand");
      setLogoPreview(uploadResult.url);
      setBrandFormData((prev) => ({ ...prev, logo: uploadResult.url }));
    } catch (error) {
      console.error("Error uploading logo:", error);
      setValidationError("Failed to upload logo. Please try again.");
    }
  };

  const handleResetLogo = () => {
    setLogoFile(null);
    setLogoPreview("");
    setBrandFormData((prev) => ({ ...prev, logo: "" }));
  };

  const handleBrandSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !brandFormData.name.trim() ||
      !brandFormData.logo.trim() ||
      !brandFormData.clientId
    ) {
      setValidationError("Name, logo, and client are required");
      return;
    }

    try {
      if (isCreatingBrand) {
        const brandData: CreateBrandData = {
          name: brandFormData.name,
          logo: brandFormData.logo,
          endpoint: brandApiService.nameToEndpoint(brandFormData.name),
          clientId: brandFormData.clientId,
          isDefault: false, // New brands are not default by default
        };

        await createBrandActions.execute(brandData);
        await brandsActions.execute(); // Reload brands
        handleCloseBrandModal();
      } else if (editingBrand) {
        await brandApiService.updateBrand(editingBrand.id, {
          name: brandFormData.name,
          logo: brandFormData.logo,
          clientId: brandFormData.clientId,
          // Preserve the isDefault status when editing
          isDefault: editingBrand.isDefault,
        });
        await brandsActions.execute(); // Reload brands
        handleCloseBrandModal();
      }
    } catch (error) {
      console.error("Error saving brand:", error);
      setValidationError(formatApiError(error as any));
    }
  };

  const handleClientSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!clientFormData.name.trim() || !clientFormData.logo.trim()) {
      setValidationError("Name and logo are required");
      return;
    }

    try {
      if (isCreatingClient) {
        const clientData: CreateClientApiData = {
          name: clientFormData.name,
          logo: clientFormData.logo,
          description: clientFormData.description,
        };

        await createClientActions.execute(clientData);
        await clientsActions.execute(); // Reload clients
        handleCloseClientModal();
      } else if (editingClient) {
        await clientApiService.updateClient(editingClient.id, {
          name: clientFormData.name,
          logo: clientFormData.logo,
          description: clientFormData.description,
        });
        await clientsActions.execute(); // Reload clients
        handleCloseClientModal();
      }
    } catch (error) {
      console.error("Error saving client:", error);
      setValidationError(formatApiError(error as any));
    }
  };

  const handleDeleteBrand = async (brand: Brand) => {
    if (brand.isDefault) {
      alert(
        "Cannot delete the default brand. Please set another brand as default first."
      );
      return;
    }

    if (!confirm(`Are you sure you want to delete "${brand.name}"?`)) {
      return;
    }

    try {
      await brandApiService.deleteBrand(brand.id);
      await brandsActions.execute(); // Reload brands
    } catch (error) {
      console.error("Error deleting brand:", error);
      alert(`Error deleting brand: ${formatApiError(error as any)}`);
    }
  };

  const handleDeleteClient = async (client: ClientApi) => {
    if (!confirm(`Are you sure you want to delete "${client.name}"?`)) {
      return;
    }

    try {
      await clientApiService.deleteClient(client.id);
      await clientsActions.execute(); // Reload clients
    } catch (error) {
      console.error("Error deleting client:", error);
      alert(`Error deleting client: ${formatApiError(error as any)}`);
    }
  };

  const handleNavigateToBrand = (brand: Brand) => {
    navigate(`/${brand.endpoint}`);
  };

  // Authentication is handled by the router, so we can assume we're authenticated here

  // Show loading state
  if (clientsState.loading || brandsState.loading) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto'></div>
          <p className='mt-4 text-gray-600'>Loading admin panel...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100'>
      {/* Blood and Treasure Header with Logout */}
      <div className='bg-white shadow-sm border-b'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center py-4'>
            <div className='flex items-center space-x-4'>
              {/* Blood and Treasure Logo */}
              <div className='flex-shrink-0'>
                <img
                  src={btLogo}
                  alt='Blood and Treasure logo'
                  className='w-16 h-16 object-contain rounded-lg'
                  onError={(e) => {
                    // Fallback to gradient div if image fails to load
                    const target = e.target as HTMLImageElement;
                    target.style.display = "none";
                    const fallback = target.nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = "flex";
                  }}
                />
                <div className='w-16 h-16 bg-gradient-to-br from-red-500 via-yellow-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-xs hidden'>
                  B&T
                </div>
              </div>

              {/* Blood and Treasure Name */}
              <div>
                <h1 className='text-2xl font-bold text-primary'>
                  Blood and Treasure
                </h1>
              </div>
            </div>

            {/* Logout Button */}
            <button
              onClick={handleLogout}
              className='flex items-center space-x-2 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors'
            >
              <LogOut className='h-4 w-4' />
              <span>Logout</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
          {/* Clients Section */}
          <div className='bg-white rounded-xl shadow-lg p-6'>
            <div className='flex justify-between items-center mb-6'>
              <h2 className='text-xl font-semibold text-gray-900'>Clients</h2>
              <button
                onClick={() => handleOpenClientModal()}
                className='flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors'
              >
                <Plus className='h-4 w-4' />
                <span>Add Client</span>
              </button>
            </div>

            {clientsState.error && (
              <div className='mb-4 p-4 bg-red-50 border border-red-200 rounded-lg'>
                <p className='text-red-600'>
                  {formatApiError(clientsState.error)}
                </p>
              </div>
            )}

            <div className='space-y-4'>
              {clients.map((client) => (
                <div
                  key={client.id}
                  className='flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50'
                >
                  <div className='flex items-center space-x-3'>
                    <img
                      src={client.logo}
                      alt={client.name}
                      className='h-10 w-10 rounded-lg object-cover'
                    />
                    <div>
                      <h3 className='font-medium text-gray-900'>
                        {client.name}
                      </h3>
                      {client.description && (
                        <p className='text-sm text-gray-500'>
                          {client.description}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <button
                      onClick={() => handleOpenClientModal(client)}
                      className='p-2 text-gray-400 hover:text-blue-600 transition-colors'
                    >
                      <Edit className='h-4 w-4' />
                    </button>
                    <button
                      onClick={() => handleDeleteClient(client)}
                      className='p-2 text-gray-400 hover:text-red-600 transition-colors'
                    >
                      <Trash2 className='h-4 w-4' />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Brands Section */}
          <div className='bg-white rounded-xl shadow-lg p-6'>
            <div className='flex justify-between items-center mb-6'>
              <h2 className='text-xl font-semibold text-gray-900'>Brands</h2>
            </div>

            {brandsState.error && (
              <div className='mb-4 p-4 bg-red-50 border border-red-200 rounded-lg'>
                <p className='text-red-600'>
                  {formatApiError(brandsState.error)}
                </p>
              </div>
            )}

            <div className='space-y-4'>
              {brands.map((brand) => (
                <div
                  key={brand.id}
                  className='flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50'
                >
                  <div className='flex items-center space-x-3'>
                    <img
                      src={brand.logo}
                      alt={brand.name}
                      className='h-10 w-10 rounded-lg object-cover'
                    />
                    <div>
                      <h3 className='font-medium text-gray-900'>
                        {brand.name}
                      </h3>
                      <p className='text-sm text-gray-500'>/{brand.endpoint}</p>
                    </div>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <button
                      onClick={() => handleNavigateToBrand(brand)}
                      className='p-2 text-gray-400 hover:text-green-600 transition-colors'
                      title='View Brand'
                    >
                      <ExternalLink className='h-4 w-4' />
                    </button>
                    <button
                      onClick={() => handleOpenBrandModal(brand)}
                      className='p-2 text-gray-400 hover:text-blue-600 transition-colors'
                    >
                      <Edit className='h-4 w-4' />
                    </button>
                    <button
                      onClick={() => handleDeleteBrand(brand)}
                      className='p-2 text-gray-400 hover:text-red-600 transition-colors'
                    >
                      <Trash2 className='h-4 w-4' />
                    </button>
                  </div>
                </div>
              ))}

              {/* Add Brand Card */}
              <div
                onClick={() => handleOpenBrandModal()}
                className='flex items-center justify-center p-8 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-400 hover:bg-gray-50 transition-all duration-300 cursor-pointer'
              >
                <div className='text-center'>
                  <Plus className='w-8 h-8 text-white mx-auto mb-2' />
                  <span className='text-lg text-white font-medium'>
                    Add Brand
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Brand Modal */}
      <Modal
        isOpen={isModalOpen("brand")}
        onClose={handleCloseBrandModal}
        title={isCreatingBrand ? "Add Brand" : "Edit Brand"}
        className='max-w-md'
      >
        <form onSubmit={handleBrandSubmit} className='space-y-4'>
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Name
            </label>
            <input
              type='text'
              value={brandFormData.name}
              onChange={(e) =>
                setBrandFormData({ ...brandFormData, name: e.target.value })
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              placeholder='Brand name'
              required
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Client
            </label>
            <select
              value={brandFormData.clientId}
              onChange={(e) =>
                setBrandFormData({
                  ...brandFormData,
                  clientId: e.target.value,
                })
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              required
            >
              <option value=''>Select a client</option>
              {clients.map((client) => (
                <option key={client.id} value={client.id}>
                  {client.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Logo
            </label>
            <div className='space-y-2'>
              {logoPreview && (
                <img
                  src={logoPreview}
                  alt='Logo preview'
                  className='h-20 w-20 rounded-lg object-cover border'
                />
              )}
              <input
                type='file'
                onChange={handleLogoChange}
                accept='image/*'
                className='w-full'
              />
              {logoPreview && (
                <button
                  type='button'
                  onClick={handleResetLogo}
                  className='text-sm text-red-600 hover:text-red-700'
                >
                  Reset Logo
                </button>
              )}
            </div>
          </div>

          {validationError && (
            <div className='p-3 bg-red-50 border border-red-200 rounded-lg'>
              <p className='text-red-600 text-sm'>{validationError}</p>
            </div>
          )}

          <div className='flex space-x-3'>
            <button
              type='button'
              onClick={handleCloseBrandModal}
              className='flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors'
            >
              Cancel
            </button>
            <button
              type='submit'
              disabled={createBrandState.loading}
              className='flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50'
            >
              {createBrandState.loading ? "Saving..." : "Save"}
            </button>
          </div>
        </form>
      </Modal>

      {/* Client Modal */}
      <Modal
        isOpen={isModalOpen("client")}
        onClose={handleCloseClientModal}
        title={isCreatingClient ? "Add Client" : "Edit Client"}
        className='max-w-md'
      >
        <form onSubmit={handleClientSubmit} className='space-y-4'>
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Name
            </label>
            <input
              type='text'
              value={clientFormData.name}
              onChange={(e) =>
                setClientFormData({
                  ...clientFormData,
                  name: e.target.value,
                })
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              placeholder='Client name'
              required
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Description
            </label>
            <textarea
              value={clientFormData.description}
              onChange={(e) =>
                setClientFormData({
                  ...clientFormData,
                  description: e.target.value,
                })
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              placeholder='Client description'
              rows={3}
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Logo URL
            </label>
            <input
              type='text'
              value={clientFormData.logo}
              onChange={(e) =>
                setClientFormData({
                  ...clientFormData,
                  logo: e.target.value,
                })
              }
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              placeholder='Logo URL or base64 data'
              required
            />
          </div>

          {validationError && (
            <div className='p-3 bg-red-50 border border-red-200 rounded-lg'>
              <p className='text-red-600 text-sm'>{validationError}</p>
            </div>
          )}

          <div className='flex space-x-3'>
            <button
              type='button'
              onClick={handleCloseClientModal}
              className='flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors'
            >
              Cancel
            </button>
            <button
              type='submit'
              disabled={createClientState.loading}
              className='flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50'
            >
              {createClientState.loading ? "Saving..." : "Save"}
            </button>
          </div>
        </form>
      </Modal>
      <AdminFooter />
    </div>
  );
}
