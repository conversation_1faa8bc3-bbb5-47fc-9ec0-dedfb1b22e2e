/* Team Page Styles */

.container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
  padding: var(--spacing-2);
}

/* Header Section */
.header {
  /* Header styles */
}

.title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.subtitle {
  color: var(--color-text-secondary);
  margin-top: var(--spacing-2);
  font-size: var(--font-size-lg);
}

/* Team Grid */
.teamGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 768px) {
  .teamGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .teamGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.memberCard {
  text-align: center;
  transform: scale(1);
  transition: transform 200ms ease-in-out;
  cursor: pointer;
  padding: var(--spacing-6);
}

.memberCard:hover {
  transform: scale(1.05);
}

.memberImageContainer {
  position: relative;
}

.memberImage {
  width: 128px;
  height: 128px;
  border-radius: var(--radius-full);
  margin: 0 auto var(--spacing-4);
  box-shadow: var(--shadow-lg);
}

.memberName {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.memberRole {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Modal Styles */
.modalBackdrop {
  position: fixed;
  inset: 0;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
}

.modalOverlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(4px);
}

.modal {
  position: relative;
  max-width: 448px;
  width: 100%;
  min-height: 420px;
  height: 500px;
  margin: var(--spacing-4);
  padding: var(--spacing-8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.modalCloseButton {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  padding: var(--spacing-2);
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  background: none;
}

.modalCloseIcon {
  height: var(--spacing-5);
  width: var(--spacing-5);
}

.modalImage {
  width: 128px;
  height: 128px;
  border-radius: var(--radius-full);
  margin-bottom: var(--spacing-4);
  box-shadow: var(--shadow-lg);
}

.modalName {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
}

.modalRole {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-2);
}

.modalDetails {
  color: var(--color-text-secondary);
  text-align: left;
  margin-bottom: var(--spacing-4);
  flex: 1;
}

.modalNavigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: var(--spacing-4);
}

.navButton {
  padding: var(--spacing-2);
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  background: none;
}

.navIcon {
  height: var(--spacing-6);
  width: var(--spacing-6);
}

/* Contact Icons */
.contactIcons {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.contactButton {
  padding: var(--spacing-2);
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  background: none;
  transition: all 200ms ease-in-out;
}

.contactButton:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.contactIcon {
  height: var(--spacing-5);
  width: var(--spacing-5);
  color: var(--color-text-primary);
  transition: color var(--transition-base);
}

.contactButton:hover .contactIcon {
  color: var(--color-text-secondary);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .teamGrid {
    gap: var(--spacing-4);
  }
  
  .memberCard {
    padding: var(--spacing-4);
  }
  
  .memberImage {
    width: 96px;
    height: 96px;
  }
  
  .modal {
    margin: var(--spacing-2);
    padding: var(--spacing-6);
    min-height: 360px;
    height: 440px;
  }
  
  .modalImage {
    width: 96px;
    height: 96px;
  }
  
  .modalName {
    font-size: var(--font-size-xl);
  }
  
  .modalRole {
    font-size: var(--font-size-base);
  }
  
  .contactIcons {
    gap: var(--spacing-2);
  }
}
