import React from "react";
import { NeumorphicContainer } from "../components/NeumorphicContainer";
import styles from "./Progress.module.css";

// Import timeline data and utilities
import {
  getTodaysDate,
  getProgressPercentage,
} from "../data/timelineData";



export function Progress() {
  const progressPercentage = getProgressPercentage();
  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h2 className={styles.title}>
            Current Status
          </h2>
        </div>
        <p className={styles.subtitle}>latest update as of {getTodaysDate()}</p>
      </div>

      <NeumorphicContainer>
        <div className={styles.progressBar}>
          <div className={styles.progressTrack}>
            <div
              className={styles.progressFill}
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          <span className={styles.progressPercentage}>
            {Math.round(progressPercentage)}%
          </span>
        </div>

        <div className={styles.content}>
          <p className={styles.mainText}>
            We are completing the on-premise Persona Immersion & Activation
            Workshop.
          </p>

          <div className={styles.spacer} />

          <p className={styles.secondaryText}>
            The goal of this workshop is to introduce the Personas & their
            common characteristics and then go through next steps required
            before completion.

            <br/>

            Previously we completed all phases of the research project including participant recruitment, in-depth interviews, holiday preparation analysis, holiday experience mapping, and comprehensive depth interviews. The major milestones achieved include persona research and data collection, initial persona creation and validation, and development of key insights and strategies.
          </p>
        </div>
      </NeumorphicContainer>
    </div>
  );
}
