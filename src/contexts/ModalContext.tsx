import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from "react";

// Define modal types
export type ModalType =
  | "search"
  | "share"
  | "ai"
  | "brand"
  | "client"
  | "persona"
  | "team"
  | "video"
  | "podcast"
  | "clientSettings";

interface ModalContextType {
  activeModal: ModalType | null;
  openModal: (modalType: ModalType) => void;
  closeModal: () => void;
  closeAllModals: () => void;
  isModalOpen: (modalType: ModalType) => boolean;
}

const ModalContext = createContext<ModalContextType | undefined>(undefined);

interface ModalProviderProps {
  children: ReactNode;
}

export function ModalProvider({ children }: ModalProviderProps) {
  const [activeModal, setActiveModal] = useState<ModalType | null>(null);

  const openModal = useCallback((modalType: ModalType) => {
    // Close any existing modal before opening a new one
    setActiveModal(modalType);
  }, []);

  const closeModal = useCallback(() => {
    setActiveModal(null);
  }, []);

  const closeAllModals = useCallback(() => {
    setActiveModal(null);
  }, []);

  const isModalOpen = useCallback(
    (modalType: ModalType) => {
      return activeModal === modalType;
    },
    [activeModal]
  );

  const value: ModalContextType = {
    activeModal,
    openModal,
    closeModal,
    closeAllModals,
    isModalOpen,
  };

  return (
    <ModalContext.Provider value={value}>{children}</ModalContext.Provider>
  );
}

export function useModal() {
  const context = useContext(ModalContext);
  if (context === undefined) {
    throw new Error("useModal must be used within a ModalProvider");
  }
  return context;
}
