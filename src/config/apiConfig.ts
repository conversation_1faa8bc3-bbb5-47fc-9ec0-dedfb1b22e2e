// API configuration
export const API_CONFIG = {
  // Base URL for API requests
  BASE_URL: import.meta.env.VITE_API_BASE_URL || "http://localhost:3000",

  // Request timeout in milliseconds
  TIMEOUT: 10000,

  // File upload settings
  UPLOAD: {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB (updated to match backend)
    ALLOWED_TYPES: [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
    ],
    ALLOWED_EXTENSIONS: [".jpg", ".jpeg", ".png", ".gif", ".webp"],
  },

  // Endpoints
  ENDPOINTS: {
    HEALTH: "/health",
    CLIENTS: "/clients",
    BRANDS: "/brands",
    UPLOAD: "/upload",
  },

  // Error messages
  ERRORS: {
    NETWORK_ERROR: "Network error. Please check your connection.",
    TIMEOUT_ERROR: "Request timeout. Please try again.",
    VALIDATION_ERROR: "Validation failed. Please check your input.",
    SERVER_ERROR: "Server error. Please try again later.",
    NOT_FOUND: "Resource not found.",
    UNAUTHORIZED: "Unauthorized access.",
    FORBIDDEN: "Access forbidden.",
  },

  // Success messages
  SUCCESS: {
    CLIENT_CREATED: "Client created successfully.",
    CLIENT_UPDATED: "Client updated successfully.",
    CLIENT_DELETED: "Client deleted successfully.",
    BRAND_CREATED: "Brand created successfully.",
    BRAND_UPDATED: "Brand updated successfully.",
    BRAND_DELETED: "Brand deleted successfully.",
    FILE_UPLOADED: "File uploaded successfully.",
    FILE_DELETED: "File deleted successfully.",
  },
} as const;

// Environment check
export const isDevelopment = import.meta.env.DEV;
export const isProduction = import.meta.env.PROD;

// API status check
export const checkApiStatus = async (): Promise<boolean> => {
  try {
    const response = await fetch(
      `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.HEALTH}`
    );
    return response.ok;
  } catch (error) {
    console.error("API status check failed:", error);
    return false;
  }
};
