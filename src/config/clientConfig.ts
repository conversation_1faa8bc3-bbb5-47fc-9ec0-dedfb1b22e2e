// Client configuration - easily changeable for different clients
import btLogo from "../assets/BT.png";

export interface ClientConfig {
  name: string;
  logo: string;
  description?: string;
}

// Default client configuration (for reset functionality)
export const defaultClientConfig: ClientConfig = {
  name: "Blood and Treasure",
  logo: btLogo, // Use Blood and Treasure logo instead of Scoot logo
  description: "Dashboard",
};

// Get client configuration from localStorage or use default
export const getClientConfig = (): ClientConfig => {
  const stored = localStorage.getItem("clientConfig");
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch (error) {
      console.error("Error parsing stored client config:", error);
    }
  }
  return defaultClientConfig;
};

// Save client configuration to localStorage
export const saveClientConfig = (config: ClientConfig): void => {
  localStorage.setItem("clientConfig", JSON.stringify(config));
};

// Reset client configuration to default
export const resetClientConfig = (): void => {
  localStorage.removeItem("clientConfig");
};

// Force update current client configuration to use new defaults
export const updateClientConfigToDefaults = (): void => {
  localStorage.removeItem("clientConfig");
  // This will force the next getClientConfig() call to use the new defaults
};

// Force reset to Blood and Treasure configuration
export const resetToBloodAndTreasure = (): void => {
  localStorage.removeItem("clientConfig");
  // Force immediate update by dispatching an event
  window.dispatchEvent(
    new CustomEvent("clientConfigChanged", {
      detail: { config: defaultClientConfig },
    })
  );
};

// Legacy export for backward compatibility
export const clientConfig = getClientConfig();
