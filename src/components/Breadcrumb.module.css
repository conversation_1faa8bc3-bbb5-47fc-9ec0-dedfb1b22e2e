.breadcrumb {
  padding: 1rem 2rem; /* px-8 py-4 */
}

.list {
  display: flex;
  align-items: center;
  column-gap: 0.5rem; /* space-x-2 */
}

.separator {
  height: 1rem; /* for icon alignment */
  width: 1rem;
  color: var(--color-gray-400);
  margin: 0 0.5rem; /* mx-2 */
}

.link {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
  transition: color var(--transition-base);
}

.link:hover {
  color: var(--color-gray-700);
}

.current {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
}

