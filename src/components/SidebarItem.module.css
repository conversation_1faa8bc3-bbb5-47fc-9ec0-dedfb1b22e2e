.itemBtn {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.5rem; /* p-2 */
  border-radius: var(--radius-lg);
  transition: background-color var(--transition-base), color var(--transition-base);
}
.itemActive {
  background: #2563EB; /* bg-blue-600 */
  color: #fff;
}
.itemDefault {
  color: #D1D5DB; /* text-gray-300 */
}
.itemDefault:hover {
  background: #ffffff44; /* hover:bg-gray-700 */
}
.iconBox {
  flex: none;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.label {
  display: inline-block;
  transition: all 300ms ease;
  margin-left: 0.75rem;
}
.collapsedHidden {
  opacity: 0;
  transform: translateX(0.5rem);
  pointer-events: none;
  width: 0;
}
.expandedVisible {
  opacity: 1;
  transform: translateX(0);
  width: auto;
}

