import React, { useEffect } from "react";
import backgroundImage from "../assets/circut.svg";
import styles from "./BackgroundImage.module.css";

const BackgroundImage: React.FC = () => {
  useEffect(() => {
    // Apply background image to body element via a CSS module class
    document.body.classList.add(styles.bodyBg);
    document.body.style.backgroundImage = `url(${backgroundImage})`;

    // Cleanup function to remove background when component unmounts
    return () => {
      document.body.classList.remove(styles.bodyBg);
      document.body.style.backgroundImage = "";
    };
  }, []);

  return null; // This component doesn't render anything
};

export default BackgroundImage;
