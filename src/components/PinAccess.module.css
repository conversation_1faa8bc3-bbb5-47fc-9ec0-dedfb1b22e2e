.container {
  min-height: 100vh; /* min-h-screen */
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: linear-gradient(to bottom right, #eff6ff, #e0e7ff); /* from-blue-50 to-indigo-100 */
}

.card {
  background: white;
  border-radius: 1rem; /* rounded-2xl */
  box-shadow: var(--shadow-xl);
  padding: 2rem; /* p-8 */
  width: 100%;
  max-width: 28rem; /* max-w-md */
}

.center {
  text-align: center;
}

.spinner {
  height: 2rem; /* h-8 */
  width: 2rem;  /* w-8 */
  border-radius: 9999px;
  border-bottom: 2px solid var(--color-blue-600);
  margin: 0 auto;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loadingText {
  margin-top: 1rem; /* mt-4 */
  color: var(--color-gray-600);
}

.iconLg {
  height: 3rem; /* h-12 */
  width: 3rem;  /* w-12 */
  color: var(--color-blue-600);
  margin: 0 auto 1rem; /* mx-auto mb-4 */
}

.title {
  font-size: 1.5rem; /* text-2xl */
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 0.5rem; /* mb-2 */
}

.subtitle {
  color: var(--color-gray-600);
  margin-bottom: 1.5rem; /* mb-6 */
}

.form {
  display: flex;
  flex-direction: column;
  row-gap: 1rem; /* space-y-4 */
}

.relative {
  position: relative;
}

.input {
  width: 100%;
  padding: 0.75rem 1rem; /* px-4 py-3 */
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  outline: none;
  text-align: center;
  font-size: 1.125rem; /* text-lg */
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  letter-spacing: 0.35em; /* tracking-widest */
  color: #000; /* text-black */
}

.input::placeholder {
  color: var(--color-gray-500);
}

.input:focus {
  box-shadow: 0 0 0 2px var(--color-blue-500);
  border-color: transparent;
}

.toggleBtn {
  position: absolute;
  right: 0.75rem; /* right-3 */
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-gray-400);
  transition: color var(--transition-base);
}

.toggleBtn:hover {
  color: var(--color-gray-600);
}

.alert {
  display: flex;
  align-items: center;
  justify-content: center;
  column-gap: 0.5rem; /* space-x-2 */
  padding: 0.75rem; /* p-3 */
  border-radius: var(--radius-lg);
}

.alertError {
  color: var(--color-red-600);
  background: var(--color-red-50);
}

.alertSuccess {
  color: var(--color-green-600);
  background: var(--color-green-50);
}

.alertIcon {
  height: 1.25rem; /* h-5 */
  width: 1.25rem;  /* w-5 */
}

.buttonRow {
  display: flex;
  column-gap: 0.75rem; /* space-x-3 */
}

.btn {
  flex: 1;
  color: white;
  padding: 0.75rem 1rem; /* py-3 px-4 */
  border-radius: var(--radius-lg);
  transition: background-color var(--transition-base);
}

.btnGray {
  background: var(--color-gray-600);
}

.btnGray:hover {
  background: var(--color-gray-700);
}

.btnBlue {
  background: var(--color-blue-600);
}

.btnBlue:hover {
  background: var(--color-blue-700);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.note {
  margin-top: 1.5rem; /* mt-6 */
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  text-align: center;
}

