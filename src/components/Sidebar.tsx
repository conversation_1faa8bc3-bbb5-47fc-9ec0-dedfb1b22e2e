import React, { useState, useEffect } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import {
  Video,
  BarChart2,
  Notebook,
  Clock,
  UserCircle,
  ChevronDown,
  Users,
  RefreshCcw,
  PanelLeft,
  PanelRight,
  Settings,
  Bot,
  Workflow,
  FileText,
  BarChart3,
} from "lucide-react";
import classNames from "classnames";
import logo from "../assets/scoot-logo.png";
import { brandApiService } from "../services/brandApiService";
import type { Brand } from "../services/brandApiService";
import { getClientConfig } from "../config/clientConfig";
import styles from "./Sidebar.module.css";
import sidebarItemStyles from "./SidebarItem.module.css";

// SidebarProps defines the props for the Sidebar component
interface SidebarProps {
  activeSection: string;
  onSectionChange: (sectionId: string) => void;
  isCollapsed: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
}

// Sidebar main component
export function Sidebar({
  activeSection,
  onSectionChange,
  isCollapsed,
  setIsCollapsed,
}: SidebarProps) {
  const { brandName } = useParams<{ brandName: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const [isSummaryExpanded, setIsSummaryExpanded] = useState(true);
  const [isAdminExpanded, setIsAdminExpanded] = useState(true);
  const [lastRefreshed, setLastRefreshed] = useState<Date>(new Date());
  const [currentBrand, setCurrentBrand] = useState<Brand | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentClient, setCurrentClient] = useState<{
    name: string;
    logo: string;
  } | null>(null);

  // Load brand based on URL parameter
  useEffect(() => {
    const loadBrand = async () => {
      setLoading(true);
      try {
        if (brandName) {
          const brand = await brandApiService.getBrandByEndpoint(brandName);
          if (brand) {
            setCurrentBrand(brand);
          } else {
            // If brand not found, get default brand
            const defaultBrand = await brandApiService.getDefaultBrand();
            setCurrentBrand(defaultBrand);
          }
        } else {
          // No brand name in URL, get default brand
          const defaultBrand = await brandApiService.getDefaultBrand();
          setCurrentBrand(defaultBrand);
        }
      } catch (error) {
        console.error("Error loading brand in sidebar:", error);
        // Fallback to a basic brand object
        setCurrentBrand({
          id: "fallback",
          name: "Project Roots",
          logo: "",
          endpoint: "fallback",
          clientId: "",
          isDefault: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      } finally {
        setLoading(false);
      }
    };

    loadBrand();
  }, [brandName]);

  // Load client information from brand data
  useEffect(() => {
    const loadClientInfo = () => {
      if (currentBrand?.client) {
        // Use client information directly from brand data
        setCurrentClient({
          name: currentBrand.client.name,
          logo: currentBrand.client.logo,
        });
      } else if (loading) {
        // Show loading state
        setCurrentClient(null);
      } else {
        // Fallback to default client info
        setCurrentClient({
          name: "Blood & Treasure",
          logo: "", // Will show initial 'B'
        });
      }
    };

    loadClientInfo();
  }, [currentBrand?.client, loading]);

  // Determine current active section from URL
  const getCurrentActiveSection = () => {
    const pathname = location.pathname;
    const segments = pathname.split('/').filter(Boolean);
    
    // Get the last segment which should be the page identifier
    const lastSegment = segments[segments.length - 1];
    
    // Map URL segments to section IDs
    const sectionMapping: { [key: string]: string } = {
      'timeline': 'timeline',
      'progress': 'progress',
      'team': 'team',
      'personas': 'personas',
      'media': 'media',
      'ai-chatbot': 'ai-chatbot',
      'team-management': 'team-management',
      'workflow': 'workflow',
      'auto-reports': 'auto-reports',
      'analytics': 'analytics',
      'overview': 'timeline', // Default to timeline for overview page
      'admin': 'ai-chatbot', // Default to first admin item
    };
    
    return sectionMapping[lastSegment] || activeSection;
  };

  const currentActiveSection = getCurrentActiveSection();

  // Auto-expand sections based on active page - keep both sections open
  useEffect(() => {
    const summaryItems = ['timeline', 'progress', 'team', 'personas', 'media'];
    const adminItems = ['ai-chatbot', 'team-management', 'workflow', 'auto-reports', 'analytics'];

    if (summaryItems.includes(currentActiveSection)) {
      setIsSummaryExpanded(true);
      // Keep admin section open as well
    } else if (adminItems.includes(currentActiveSection)) {
      setIsAdminExpanded(true);
      // Keep summary section open as well
    }
  }, [currentActiveSection]);

  // Listen for client config changes to update sidebar footer
  // This allows the "Powered by [Client Name]" text to update immediately when admin saves changes
  useEffect(() => {
    const handleClientConfigChange = () => {
      const cfg = getClientConfig();
      setCurrentClient({ name: cfg.name, logo: cfg.logo });
    };

    // Listen for custom event when client config changes
    window.addEventListener("clientConfigChanged", handleClientConfigChange);

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "clientConfig") {
        const cfg = getClientConfig();
        setCurrentClient({ name: cfg.name, logo: cfg.logo });
      }
    };

    // Also check for changes on storage events (in case of multiple tabs)
    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener(
        "clientConfigChanged",
        handleClientConfigChange
      );
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  const menuSections = [
    {
      id: "summary",
      label: "Overview",
      icon: Notebook,
      isExpandable: true,
      items: [
        { id: "timeline", icon: Clock, label: "Timeline" },
        { id: "progress", icon: BarChart2, label: "Progress" },
        { id: "team", icon: UserCircle, label: "Team" },
        { id: "personas", icon: Users, label: "Personas" },
        { id: "media", icon: Video, label: "Media" },
      ],
    },
    {
      id: "admin",
      label: "Admin",
      icon: Settings,
      isExpandable: true,
      items: [
        { id: "ai-chatbot", icon: Bot, label: "AI Chatbot" },
        { id: "team-management", icon: Users, label: "Team Management" },
        { id: "workflow", icon: Workflow, label: "Workflow" },
        { id: "auto-reports", icon: FileText, label: "Auto-Reports" },
        { id: "analytics", icon: BarChart3, label: "Analytics" },
      ],
    },
  ];

  // Handles navigation between sidebar sections
  const handleSectionClick = (sectionId: string) => {
    // Always call onSectionChange to handle scrolling
    onSectionChange(sectionId);

    // Navigate to the appropriate page based on the section
    if (["timeline", "progress", "team", "personas", "media"].includes(sectionId)) {
      // Navigate to overview page with the specific section
      navigate(`/${brandName}/${sectionId}`);
    } else if (["ai-chatbot", "team-management", "workflow", "auto-reports", "analytics"].includes(sectionId)) {
      // Navigate to admin page with the specific section
      navigate(`/${brandName}/${sectionId}`);
    }
  };

  // Handles refresh button click
  const handleRefresh = () => {
    // Add your data refresh logic here
    setLastRefreshed(new Date());
  };

  // Helper function to render client logo or initial
  const renderClientLogo = (clientName: string, clientLogo: string) => {
    if (clientLogo) {
      return (
        <img
          src={clientLogo}
          alt={`${clientName} logo`}
          className={styles.avatarContainer}
        />
      );
    } else {
      // Show initial letter in a circular container
      const initial = clientName.charAt(0).toUpperCase();
      return (
        <div className={classNames(styles.avatarContainer, styles.avatarBlue)}>
          <span className={styles.avatarText}>{initial}</span>
        </div>
      );
    }
  };

  return (
    <>
      {/* Toggle button positioned absolutely outside sidebar */}
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className={styles.toggleFab}
        style={{
          left: isCollapsed ? "72px" : "290px", // Position based on sidebar state
          transform: "translateX(-50%)", // Center the button on the edge
          zIndex: 1050,
        }}
      >
        {isCollapsed ? <PanelRight size={20} /> : <PanelLeft size={20} />}
      </button>

      <aside
        className={classNames(
          styles.sidebar,
          isCollapsed ? styles.sidebarCollapsed : styles.sidebarExpanded
        )}
      >
        {/* Sidebar Header: logo, title, collapse button */}
        <div className={styles.header}>
          <div className={styles.headerContainer}>
            {/* Fixed rectangular container for brand logo - 4:1 ratio like balsam.png */}
            <div className={classNames(isCollapsed ? styles.logoSmall : styles.logoLarge)}>
              <img
                src={currentBrand?.logo || logo}
                alt=''
                className={classNames(styles.logoImage, isCollapsed ? styles.logoImgSmall : styles.logoImgLarge)}
              />
            </div>
          </div>
        </div>

        {/* Sidebar Sections: navigation links and expandable sections */}
        <nav className={styles.content}>
          <ul className={styles.list}>
            {menuSections.map((section) => (
              <li key={section.id}>
                {section.isExpandable ? (
                  <>
                    {/* Section header with navigation and integrated chevron */}
                    <button
                      className={styles.sectionNavBtn}
                      onClick={() => {
                        // Toggle expansion state
                        if (section.id === "summary") {
                          setIsSummaryExpanded(!isSummaryExpanded);
                        } else if (section.id === "admin") {
                          setIsAdminExpanded(!isAdminExpanded);
                        }
                        
                        // Navigate to section page
                        if (section.id === "summary") {
                          navigate(`/${brandName}/overview`);
                        } else if (section.id === "admin") {
                          navigate(`/${brandName}/admin`);
                        }
                      }}
                    >
                      <div className={styles.sectionContent}>
                        {/* Only render icon if defined to avoid JSX type errors */}
                        {section.icon ? <section.icon size={20} /> : <span />}
                        {!isCollapsed && <span>{section.label}</span>}
                      </div>
                      {!isCollapsed && (
                        <ChevronDown
                          className={classNames(
                            styles.chevron,
                            ((section.id === "summary" && isSummaryExpanded) || (section.id === "admin" && isAdminExpanded)) && styles.chevronExpanded
                          )}
                          size={16}
                        />
                      )}
                    </button>
                    {/* Expandable section items */}
                    {((section.id === "summary" && isSummaryExpanded) ||
                      (section.id === "admin" && isAdminExpanded)) &&
                      section.items && (
                        <ul className={styles.subList}>
                          {section.items.map((item) => (
                            <SidebarItem
                              key={item.id}
                              {...item}
                              active={currentActiveSection === item.id}
                              collapsed={isCollapsed}
                              onClick={handleSectionClick}
                              data-tour={`${item.id}-nav`}
                            />
                          ))}
                        </ul>
                      )}
                  </>
                ) : section.items ? (
                  // Render non-expandable section with multiple items
                  section.items.map((item) => (
                    <SidebarItem
                      key={item.id}
                      {...item}
                      active={currentActiveSection === item.id}
                      collapsed={isCollapsed}
                      onClick={handleSectionClick}
                      data-tour={`${item.id}-nav`}
                    />
                  ))
                ) : (
                  // Render single section item
                  <SidebarItem
                    id={section.id}
                    icon={section.icon}
                    label={section.label}
                    active={currentActiveSection === section.id}
                    collapsed={isCollapsed}
                    onClick={handleSectionClick}
                    data-tour={`${section.id}-nav`}
                  />
                )}
              </li>
            ))}
          </ul>
        </nav>

        {/* Refresh Section: refresh button and last refreshed time */}
        <div className={styles.refreshDivider}>
          <div className={styles.refreshBox}>
            <button
              onClick={handleRefresh}
              className={styles.refreshBtn}
            >
              <RefreshCcw size={16} />
              {!isCollapsed && <span className={styles.refreshText}>Refresh Data</span>}
            </button>
            {/* Last refreshed time */}
            {!isCollapsed && (
              <p className={styles.muted}>
                Last refreshed: {lastRefreshed.toLocaleTimeString()}
              </p>
            )}
            {isCollapsed && (
              <p className={styles.muted}>
                {"\u200b \u200b"}
              </p>
            )}
          </div>
        </div>

        {/* Footer: Powered by client badge - following AdminFooter styling */}
        <div className={styles.footerContainer}>
          {isCollapsed ? (
            <button
              onClick={() => navigate("/admin")}
              className={styles.adminBtnCollapsed}
              title='Go to Admin Panel'
            >
              {loading ? (
                <div className={classNames(styles.avatarContainer, styles.avatarGray)}>
                  <div className={styles.spinnerContainer}></div>
                </div>
              ) : currentClient ? (
                renderClientLogo(currentClient.name, currentClient.logo)
              ) : (
                <div className={classNames(styles.avatarContainer, styles.avatarGray)}>
                  <span className={styles.avatarText}>?</span>
                </div>
              )}
            </button>
          ) : (
            <button
              onClick={() => navigate("/admin")}
              className={styles.adminBtn}
              title='Go to Admin Panel'
            >
              {loading ? (
                <div className={classNames(styles.avatarContainer, styles.avatarGray)}>
                  <div className={styles.spinnerContainer}></div>
                </div>
              ) : currentClient ? (
                renderClientLogo(currentClient.name, currentClient.logo)
              ) : (
                <div className={classNames(styles.avatarContainer, styles.avatarGray)}>
                  <span className={styles.avatarText}>?</span>
                </div>
              )}
              <div
                className={classNames(
                  styles.label,
                  isCollapsed ? styles.collapsedHidden : styles.expandedVisible
                )}
                style={{ minWidth: isCollapsed ? 0 : undefined }}
              >
                <div className={styles.labelContainer}>
                  <div className={styles.poweredByLabel}>powered by</div>
                  <div className={styles.poweredByBrand}>
                    {loading ? (
                      <div className={styles.skeleton}></div>
                    ) : (
                      currentClient?.name || "Blood & Treasure"
                    )}
                  </div>
                </div>
              </div>
            </button>
          )}
        </div>
      </aside>
    </>
  );
}

// SidebarItemProps defines the props for a sidebar navigation item
interface SidebarItemProps {
  id: string;
  // Use React.ElementType for Lucide icons to avoid prop type errors
  icon: React.ElementType;
  label: string;
  active: boolean;
  collapsed: boolean;
  onClick: (id: string) => void;
  "data-tour"?: string;
}

// SidebarItem renders a single navigation item in the sidebar
const SidebarItem: React.FC<SidebarItemProps> = ({
  id,
  icon: Icon,
  label,
  active,
  collapsed,
  onClick,
  "data-tour": dataTour,
}) => (
  <li>
    <button
      className={classNames(
        sidebarItemStyles.itemBtn,
        collapsed && sidebarItemStyles.itemCentered,
        active ? sidebarItemStyles.itemActive : sidebarItemStyles.itemDefault
      )}
      onClick={() => onClick(id)}
      data-tour={dataTour}
    >
      <span className={sidebarItemStyles.iconBox}>
        <Icon size={20} />
      </span>
      <span
        className={classNames(
          sidebarItemStyles.label,
          collapsed ? sidebarItemStyles.labelHidden : sidebarItemStyles.labelVisible
        )}
        style={{ minWidth: collapsed ? 0 : undefined }}
      >
        {label}
      </span>
    </button>
  </li>
);
