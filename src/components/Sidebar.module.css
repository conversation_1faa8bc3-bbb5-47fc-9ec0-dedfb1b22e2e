/* Sidebar Component CSS Module */

/* Extending Sidebar module with toggle FAB to match previous Tailwind styles */

/* Header and content */
.header {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  /* p-4 */
  border-bottom: 1px solid #ffffff44;
  /* border-gray-700 */
  height: 5rem;
  /* h-20 */
}

.content {
  flex: 1;
  padding: 0.5rem;
  /* p-2 */
  overflow-y: auto;
}

.list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  row-gap: 0.25rem;
  /* space-y-1 */
}

/* Section header row */
.sectionRow {
  display: flex;
  align-items: center;
}

.sectionNavBtn {
  display: flex;
  align-items: center;
  column-gap: 0.5rem;
  background: #ffffff00;
  /* space-x-2 */
  width: 100%;
  padding: 0.5rem;
  /* p-2 */
  color: #D1D5DB;
  /* text-gray-300 */
  border-radius: var(--radius-lg);
  transition: background-color var(--transition-base), color var(--transition-base);
  justify-content: space-between;
}

.sectionNavBtn:hover {
  background: #ffffff44;
}

/* Center section navigation button when collapsed */
.sidebarCollapsed .sectionNavBtn {
  justify-content: center;
  padding: 0.5rem;
}

.chevron {
  transition: transform var(--transition-base);
  color: #D1D5DB;
}

.chevronExpanded {
  transform: rotate(180deg);
}

/* Nested list */
.subList {
  margin-left: 1rem;
  /* ml-4 */
  margin-top: 0.25rem;
  /* mt-1 */
  display: flex;
  flex-direction: column;
  row-gap: 0.25rem;
}

/* Remove left margin for nested items when collapsed */
.sidebarCollapsed .subList {
  margin-left: 0;
}

/* Refresh area */
.refreshDivider {
  position: fixed;
  bottom: 80px;
  width: 100%;
  flex-shrink: 0;
  border-top: 1px solid #ffffff44;
}

.refreshBox {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  row-gap: 0.5rem;
}

.refreshBtn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: var(--radius-lg);
  border: 1px solid #ffffff44;
  background: #ffffff11;
  color: #ffffff;
  transition: background-color var(--transition-base), color var(--transition-base);
}

.refreshBtn:hover {
  background: #ffffff44;
  color: white;
}

.muted {
  font-size: .75rem;
  color: #6B7280;
  text-align: center;
}

/* Footer admin button */
.adminBtnCollapsed {
  padding: .5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 4rem;
  min-height: 4rem;
  width: 4rem;
  border-radius: var(--radius-lg);
  color: #ffffff44;
  font-size: .75rem;
  margin-left: -1rem;
  transition: all 200ms ease;
  cursor: pointer;
}

.adminBtnCollapsed:hover {
  background: #ffffff44;
  color: white;
}

.adminBtn {
  padding: .5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: .5rem;
  height: 4rem;
  min-height: 4rem;
  width: 100%;
  border-radius: var(--radius-lg);
  color: #ffffff44;
  font-size: .75rem;
  transition: all 200ms ease;
  cursor: pointer;
}

.adminBtn:hover {
  background: #ffffff44;
  color: white;
}

.label {
  transition: all 300ms ease;
}

.collapsedHidden {
  opacity: 0;
  transform: translateX(.5rem);
  pointer-events: none;
  width: 0;
  display: none;
}

.expandedVisible {
  opacity: 1;
  transform: translateX(0);
  width: auto;
}

.poweredByLabel {
  font-size: .875rem;
  color: #ffffff44;
  font-weight: 500;
}

.poweredByBrand {
  color: var(--color-primary);
  font-weight: 600;
}

.skeleton {
  animation: pulse 1.5s ease-in-out infinite;
  background: #ffffff44;
  height: 1rem;
  width: 6rem;
  border-radius: .5rem;
}

@keyframes pulse {
  0% {
    opacity: .6
  }

  50% {
    opacity: 1
  }

  100% {
    opacity: .6
  }
}

.toggleFab {
  position: fixed;
  top: 1rem;
  /* top-4 */
  padding: 1rem;
  /* p-2 */
  border-radius: 200px;
  background: #ffffff12;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  color: white;
  box-shadow: var(--shadow-lg);
  transition: all 300ms ease;
  /* transition-all duration-300 */
}

.toggleFab:hover {
  background: #ffffff44;
  /* hover:bg-gray-700 */
}

.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  background: var(--color-neumorphic-shadow-dark);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-right: 1px solid #ffffff22;
  transition: all var(--transition-base);
  z-index: 1030;
  overflow: hidden;
}

.sidebarExpanded {
  width: var(--sidebar-width-expanded);
}

.sidebarCollapsed {
  width: var(--sidebar-width-collapsed);
}

.sidebarHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6);
  border-bottom: #ffffff44;
  height: var(--header-height);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  transition: opacity var(--transition-base);
}

.logoImage {
  object-fit: contain;
  object-position: center;
  transition: all 300ms ease;
  filter: brightness(0) invert(1);
  /* match previous inline filter */
}

.logoSmall {
  width: 2rem;
  /* w-8 */
  height: 2rem;
  /* h-8 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.logoLarge {
  width: 14rem;
  /* w-56 */
  height: 3.5rem;
  /* h-14 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.logoImgSmall {
  width: 2rem;
  height: 2rem;
}

.logoImgLarge {
  width: 100%;
  height: 100%;
}

.logoText {
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  font-size: var(--font-size-lg);
  line-height: var(--line-height-tight);
}

.toggleButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-base);
}

.expandBtn {
  padding: 0.5rem;
  /* p-2 */
  border-radius: var(--radius-lg);
  color: #ffffffaa;
  /* text-gray-300 */
  transition: background-color var(--transition-base), color var(--transition-base);
}

.expandBtn:hover {
  background: #ffffff44;
  /* hover:bg-gray-700 */
}

.toggleButton:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--color-text-primary);
}

.sidebarContent {
  height: calc(100vh - var(--header-height));
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--spacing-4) 0;
}

/* Navigation Styles */
.navSection {
  margin-bottom: var(--spacing-6);
}

.navSectionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-2) var(--spacing-6);
  margin-bottom: var(--spacing-2);
  cursor: pointer;
  transition: all var(--transition-base);
  border-radius: var(--radius-lg);
  margin: 0 var(--spacing-3) var(--spacing-2) var(--spacing-3);
}

.navSectionHeader:hover {
  background: rgba(255, 255, 255, 0.1);
}

.navSectionTitle {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
}

.navChevron {
  transition: transform var(--transition-base);
  color: #ffffff44;
}

.navChevron.expanded {
  transform: rotate(180deg);
}

.navItems {
  list-style: none;
  margin: 0;
  padding: 0;
}

.navItem {
  margin-bottom: var(--spacing-1);
}
.navItem:hover {
  background: rgba(255, 255, 255, 0.1);
}

.navLink {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-6);
  margin: 0 var(--spacing-3);
  color: var(--color-text-secondary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-base);
  cursor: pointer;
}

.navLink:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--color-text-primary);
}

.navLink.active {
  background: #ffffff44;
  color: var(--color-primary-700);
  border: 1px solid var(--color-primary-200);
}

.navIcon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.navText {
  transition: opacity var(--transition-base);
}

/* Footer Styles */
.sidebarFooter {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-4);
  border-top: 1px solid var(--color-gray-200);
  background: var(--color-neumorphic-bg);
}

.refreshButton {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  width: 100%;
  padding: var(--spacing-3);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  color: var(--color-text-secondary);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--transition-base);
}

.refreshButton:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--color-text-primary);
}

.refreshIcon {
  width: 16px;
  height: 16px;
}

.refreshText {
  color: #ffffff44;
  transition: opacity var(--transition-base);
}

/* Client Logo Styles */
.clientLogo {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-md);
  object-fit: cover;
  background: #ffffff11;
}

.clientLogoPlaceholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: var(--radius-md);
  background: var(--color-primary-100);
  color: var(--color-primary-600);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xs);
}

/* Collapsed state adjustments */
.collapsed .logoText,
.collapsed .navText,
.collapsed .refreshText {
  opacity: 0;
  visibility: hidden;
}

.collapsed .navSectionHeader {
  justify-content: center;
  padding: var(--spacing-2);
}

.collapsed .navLink {
  justify-content: center;
  padding: var(--spacing-3);
}

.collapsed .refreshButton {
  justify-content: center;
  padding: var(--spacing-3);
}

.collapsed .navChevron {
  display: none;
}

/* Scrollbar styling */
.sidebarContent::-webkit-scrollbar {
  width: 4px;
}

.sidebarContent::-webkit-scrollbar-track {
  background: transparent;
}

.sidebarContent::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.sidebarContent::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Consolidated semantic classes */
.headerContainer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sectionContent {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refreshText {
  font-size: 0.875rem;
}

.footerContainer {
  flex-shrink: 0;
  padding: 1rem;
  position: fixed;
  bottom: 0;
  width: 100%;
}

.avatarContainer {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 200ms ease;
}

.avatarContainer:hover {
  transform: scale(1.05);
}

.avatarGray {
  background-color: #4b5563;
}

.avatarBlue {
  background-color: #2563eb;
}

.avatarText {
  color: white;
  font-weight: 600;
  font-size: 1.125rem;
}

.spinnerContainer {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 9999px;
  border-bottom: 2px solid white;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.labelContainer {
  text-align: center;
}

/* Tour data attributes */
/* [data-tour] {
  position: relative;
} */

/* Responsive adjustments */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform var(--transition-base);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .sidebarHeader {
    padding: var(--spacing-4);
  }

  .navSectionHeader,
  .navLink {
    padding-left: var(--spacing-4);
    padding-right: var(--spacing-4);
  }
}