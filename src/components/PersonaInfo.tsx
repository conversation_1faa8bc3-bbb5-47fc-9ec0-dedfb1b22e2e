import React from "react";

interface PersonaInfoProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  points: string[];
  height: number;
  neumorphicRaised?: boolean;
}

export function PersonaInfo({
  icon,
  title,
  description,
  points,
  height,
  neumorphicRaised,
}: PersonaInfoProps) {
  return (
    <div
      className={`${
        neumorphicRaised ? "neumorphic-elevated" : "neumorphic-inset"
      } col-span-2 row-span-2`}
      style={{ height: height }}
    >
      <div className='flex items-center space-x-2 mb-4'>
        {icon}
        <h3 className='text-lg font-semibold text-text-primary'>{title}</h3>
      </div>
      <p className='text-text-secondary mb-4'>{description}</p>
      <ul className='space-y-2 text-text-secondary'>
        {points.map((point, index) => (
          <li key={index} className='flex items-start'>
            <div className='h-2 w-2 rounded-full bg-blue-600 mt-2 mr-3' />
            <span>{point}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}
