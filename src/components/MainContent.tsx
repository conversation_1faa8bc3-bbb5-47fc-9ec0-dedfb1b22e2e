import React from 'react';
import { Overview } from '../sections/Overview';
import { PersonaGroups } from '../sections/PersonaGroups';
import { CompactTimeline } from '../sections/CompactTimeline';
import { ResearchInsights } from '../sections/ResearchInsights';

interface MainContentProps {
  filters: {
    category: string;
    stage: string;
    location: string;
  };
}

export function MainContent({ filters }: MainContentProps) {
  return (
    <div className="max-w-7xl mx-auto px-8 py-6 space-y-12">
      <Overview />
      <PersonaGroups filters={filters} />
      <CompactTimeline />
      <ResearchInsights />
    </div>
  );
}