/* PersonaCard Component CSS Module */

.personaCard {
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: all var(--transition-base);
  height: 350px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: white;
  border: 2px solid;
}

.personaCard:hover {
  box-shadow: var(--shadow-xl);
  transform: scale(1.02);
  box-shadow: var(--shadow-2xl);
}

.personaCard:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary-500), 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* Category-specific border styles */
.personaCard.ambitious {
  border-color: rgb(239, 68, 68);
}

.personaCard.ambitious:hover {
  border-color: rgb(220, 38, 38);
}

.personaCard.architect {
  border-color: rgb(59, 130, 246);
}

.personaCard.architect:hover {
  border-color: rgb(37, 99, 235);
}

.personaCard.contented {
  border-color: rgb(34, 197, 94);
}

.personaCard.contented:hover {
  border-color: rgb(22, 163, 74);
}

.personaCard.sophisticate {
  border-color: rgb(168, 85, 247);
}

.personaCard.sophisticate:hover {
  border-color: rgb(147, 51, 234);
}

.personaCard.overcomer {
  border-color: rgb(245, 158, 11);
}

.personaCard.overcomer:hover {
  border-color: rgb(217, 119, 6);
}

.cardHeader {
  padding: var(--spacing-4);
  position: relative;
  color: rgb(31, 41, 55);
}

.profileImageContainer {
  position: absolute;
  top: var(--spacing-4);
  left: var(--spacing-4);
}

.profileImage {
  width: 64px;
  height: 64px;
  border-radius: var(--radius-full);
  object-fit: cover;
}

.profileImagePlaceholder {
  width: 64px;
  height: 64px;
  border-radius: var(--radius-full);
  background: var(--color-gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
}

.cardContent {
  margin-left: 80px;
}

.cardTitleRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.personaName {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

.categoryBadge {
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: 1px solid;
}

/* Category badge styles */
.categoryBadge.ambitious {
  background: rgb(254, 242, 242);
  color: rgb(153, 27, 27);
  border-color: rgb(239, 68, 68);
}

.categoryBadge.architect {
  background: rgb(239, 246, 255);
  color: rgb(30, 64, 175);
  border-color: rgb(59, 130, 246);
}

.categoryBadge.contented {
  background: rgb(240, 253, 244);
  color: rgb(20, 83, 45);
  border-color: rgb(34, 197, 94);
}

.categoryBadge.sophisticate {
  background: rgb(250, 245, 255);
  color: rgb(88, 28, 135);
  border-color: rgb(168, 85, 247);
}

.categoryBadge.overcomer {
  background: rgb(255, 251, 235);
  color: rgb(146, 64, 14);
  border-color: rgb(245, 158, 11);
}

.personaTitle {
  font-size: var(--font-size-sm);
  color: black;
}

.cardBody {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding: 0 var(--spacing-4);
  overflow-y: auto;
}

.aboutSection {
  margin-bottom: var(--spacing-2);
}

.sectionTitle {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-1);
  color: rgb(31, 41, 55);
}

.sectionText {
  font-size: var(--font-size-sm);
  color: rgb(31, 41, 55);
  line-height: var(--line-height-relaxed);
}

.categorySection {
  margin-bottom: var(--spacing-3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .personaCard {
    height: auto;
    min-height: 300px;
  }
  
  .cardContent {
    margin-left: 72px;
  }
  
  .profileImage,
  .profileImagePlaceholder {
    width: 56px;
    height: 56px;
  }
  
  .cardTitleRow {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }
}