.footer {
  /* container allows additional external className composition */
}

.row {
  display: flex;
  align-items: center;
  column-gap: 1rem; /* space-x-4 */
  padding: 1rem 0;  /* py-4 */
}

.logoWrap {
  flex-shrink: 0;
}

.logoImg {
  width: 4rem;  /* w-16 */
  height: 4rem; /* h-16 */
  object-fit: contain;
  border-radius: var(--radius-lg);
}

.logoFallback {
  width: 4rem;
  height: 4rem;
  border-radius: var(--radius-lg);
  display: none; /* becomes flex on image error */
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xs);
  background: linear-gradient(135deg, #ef4444, #f59e0b 50%, #2563eb);
}

.textWrap {
  flex: 1;
}

.text {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  font-weight: var(--font-weight-medium);
}

.brand {
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
}

