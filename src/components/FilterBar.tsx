import React from 'react';
import { Filter, MapPin, Users, Clock, Calendar, ChevronDown } from 'lucide-react';
import styles from './FilterBar.module.css';

interface FilterBarProps {
  filters: {
    category: string;
    stage: string;
    location: string;
    dateFrom?: string;
    dateTo?: string;
  };
  setFilters: React.Dispatch<React.SetStateAction<{
    category: string;
    stage: string;
    location: string;
    dateFrom?: string;
    dateTo?: string;
  }>>;
}

export function FilterBar({ filters, setFilters }: FilterBarProps) {
  const categories = ['all', 'customer', 'business owner', 'stakeholder'];
  const stages = ['all', 'research', 'validation', 'implementation'];
  const locations = ['all', 'US', 'Europe', 'Asia'];

  return (
    <div className={styles.container}>
      <div className={styles.inner}>
        <div className={styles.row}>
          <div className={styles.controls}>
            {/* Category Filter */}
            <div className={styles.group}>
              <label className={styles.label}>Category</label>
              <div className={styles.relative}>
                <div className={styles.iconWrapper}>
                  <Users className={styles.iconGray} />
                </div>
                <select
                  value={filters.category}
                  onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                  className={`${styles.select} ${styles.withIconLeft}`}
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </select>
                <div className={styles.chevronWrapper}>
                  <ChevronDown className={styles.iconGray} />
                </div>
              </div>
            </div>

            {/* Stage Filter */}
            <div className={styles.group}>
              <label className={styles.label}>Research Stage</label>
              <div className={styles.relative}>
                <div className={styles.iconWrapper}>
                  <Clock className={styles.iconGray} />
                </div>
                <select
                  value={filters.stage}
                  onChange={(e) => setFilters(prev => ({ ...prev, stage: e.target.value }))}
                  className={`${styles.select} ${styles.withIconLeft}`}
                >
                  {stages.map(stage => (
                    <option key={stage} value={stage}>
                      {stage.charAt(0).toUpperCase() + stage.slice(1)}
                    </option>
                  ))}
                </select>
                <div className={styles.chevronWrapper}>
                  <ChevronDown className={styles.iconGray} />
                </div>
              </div>
            </div>

            {/* Location Filter */}
            <div className={styles.group}>
              <label className={styles.label}>Location</label>
              <div className={styles.relative}>
                <div className={styles.iconWrapper}>
                  <MapPin className={styles.iconGray} />
                </div>
                <select
                  value={filters.location}
                  onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                  className={`${styles.select} ${styles.withIconLeft}`}
                >
                  {locations.map(location => (
                    <option key={location} value={location}>
                      {location.charAt(0).toUpperCase() + location.slice(1)}
                    </option>
                  ))}
                </select>
                <div className={styles.chevronWrapper}>
                  <ChevronDown className={styles.iconGray} />
                </div>
              </div>
            </div>
          </div>

          {/* Date Range */}
          <div className={styles.group}>
            <label className={styles.label}>Date Range</label>
            <div className={styles.dateRow}>
              <div className={styles.relative}>
                <div className={styles.iconWrapper}>
                  <Calendar className={styles.iconGray} />
                </div>
                <input
                  type="date"
                  value={filters.dateFrom || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                  className={`${styles.input} ${styles.withIconLeft}`}
                />
              </div>
              <span className={styles.muted}>to</span>
              <input
                type="date"
                value={filters.dateTo || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                className={styles.input}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}