import React from "react";
import btLogo from "../assets/BT.png";
import styles from "./AdminFooter.module.css";

interface AdminFooterProps {
  className?: string;
}

export const AdminFooter: React.FC<AdminFooterProps> = ({ className = "" }) => {
  return (
    <div className={`${styles.footer} ${className}`}>
      <div className={styles.row}>
        {/* Blood and Treasure Logo */}
        <div className={styles.logoWrap}>
          <img
            src={btLogo}
            alt='Blood and Treasure logo'
            className={styles.logoImg}
            onError={(e) => {
              // Fallback to gradient div if image fails to load
              const target = e.target as HTMLImageElement;
              target.style.display = "none";
              const fallback = target.nextElementSibling as HTMLElement;
              if (fallback) fallback.style.display = "flex";
            }}
          />
          <div className={styles.logoFallback}>
            B&T
          </div>
        </div>

        {/* Powered by Text */}
        <div className={styles.textWrap}>
          <p className={styles.text}>
            powered by{" "}
            <span className={styles.brand}>
              Blood and Treasure
            </span>
          </p>
        </div>
      </div>
    </div>
  );
};
