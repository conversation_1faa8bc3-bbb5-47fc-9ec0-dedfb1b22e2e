import React from "react";
import { LucideIcon } from "lucide-react";

interface GlassyButtonProps {
  icon: LucideIcon;
  onClick: () => void;
  ariaLabel?: string;
  className?: string;
}

export function GlassyButton({
  icon: Icon,
  onClick,
  ariaLabel,
  className = "",
}: GlassyButtonProps) {
  return (
    <button
      onClick={onClick}
      className={`glassy-button ${className}`}
      aria-label={ariaLabel}
    >
      <Icon className='h-4 w-4' />
    </button>
  );
}
