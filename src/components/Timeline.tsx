import React from "react";
import { Check } from "lucide-react";

const timelineEvents = [
  {
    phase: "Study Design & Participant Recruit",
    date: "2024-11-19",
    completed: true,
  },
  {
    phase: "Phase I: Real Life",
    date: "2024-11-20",
    completed: true,
  },
  {
    phase: "Phase II: Holiday Prep",
    date: "2024-11-21",
    completed: true,
  },
  {
    phase: "Phase III: Holiday Experience",
    date: "2024-11-22",
    completed: true,
  },
  {
    phase: "Phase IV: Depth Interviews",
    date: "2024-11-23",
    completed: true,
  },
];

export function Timeline() {
  return (
    <div className='py-8'>
      <h2 className='text-2xl font-bold mb-6'>Timeline of Events</h2>
      <div className='relative px-4'>
        {/* Main timeline line */}
        <div className='absolute left-8 top-0 h-full w-px bg-gray-200' />

        {/* Timeline events */}
        <div className='space-y-8'>
          {timelineEvents.map((event, index) => (
            <div
              key={index}
              className='relative flex items-center group'
              style={{
                opacity: 0,
                animation: `fadeIn 0.5s ease-out forwards ${index * 0.2}s`,
              }}
            >
              <div className='absolute left-8 -translate-x-1/2'>
                <div className='h-4 w-4 rounded-full bg-green-500 flex items-center justify-center'>
                  <Check className='h-3 w-3 text-white' />
                </div>
              </div>
              <div className='ml-16 transform translate-y-0 group-hover:-translate-y-1 transition-transform duration-200'>
                <h3 className='text-lg font-medium text-gray-900'>
                  {event.phase}
                </h3>
                <p className='text-sm text-gray-500 mt-1'>{event.date}</p>
              </div>
            </div>
          ))}

          {/* Today: Persona Workshop */}
          <div
            className='relative flex items-center group'
            style={{
              opacity: 0,
              animation: "fadeIn 0.5s ease-out forwards 1.2s",
            }}
          >
            <div className='absolute left-8 -translate-x-1/2'>
              <div className='h-4 w-4 rounded-full bg-blue-500 ring-4 ring-blue-100' />
            </div>
            <div className='ml-16 transform translate-y-0 group-hover:-translate-y-1 transition-transform duration-200'>
              <h3 className='text-lg font-medium text-gray-900'>
                Today: Persona Workshop
              </h3>
              <p className='text-sm text-gray-500 mt-1'>2024-11-24</p>
            </div>
          </div>

          {/* Completion Step */}
          <div
            className='relative flex items-center group'
            style={{
              opacity: 0,
              animation: "fadeIn 0.5s ease-out forwards 1.4s",
            }}
          >
            <div className='absolute left-8 -translate-x-1/2'>
              <div className='h-4 w-4 rounded-full border-2 border-gray-300 bg-white' />
            </div>
            <div className='ml-16 transform translate-y-0 group-hover:-translate-y-1 transition-transform duration-200'>
              <h3 className='text-lg font-medium text-gray-900'>Completion</h3>
              <p className='text-sm text-gray-500 mt-1'>2024-11-25</p>
            </div>
          </div>
        </div>
      </div>

      <style>
        {`
          @keyframes fadeIn {
            from {
              opacity: 0;
              transform: translateY(10px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `}
      </style>
    </div>
  );
}
