import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Lock, Eye, EyeOff, AlertCircle, CheckCircle } from "lucide-react";
import { adminService } from "../services";
import { API_CONFIG } from "../config/apiConfig";
import styles from "./PinAccess.module.css";

interface PinAccessProps {
  onSuccess: () => void;
  onCancel?: () => void;
}

export const PinAccess: React.FC<PinAccessProps> = ({
  onSuccess,
  onCancel,
}) => {
  const navigate = useNavigate();
  const [pin, setPin] = useState("");
  const [showPin, setShowPin] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const [pinExists, setPinExists] = useState<boolean | null>(null);

  // Check if PIN exists on component mount
  useEffect(() => {
    checkPinExists();
  }, []);

  const checkPinExists = async () => {
    try {
      const exists = await adminService.pinExists();
      setPinExists(exists);
    } catch (error) {
      console.error("Error checking PIN existence:", error);
      setError("Unable to check PIN status. Please try again.");
    }
  };

  const handlePinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase();
    if (value.length <= 6) {
      setPin(value);
      setError(""); // Clear error when user types
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!pin || pin.length !== 6) {
      setError("Please enter a 6-character PIN");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const isValid = await adminService.validatePin(pin);

      if (isValid) {
        setSuccess(true);
        // Store authentication in session
        sessionStorage.setItem("adminAuthenticated", "true");
        sessionStorage.setItem("adminPin", pin);

        setTimeout(() => {
          onSuccess();
        }, 1000);
      } else {
        setError("Invalid PIN. Please try again.");
      }
    } catch (error) {
      console.error("Error validating PIN:", error);
      setError(
        "Unable to validate PIN. Please check your connection and try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      navigate("/");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    // Allow only alphanumeric characters
    const char = String.fromCharCode(e.which);
    if (
      !/[A-Za-z0-9]/.test(char) &&
      e.key !== "Backspace" &&
      e.key !== "Delete"
    ) {
      e.preventDefault();
    }
  };

  if (pinExists === null) {
    return (
      <div className={styles.container}>
        <div className={styles.card}>
          <div className={styles.center}>
            <div className={styles.spinner}></div>
            <p className={styles.loadingText}>Checking PIN status...</p>
          </div>
        </div>
      </div>
    );
  }

  if (pinExists === false) {
    return (
      <div className={styles.container}>
        <div className={styles.card}>
          <div className={styles.center}>
            <Lock className={styles.iconLg} />
            <h2 className={styles.title}>No PIN Set</h2>
            <p className={styles.subtitle}>
              No admin PIN has been configured. Please contact your
              administrator.
            </p>
            <button
              onClick={handleCancel}
              className={`${styles.btn} ${styles.btnGray}`}
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.card}>
        <div className={styles.center}>
          <Lock className={styles.iconLg} />
          <h2 className={styles.title}>Admin Access</h2>
          <p className={styles.subtitle}>
            Enter your 6-character admin PIN to continue
          </p>

          <form onSubmit={handleSubmit} className={styles.form}>
            <div className={styles.relative}>
              <input
                type={showPin ? "text" : "password"}
                value={pin}
                onChange={handlePinChange}
                onKeyPress={handleKeyPress}
                placeholder='Enter PIN'
                className={styles.input}
                maxLength={6}
                disabled={isLoading || success}
                autoFocus
              />
              <button
                type='button'
                onClick={() => setShowPin(!showPin)}
                className={styles.toggleBtn}
                disabled={isLoading || success}
              >
                {showPin ? (
                  <EyeOff className={styles.alertIcon} />
                ) : (
                  <Eye className={styles.alertIcon} />
                )}
              </button>
            </div>

            {error && (
              <div className={`${styles.alert} ${styles.alertError}`}>
                <AlertCircle className={styles.alertIcon} />
                <span className='text-sm font-medium'>{error}</span>
              </div>
            )}

            {success && (
              <div className={`${styles.alert} ${styles.alertSuccess}`}>
                <CheckCircle className={styles.alertIcon} />
                <span className='text-sm font-medium'>Access granted!</span>
              </div>
            )}

            <div className={styles.buttonRow}>
              <button
                type='button'
                onClick={handleCancel}
                className={`${styles.btn} ${styles.btnGray}`}
                disabled={isLoading || success}
              >
                Cancel
              </button>
              <button
                type='submit'
                disabled={isLoading || success || pin.length !== 6}
                className={`${styles.btn} ${styles.btnBlue}`}
              >
                {isLoading ? (
                  <div className='flex items-center justify-center'>
                    <div className='animate-spin rounded-full h-5 w-5 border-b-2 border-white'></div>
                    <span className='ml-2'>Validating...</span>
                  </div>
                ) : (
                  "Access"
                )}
              </button>
            </div>
          </form>

          <div className={styles.note}>
            <p>PIN must be exactly 6 characters</p>
            <p>Use letters and numbers only</p>
          </div>
        </div>
      </div>
    </div>
  );
};
