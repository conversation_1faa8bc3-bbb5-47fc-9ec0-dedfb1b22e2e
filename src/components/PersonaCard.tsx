import React, { useState } from "react";
import { User } from "lucide-react";
import { CATEGORY_STYLES } from "../data/mockPersonas";

export function PersonaCard({
  persona,
  handlePersonaClick,
}: {
  persona: {
    id: string;
    name: string;
    category: string;
    title: string;
    summary: string;
    profileImageUrl?: string;
    profileImageName?: string;
    description?: string; // Category description
  };
  handlePersonaClick: (id: string) => void;
}) {
  const getCategoryStyles = (category: string) => {
    return (
      CATEGORY_STYLES[category as keyof typeof CATEGORY_STYLES] ||
      CATEGORY_STYLES.Ambitious
    );
  };

  // Generate unique background patterns for each persona type
  const getPersonaPattern = (category: string) => {
    const patterns: Record<string, string> = {
      Ambitious: `
        linear-gradient(90deg, rgba(239, 68, 68, 0.25) 0%, transparent 2px, transparent 98%, rgba(239, 68, 68, 0.25) 100%),
        linear-gradient(0deg, rgba(239, 68, 68, 0.25) 0%, transparent 2px, transparent 98%, rgba(239, 68, 68, 0.25) 100%),
        linear-gradient(45deg, rgba(239, 68, 68, 0.15) 0%, transparent 20%, rgba(239, 68, 68, 0.15) 40%, transparent 60%, rgba(239, 68, 68, 0.15) 80%, transparent 100%)
      `,
      Architect: `
        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.3) 0%, transparent 8px),
        radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.3) 0%, transparent 8px),
        radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.15) 0%, transparent 15px),
        linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, transparent 30%, rgba(59, 130, 246, 0.1) 100%)
      `,
      Contented: `
        radial-gradient(ellipse 80px 40px at 30% 30%, rgba(34, 197, 94, 0.25) 0%, transparent 50%),
        radial-gradient(ellipse 60px 30px at 70% 70%, rgba(34, 197, 94, 0.25) 0%, transparent 50%),
        radial-gradient(ellipse 100px 50px at 50% 50%, rgba(34, 197, 94, 0.12) 0%, transparent 50%)
      `,
      Sophisticate: `
        linear-gradient(135deg, rgba(168, 85, 247, 0.25) 0%, transparent 30%, rgba(168, 85, 247, 0.25) 60%, transparent 100%),
        linear-gradient(45deg, rgba(168, 85, 247, 0.15) 0%, transparent 40%, rgba(168, 85, 247, 0.15) 80%, transparent 100%),
        radial-gradient(circle at 25% 75%, rgba(168, 85, 247, 0.2) 0%, transparent 12px),
        radial-gradient(circle at 75% 25%, rgba(168, 85, 247, 0.2) 0%, transparent 12px)
      `,
      Overcomer: `
        linear-gradient(60deg, rgba(245, 158, 11, 0.25) 0%, transparent 25%, rgba(245, 158, 11, 0.25) 50%, transparent 75%, rgba(245, 158, 11, 0.25) 100%),
        linear-gradient(120deg, rgba(245, 158, 11, 0.15) 0%, transparent 35%, rgba(245, 158, 11, 0.15) 65%, transparent 100%),
        radial-gradient(circle at 40% 60%, rgba(245, 158, 11, 0.2) 0%, transparent 10px),
        radial-gradient(circle at 60% 40%, rgba(245, 158, 11, 0.2) 0%, transparent 10px)
      `,
    };

    return patterns[category] || patterns.Ambitious;
  };

  // Handle keyboard events for accessibility
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      handlePersonaClick(persona.id);
    }
  };

  const categoryStyles = getCategoryStyles(persona.category);
  const [imageError, setImageError] = useState(false);

  // Image fallback system: Unsplash URL -> Local Image -> Default Icon
  const getProfileImage = (): string | undefined => {
    if (!imageError && persona.profileImageUrl) {
      return persona.profileImageUrl;
    }

    if (persona.profileImageName) {
      try {
        // Dynamic import for local images
        return new URL(
          `../assets/personas/${persona.profileImageName}`,
          import.meta.url
        ).href;
      } catch {
        return undefined;
      }
    }

    return undefined;
  };

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div
      // Make entire card clickable with proper accessibility
      role='button'
      tabIndex={0}
      onClick={() => handlePersonaClick(persona.id)}
      onKeyDown={handleKeyDown}
      className={`rounded-2xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl h-[350px] flex flex-col cursor-pointer hover:scale-[1.02] hover:shadow-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 border-2 ${categoryStyles.border} ${categoryStyles.borderHover} bg-white`}
      style={{
        background: `${getPersonaPattern(
          persona.category
        )}, rgba(191, 168, 147, 0.91)`,
        backdropFilter: "blur(20px)",
        WebkitBackdropFilter: "blur(20px)",
      }}
      aria-label={`View details for ${persona.name}, ${persona.category} persona`}
    >
      <div className={`p-4 relative text-gray-800`}>
        <div className='absolute top-4 left-4'>
          {getProfileImage() ? (
            <img
              src={getProfileImage()}
              alt={persona.name}
              className='w-16 h-16 rounded-full object-cover'
              onError={handleImageError}
            />
          ) : (
            <div className='w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center'>
              <User className='h-8 w-8 text-gray-400' />
            </div>
          )}
        </div>
        <div className='ml-20'>
          <div className='flex items-center justify-between'>
            <h3 className='text-base font-semibold'>{persona.name}</h3>
            <span
              className={`px-3 py-1 rounded-full ${categoryStyles.badgeBg} ${categoryStyles.badgeText} text-sm font-medium border ${categoryStyles.containerBorder}`}
            >
              {persona.category}
            </span>
          </div>
          <p className='text-sm text-black'>{persona.title}</p>
        </div>
      </div>

      <div className='flex-grow flex flex-col px-4 overflow-y-auto'>
        {/* Persona Background - Now on top */}
        <div className='mb-2'>
          <h4 className={`text-sm font-semibold mb-1 text-gray-800`}>
            About {persona.name}:
          </h4>
          <p className={`text-sm text-gray-800 leading-relaxed`}>
            {persona.summary.length > 100
              ? `${persona.summary.substring(0, 100)}...`
              : persona.summary}
          </p>
        </div>

        {/* Category Description - Now below */}
        {persona.description && (
          <div className='mb-3'>
            <h4 className={`text-sm font-semibold mb-1 text-gray-800`}>
              {persona.category} Type:
            </h4>
            <p className={`text-sm text-gray-800 leading-relaxed`}>
              {persona.description.length > 120
                ? `${persona.description.substring(0, 120)}...`
                : persona.description}
            </p>
          </div>
        )}
      </div>

      {/* Removed the plus button - entire card is now clickable */}
    </div>
  );
}
