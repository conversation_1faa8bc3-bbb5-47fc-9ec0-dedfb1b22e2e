import React, { useState, useEffect, useRef, useId } from "react";
import { Share2, <PERSON>rkle, X } from "lucide-react";
import { useParams } from "react-router-dom";
import { brandApiService } from "../services/brandApiService";
import type { Brand } from "../services/brandApiService";
import { GlassyButton } from "./GlassyButton";
import { useOverlayState } from "../utils/overlayState";
import { useScrollManager } from "../utils/scrollManager";
import styles from "./Header.module.css";

interface HeaderProps {
  isCollapsed: boolean;
}

export function Header({ isCollapsed }: HeaderProps) {
  const { brandName } = useParams<{ brandName: string }>();

  const [currentBrand, setCurrentBrand] = useState<Brand | null>(null);
  const [loading, setLoading] = useState(true);
  const [showAiTooltip, setShowAiTooltip] = useState(false);
  const aiButtonRef = useRef<HTMLButtonElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const modalId = useId(); // Generate unique ID for this modal instance
  
  // Register AI tooltip with the overlay system
  useOverlayState(showAiTooltip);
  
  // Use the scroll manager to preserve scroll position (Header AI tooltip is detected as regular)
  useScrollManager(modalId, showAiTooltip, 'Header');

  // Load brand based on URL parameter
  useEffect(() => {
    const loadBrand = async () => {
      setLoading(true);
      try {
        if (brandName) {
          const brand = await brandApiService.getBrandByEndpoint(brandName);
          if (brand) {
            setCurrentBrand(brand);
          } else {
            // If brand not found, get default brand
            const defaultBrand = await brandApiService.getDefaultBrand();
            setCurrentBrand(defaultBrand);
          }
        } else {
          // No brand name in URL, get default brand
          const defaultBrand = await brandApiService.getDefaultBrand();
          setCurrentBrand(defaultBrand);
        }
      } catch (error) {
        console.error("Error loading brand in header:", error);
        // Fallback to a basic brand object
        setCurrentBrand({
          id: "fallback",
          name: "Project Roots",
          logo: "",
          endpoint: "fallback",
          clientId: "",
          isDefault: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      } finally {
        setLoading(false);
      }
    };

    loadBrand();
  }, [brandName]);

  // Calculate tooltip position
  const getTooltipPosition = () => {
    if (!aiButtonRef.current) return { top: 0, left: 0 };

    const rect = aiButtonRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current?.getBoundingClientRect();
    const headerHeight = 64; // Header height in pixels
    const gap = 12; // Slightly larger gap between header and tooltip

    // Position tooltip right below the header, aligned to the right edge of AI button
    const top = headerHeight + gap;
    let left = rect.right - (tooltipRect?.width || 320); // Align right edge of tooltip to right edge of AI button

    // Ensure tooltip stays within viewport
    const viewportWidth = window.innerWidth;
    const minLeft = 20;
    const maxLeft = viewportWidth - (tooltipRect?.width || 320) - 20;

    if (left < minLeft) left = minLeft;
    if (left > maxLeft) left = maxLeft;

    return { top, left };
  };

  return (
    <>
      <div className={styles.headerSpacer}></div>
      <header
        className={styles.header}
        style={{ left: isCollapsed ? "64px" : "256px", zIndex: 1040 }}
        data-tour='header'
      >
        <div className={styles.headerContent}>
          <div className={styles.headerBrand}>
            <div className='flex items-start'>
              {/* <span className='header-brand-label'>Name:</span> */}
              <div>
                {/* <h1 className='header-brand-title'>{currentBrand.name}</h1> */}
                <h1 className={styles.headerBrandTitle}>Project Roots</h1>
              </div>
            </div>
          </div>

          <div className={styles.headerActions}>
            <div className={styles.headerUserStatus}>
              <div className={styles.headerUserIndicator}></div>
              <span className={styles.headerUserName}>
                Logged in as: {currentBrand?.client?.firstName || "User"}
              </span>
            </div>

            <GlassyButton
              icon={Share2}
              onClick={() => {
                // Disabled popup modal - just copy link to clipboard
                navigator.clipboard.writeText(window.location.href);
                alert("Link copied to clipboard!");
              }}
              ariaLabel='Share'
            />
            <button
              ref={aiButtonRef}
              onClick={() => setShowAiTooltip(!showAiTooltip)}
              className='header-ai-button group'
              aria-label='AI Bot'
            >
              <Sparkle className='h-5 w-5 transition-transform duration-1000 ease-in-out group-hover:animate-spin' />
            </button>
          </div>
        </div>
      </header>

      {/* AI Tooltip */}
      {showAiTooltip && (
        <>
          {/* Overlay */}
          <div
            className={styles.aiTooltipOverlay}
            onClick={() => setShowAiTooltip(false)}
          />

          {/* Tooltip */}
          <div
            ref={tooltipRef}
            className={styles.aiTooltip}
            style={{
              top: `${getTooltipPosition().top}px`,
              left: `${getTooltipPosition().left}px`,
            }}
          >
            <div className={styles.aiTooltipContent}>
              {/* Header */}
              <div className={styles.aiTooltipHeader}>
                <div></div>
                <button
                  onClick={() => setShowAiTooltip(false)}
                  className={styles.aiTooltipCloseButton}
                  title='Close tooltip'
                >
                  <X className='w-4 h-4 text-gray-500' />
                </button>
              </div>

              {/* Content */}
              <div className={styles.aiTooltipText}>
                <h3>AI Features</h3>
                <div>
                  <p>
                    Want AI chat or instant reports? Those features can be built
                    right in.
                  </p>
                </div>
              </div>

              {/* Actions */}
              <div className={styles.aiTooltipActions}>
                <button
                  onClick={() => setShowAiTooltip(false)}
                  className={styles.aiTooltipButton}
                >
                  Got it
                </button>
              </div>
            </div>

            {/* Arrow */}
            <div className={styles.aiTooltipArrow} />
          </div>
        </>
      )}
    </>
  );
}
