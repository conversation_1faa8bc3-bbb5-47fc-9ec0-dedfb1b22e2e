import React, { useState, useId } from "react";
import { Upload, RotateCcw, Save, Trash2, X } from "lucide-react";
import { uploadService } from "../services/uploadService";
import { Client } from "../services/clientApiService";
import { useOverlayState } from "../utils/overlayState";
import { useScrollManager } from "../utils/scrollManager";

interface ClientModalProps {
  isOpen: boolean;
  onClose: () => void;
  editingClient: Client | null;
  isCreating: boolean;
  onSubmit: (formData: {
    name: string;
    firstName?: string;
    logo: string;
    description: string;
  }) => void;
  onDelete?: (client: Client) => void;
}

export function ClientModal({
  isOpen,
  onClose,
  editingClient,
  isCreating,
  onSubmit,
  onDelete,
}: ClientModalProps) {
  const modalId = useId(); // Generate unique ID for this modal instance
  
  // Register this modal with the overlay system
  useOverlayState(isOpen);
  
  // Use the scroll manager to preserve scroll position (ClientModal is detected as regular)
  useScrollManager(modalId, isOpen, 'ClientModal');
  
  const [formData, setFormData] = useState({
    name: "",
    firstName: "",
    logo: "",
    description: "",
  });
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string>("");
  const [validationError, setValidationError] = useState<string>("");

  // Reset form when modal opens/closes
  React.useEffect(() => {
    if (isOpen) {
      if (editingClient) {
        setFormData({
          name: editingClient.name,
          firstName: editingClient.firstName || "",
          logo: editingClient.logo,
          description: editingClient.description || "",
        });
        setLogoPreview(editingClient.logo);
        setLogoFile(null);
      } else {
        setFormData({
          name: "",
          firstName: "",
          logo: "",
          description: "",
        });
        setLogoPreview("");
        setLogoFile(null);
      }
      setValidationError("");
    }
  }, [isOpen, editingClient]);

  const handleLogoChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    const validation = uploadService.validateFile(file);
    if (!validation.isValid) {
      setValidationError(validation.error || "Invalid file");
      return;
    }

    setLogoFile(file);
    setValidationError("");

    try {
      // Upload to S3
      const uploadResult = await uploadService.uploadLogo(file, "client");
      setLogoPreview(uploadResult.url);
      setFormData((prev) => ({ ...prev, logo: uploadResult.url }));
    } catch (error) {
      console.error("Error uploading logo:", error);
      setValidationError("Failed to upload logo. Please try again.");
    }
  };

  const handleResetLogo = () => {
    const defaultLogo =
      "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzM2N0ZGRiIvPgo8cGF0aCBkPSJNMTIgMjBMMjAgMTJMMjggMjBMMjAgMjhMMTIgMjBaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K";
    setLogoFile(null);
    setLogoPreview(defaultLogo);
    setFormData((prev) => ({ ...prev, logo: defaultLogo }));
    setValidationError("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      setValidationError("Client name is required");
      return;
    }

    if (!formData.logo.trim()) {
      setValidationError("Client logo is required");
      return;
    }

    try {
      onSubmit(formData);
      onClose();
    } catch (error) {
      setValidationError("An error occurred. Please try again.");
    }
  };

  const handleDeleteClient = async () => {
    if (editingClient && onDelete) {
      onDelete(editingClient);
      onClose();
    }
  };

  return (
    <>
      {isOpen && (
        <div className='fixed inset-0 z-50 overflow-y-auto'>
          <div className='flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0'>
            <div
              className='fixed inset-0 transition-opacity bg-black bg-opacity-30 backdrop-blur-sm'
              onClick={onClose}
            ></div>

            <div className='inline-block align-bottom neumorphic-container text-left overflow-hidden transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full'>
              <div className='px-4 pt-5 pb-4 sm:p-6 sm:pb-4'>
                <div className='flex items-center justify-between mb-4'>
                  <h3 className='text-lg font-medium text-primary'>
                    {isCreating ? "Add New Client" : "Edit Client"}
                  </h3>
                  <button
                    onClick={onClose}
                    className='text-gray-400 hover:text-gray-600 transition-colors'
                  >
                    <X className='w-5 h-5' />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className='space-y-4'>
                  {/* Client Name */}
                  <div>
                    <label className='block text-sm font-medium text-primary mb-2'>
                      Client Name *
                    </label>
                    <input
                      type='text'
                      value={formData.name}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          name: e.target.value,
                        }))
                      }
                      className='w-full px-3 py-2 neumorphic-input font-bold'
                      style={{ color: "var(--color-text-primary)" }}
                      placeholder='Enter client name'
                      required
                    />
                  </div>

                  {/* First Name */}
                  <div>
                    <label className='block text-sm font-medium text-primary mb-2'>
                      First Name (for personalized welcome)
                    </label>
                    <input
                      type='text'
                      value={formData.firstName}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          firstName: e.target.value,
                        }))
                      }
                      className='w-full px-3 py-2 neumorphic-input'
                      placeholder='Enter first name for personalized welcome messages'
                    />
                  </div>

                  {/* Client Description */}
                  <div>
                    <label className='block text-sm font-medium text-primary mb-2'>
                      Description
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          description: e.target.value,
                        }))
                      }
                      className='w-full px-3 py-2 neumorphic-input'
                      placeholder='Enter client description'
                      rows={3}
                    />
                  </div>

                  {/* Logo Upload */}
                  <div>
                    <label className='block text-sm font-medium text-primary mb-2'>
                      Client Logo *
                    </label>
                    <div className='space-y-3'>
                      {/* Logo Preview */}
                      {logoPreview && (
                        <div className='flex items-center space-x-3'>
                          <img
                            src={logoPreview}
                            alt='Logo preview'
                            className='w-12 h-12 object-contain rounded-lg bg-gray-50 p-2 border'
                          />
                          <button
                            type='button'
                            onClick={handleResetLogo}
                            className='flex items-center text-sm text-primary hover:text-accent'
                          >
                            <RotateCcw className='w-4 h-4 mr-1' />
                            Reset to Default
                          </button>
                        </div>
                      )}

                      {/* File Upload */}
                      <div className='flex items-center space-x-3'>
                        <label className='flex items-center px-4 py-2 neumorphic-button-secondary cursor-pointer'>
                          <Upload className='w-4 h-4 mr-2' />
                          Choose File
                          <input
                            type='file'
                            accept='image/jpeg,image/jpg,image/png,image/gif'
                            onChange={handleLogoChange}
                            className='hidden'
                          />
                        </label>
                        {logoFile && (
                          <span className='text-sm text-primary'>
                            {logoFile.name}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Validation Error */}
                  {validationError && (
                    <div className='text-red-600 text-sm'>
                      {validationError}
                    </div>
                  )}

                  {/* Form Actions */}
                  <div className='flex justify-between pt-4'>
                    <div>
                      {!isCreating && editingClient && onDelete && (
                        <button
                          type='button'
                          onClick={handleDeleteClient}
                          className='p-3 neumorphic-button-secondary text-red-600 hover:text-red-700'
                          title='Delete Client'
                        >
                          <Trash2 className='w-5 h-5' />
                        </button>
                      )}
                    </div>
                    <div className='flex space-x-3'>
                      <button
                        type='button'
                        onClick={onClose}
                        className='px-4 py-2 neumorphic-button-secondary'
                      >
                        Cancel
                      </button>
                      <button
                        type='submit'
                        className='p-3 neumorphic-button-primary'
                        title={isCreating ? "Create Client" : "Update Client"}
                      >
                        <Save className='w-5 h-5' />
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
