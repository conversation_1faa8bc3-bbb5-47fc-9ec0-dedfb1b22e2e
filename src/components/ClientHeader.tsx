import React, { useState, useEffect, useId } from "react";
import { Settings, X, Upload, RotateCcw, Save } from "lucide-react";
import {
  getClientConfig,
  saveClientConfig,
  resetClientConfig,
  defaultClientConfig,
  ClientConfig,
  resetToBloodAndTreasure,
} from "../config/clientConfig";
import { validateLogoFile, fileToDataUrl } from "../utils/fileUtils";
import { useScrollManager } from "../utils/scrollManager";

interface ClientHeaderProps {
  className?: string;
}

export const ClientHeader: React.FC<ClientHeaderProps> = ({
  className = "",
}) => {
  const [clientConfig, setClientConfig] = useState<ClientConfig>(
    getClientConfig()
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const modalId = useId(); // Generate unique ID for this modal instance
  const [formData, setFormData] = useState<ClientConfig>(clientConfig);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string>(clientConfig.logo);
  const [validationError, setValidationError] = useState<string>("");

  // Update local state when localStorage changes
  useEffect(() => {
    setClientConfig(getClientConfig());
  }, []);

  // Use the scroll manager to preserve scroll position (ClientHeader modal is detected as regular)
  useScrollManager(modalId, isModalOpen, 'ClientHeader');

  const handleOpenModal = () => {
    setFormData(clientConfig);
    setLogoPreview(clientConfig.logo);
    setLogoFile(null);
    setValidationError("");
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setFormData(clientConfig);
    setLogoFile(null);
    setLogoPreview(clientConfig.logo);
    setValidationError("");
  };

  const handleLogoChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validation = validateLogoFile(file);
    if (!validation.isValid) {
      setValidationError(validation.error || "Invalid file");
      return;
    }

    setValidationError("");
    setLogoFile(file);

    try {
      const dataUrl = await fileToDataUrl(file);
      setLogoPreview(dataUrl);
      setFormData((prev) => ({ ...prev, logo: dataUrl }));
    } catch (error) {
      setValidationError("Error processing logo file");
    }
  };

  const handleResetToDefault = () => {
    setFormData(defaultClientConfig);
    setLogoFile(null);
    setLogoPreview(defaultClientConfig.logo);
    setValidationError("");
  };

  const handleResetToBloodAndTreasure = () => {
    resetToBloodAndTreasure();
    setClientConfig(defaultClientConfig);
    setFormData(defaultClientConfig);
    setLogoFile(null);
    setLogoPreview(defaultClientConfig.logo);
    setValidationError("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      setValidationError("Client name is required");
      return;
    }

    try {
      saveClientConfig(formData);
      setClientConfig(formData);

      // Dispatch custom event to notify other components (like Sidebar) of the change
      window.dispatchEvent(
        new CustomEvent("clientConfigChanged", {
          detail: { config: formData },
        })
      );

      handleCloseModal();
    } catch (error) {
      setValidationError(
        error instanceof Error ? error.message : "An error occurred"
      );
    }
  };

  return (
    <>
      <div className={`mb-6 ${className}`}>
        <div className='flex items-center space-x-4'>
          {/* Client Logo */}
          <div className='flex-shrink-0'>
            <img
              src={clientConfig.logo}
              alt={`${clientConfig.name} logo`}
              className='w-12 h-12 object-contain rounded-lg bg-gray-50 p-2 shadow-inner'
            />
          </div>

          {/* Client Name and Settings */}
          <div className='flex-1 flex items-center -mt-1.5'>
            <div>
              <h2 className='text-xl font-bold text-primary '>
                {clientConfig.name}
              </h2>
              {/* {clientConfig.description && (
                <p className='text-sm text-gray-600 '>
                  {clientConfig.description}
                </p>
              )} */}
            </div>

            {/* Settings Button */}
            {/* <button
              onClick={handleOpenModal}
              className='p-2 neumorphic-button-secondary rounded-lg ml-6'
              title='Edit Client Settings'
            >
              <Settings className='w-5 h-5' />
            </button> */}
          </div>
        </div>
      </div>

      {/* Client Settings Modal */}
      {isModalOpen && (
        <div className='fixed inset-0 z-50 overflow-y-auto'>
          <div className='flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0'>
            <div
              className='fixed inset-0 transition-opacity bg-black bg-opacity-30 backdrop-blur-sm'
              onClick={handleCloseModal}
            ></div>

            <div className='inline-block align-bottom neumorphic-container text-left overflow-hidden transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full'>
              <div className='px-4 pt-5 pb-4 sm:p-6 sm:pb-4'>
                <div className='flex items-center justify-between mb-4'>
                  <h3 className='text-lg font-medium text-primary'>
                    Edit Client Settings
                  </h3>
                  <button
                    onClick={handleCloseModal}
                    className='text-gray-400 hover:text-gray-600 transition-colors'
                  >
                    <X className='w-5 h-5' />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className='space-y-4'>
                  {/* Client Name */}
                  <div>
                    <label className='block text-sm font-medium text-primary mb-2'>
                      Client Name *
                    </label>
                    <input
                      type='text'
                      value={formData.name}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          name: e.target.value,
                        }))
                      }
                      className='w-full px-3 py-2 neumorphic-input font-bold'
                      style={{ color: "var(--color-text-primary)" }}
                      placeholder='Enter client name'
                      required
                    />
                  </div>

                  {/* Logo Upload */}
                  <div>
                    <label className='block text-sm font-medium text-primary mb-2'>
                      Client Logo
                    </label>
                    <div className='space-y-3'>
                      {/* Logo Preview */}
                      {logoPreview && (
                        <div className='flex items-center space-x-3'>
                          <img
                            src={logoPreview}
                            alt='Logo preview'
                            className='w-12 h-12 object-contain rounded-lg bg-gray-50 p-2 border'
                          />
                          <button
                            type='button'
                            onClick={handleResetToBloodAndTreasure}
                            className='flex items-center text-sm text-primary hover:text-accent'
                          >
                            <RotateCcw className='w-4 h-4 mr-1' />
                            Reset to Blood and Treasure
                          </button>
                        </div>
                      )}

                      {/* File Upload */}
                      <div className='flex items-center space-x-3'>
                        <label className='flex items-center px-4 py-2 neumorphic-button-secondary cursor-pointer'>
                          <Upload className='w-4 h-4 mr-2' />
                          Choose File
                          <input
                            type='file'
                            accept='image/jpeg,image/jpg,image/png'
                            onChange={handleLogoChange}
                            className='hidden'
                          />
                        </label>
                        {logoFile && (
                          <span className='text-sm text-primary'>
                            {logoFile.name}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Validation Error */}
                  {validationError && (
                    <div className='text-red-600 text-sm'>
                      {validationError}
                    </div>
                  )}

                  {/* Form Actions */}
                  <div className='flex justify-end space-x-3 pt-4'>
                    <button
                      type='button'
                      onClick={handleCloseModal}
                      className='px-4 py-2 neumorphic-button-secondary'
                    >
                      Cancel
                    </button>
                    <button
                      type='submit'
                      className='p-3 neumorphic-button-primary'
                      title='Save Client Settings'
                    >
                      <Save className='w-5 h-5' />
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
