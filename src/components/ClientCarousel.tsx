import React, { useState, useRef, useEffect } from "react";
import { ChevronLeft, ChevronRight, Edit, Trash2, Plus } from "lucide-react";
import { clientApiService, Client } from "../services";

interface ClientCarouselProps {
  selectedClientId: string | null;
  onClientSelect: (clientId: string | null) => void;
  onAddClient: () => void;
  onEditClient: (client: Client) => void;
  clients: Client[];
  onDeleteClient?: (client: Client) => void;
}

export function ClientCarousel({
  selectedClientId,
  onClientSelect,
  onAddClient,
  onEditClient,
  clients,
  onDeleteClient,
}: ClientCarouselProps) {
  const [brandCounts, setBrandCounts] = useState<Record<string, number>>({});
  const [showArrows, setShowArrows] = useState(false);
  const carouselRef = useRef<HTMLDivElement>(null);

  // Load brand counts from backend
  useEffect(() => {
    const loadBrandCounts = async () => {
      const counts: Record<string, number> = {};
      for (const client of clients) {
        try {
          counts[client.id] = await clientApiService.getClientBrandCount(
            client.id
          );
        } catch (error) {
          console.error(
            `Error loading brand count for client ${client.id}:`,
            error
          );
          counts[client.id] = 0;
        }
      }
      setBrandCounts(counts);
    };

    if (clients.length > 0) {
      loadBrandCounts();
    }
  }, [clients]);

  // Check if arrows should be shown
  useEffect(() => {
    const checkArrows = () => {
      if (carouselRef.current) {
        const { scrollWidth, clientWidth } = carouselRef.current;
        setShowArrows(scrollWidth > clientWidth);
      }
    };

    checkArrows();
    window.addEventListener("resize", checkArrows);
    return () => window.removeEventListener("resize", checkArrows);
  }, [clients]);

  const scrollLeft = () => {
    if (carouselRef.current) {
      carouselRef.current.scrollBy({ left: -300, behavior: "smooth" });
    }
  };

  const scrollRight = () => {
    if (carouselRef.current) {
      carouselRef.current.scrollBy({ left: 300, behavior: "smooth" });
    }
  };

  const handleClientClick = (clientId: string) => {
    onClientSelect(selectedClientId === clientId ? null : clientId);
  };

  const handleDeleteClient = async (client: Client, e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDeleteClient) {
      onDeleteClient(client);
    }
  };

  const handleEditClient = (client: Client, e: React.MouseEvent) => {
    e.stopPropagation();
    onEditClient(client);
  };

  return (
    <div className='relative'>
      {/* Navigation Arrows */}
      {showArrows && (
        <>
          <button
            onClick={scrollLeft}
            className='absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 neumorphic-button-secondary rounded-full shadow-lg'
            aria-label='Scroll left'
          >
            <ChevronLeft className='w-5 h-5' />
          </button>
          <button
            onClick={scrollRight}
            className='absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 neumorphic-button-secondary rounded-full shadow-lg'
            aria-label='Scroll right'
          >
            <ChevronRight className='w-5 h-5' />
          </button>
        </>
      )}

      {/* Carousel Container */}
      <div
        ref={carouselRef}
        className='flex gap-4 overflow-x-auto scrollbar-hide px-4 py-2'
        style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
      >
        {/* Client Cards */}
        {clients.map((client) => (
          <div
            key={client.id}
            onClick={() => handleClientClick(client.id)}
            className={`flex-shrink-0 w-48 h-32 neumorphic-container p-4 cursor-pointer transition-all duration-300 relative group ${
              selectedClientId === client.id
                ? "ring-2 ring-blue-500 shadow-lg"
                : "hover:scale-105"
            }`}
          >
            {/* Client Info */}
            <div className='flex flex-col h-full'>
              <div className='flex items-center space-x-3 mb-3'>
                <img
                  src={client.logo}
                  alt={client.name}
                  className='w-8 h-8 object-contain rounded-lg bg-gray-50 p-1'
                />
                <div className='flex-1 min-w-0'>
                  <h3 className='text-sm font-semibold text-primary truncate'>
                    {client.name}
                  </h3>
                </div>
              </div>

              <div className='flex-1 flex items-end'>
                <span className='text-xs text-white'>
                  {brandCounts[client.id] || 0} brands
                </span>
              </div>
            </div>

            {/* Hover Actions - Bottom Right */}
            <div className='absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1'>
              <button
                onClick={(e) => handleEditClient(client, e)}
                className='p-2 neumorphic-button-secondary rounded-lg'
                title='Edit Client'
              >
                <Edit className='w-4 h-4' />
              </button>
              {onDeleteClient && (
                <button
                  onClick={(e) => handleDeleteClient(client, e)}
                  className='p-2 neumorphic-button-secondary text-red-600 hover:text-red-700 rounded-lg'
                  title='Delete Client'
                >
                  <Trash2 className='w-4 h-4' />
                </button>
              )}
            </div>
          </div>
        ))}

        {/* Add Client Card - Now positioned at the rightmost */}
        <div
          onClick={onAddClient}
          className='flex-shrink-0 w-48 h-32 neumorphic-container p-4 cursor-pointer transition-all duration-300 hover:scale-105 flex flex-col items-center justify-center border-2 border-dashed border-gray-300 hover:border-blue-400'
        >
          <Plus className='w-8 h-8 text-white mb-2' />
          <span className='text-sm text-white font-medium'>Add Client</span>
        </div>
      </div>

      {/* Hide scrollbar for webkit browsers */}
      <style>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
}
