/* Button Component CSS Module */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-tight);
  transition: all var(--transition-base);
  border: none;
  cursor: pointer;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Size variants */
.small {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-xs);
}

.medium {
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
}

.large {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-base);
}

/* Style variants */
.primary {
  background: var(--color-primary-600);
  color: white;
  border: 1px solid var(--color-primary-600);
}

.primary:hover {
  background: var(--color-primary-700);
  border-color: var(--color-primary-700);
}

.primary:active {
  background: var(--color-primary-800);
  border-color: var(--color-primary-800);
}

.secondary {
  background: var(--color-secondary-600);
  color: white;
  border: 1px solid var(--color-secondary-600);
}

.secondary:hover {
  background: var(--color-secondary-700);
  border-color: var(--color-secondary-700);
}

.secondary:active {
  background: var(--color-secondary-800);
  border-color: var(--color-secondary-800);
}

.outline {
  background: transparent;
  color: var(--color-primary-600);
  border: 1px solid var(--color-primary-600);
}

.outline:hover {
  background: var(--color-primary-50);
  color: var(--color-primary-700);
  border-color: var(--color-primary-700);
}

.outline:active {
  background: var(--color-primary-100);
}

.ghost {
  background: transparent;
  color: var(--color-text-secondary);
  border: 1px solid transparent;
}

.ghost:hover {
  background: var(--color-gray-100);
  color: var(--color-text-primary);
}

.ghost:active {
  background: var(--color-gray-200);
}

.link {
  background: transparent;
  color: var(--color-primary-600);
  border: none;
  padding: 0;
  text-decoration: underline;
  text-underline-offset: 2px;
}

.link:hover {
  color: var(--color-primary-700);
  text-decoration: none;
}

/* Icon buttons */
.iconOnly {
  padding: var(--spacing-3);
  aspect-ratio: 1;
}

.iconOnly.small {
  padding: var(--spacing-2);
}

.iconOnly.large {
  padding: var(--spacing-4);
}

/* Button with icon and text */
.withIcon {
  gap: var(--spacing-2);
}

.icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.iconLeft {
  margin-right: var(--spacing-2);
}

.iconRight {
  margin-left: var(--spacing-2);
}

/* Loading state */
.loading {
  position: relative;
  color: transparent;
}

.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Full width */
.fullWidth {
  width: 100%;
}

/* Danger variant */
.danger {
  background: var(--color-error-600);
  color: white;
  border: 1px solid var(--color-error-600);
}

.danger:hover {
  background: var(--color-error-700);
  border-color: var(--color-error-700);
}

.danger:active {
  background: var(--color-error-800);
  border-color: var(--color-error-800);
}

/* Success variant */
.success {
  background: var(--color-success-600);
  color: white;
  border: 1px solid var(--color-success-600);
}

.success:hover {
  background: var(--color-success-700);
  border-color: var(--color-success-700);
}

/* Warning variant */
.warning {
  background: var(--color-warning-600);
  color: white;
  border: 1px solid var(--color-warning-600);
}

.warning:hover {
  background: var(--color-warning-700);
  border-color: var(--color-warning-700);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .btn {
    padding: var(--spacing-3) var(--spacing-4);
  }
  
  .small {
    padding: var(--spacing-2) var(--spacing-3);
  }
  
  .large {
    padding: var(--spacing-3) var(--spacing-5);
  }
}