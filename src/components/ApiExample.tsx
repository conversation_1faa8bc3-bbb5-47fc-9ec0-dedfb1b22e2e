import React, { useState, useEffect } from "react";
import { useApi } from "../hooks/useApi";
import { clientApiService, brandApiService, uploadService } from "../services";
import { formatApiError } from "../utils/apiUtils";
import { API_CONFIG } from "../config/apiConfig";
import type {
  Client,
  Brand,
  CreateClientData,
  CreateBrandData,
} from "../services";

// Example component demonstrating API usage
export const ApiExample: React.FC = () => {
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [newClientData, setNewClientData] = useState<CreateClientData>({
    name: "",
    logo: "",
    description: "",
  });
  const [newBrandData, setNewBrandData] = useState<CreateBrandData>({
    name: "",
    logo: "",
    endpoint: "",
    clientId: "",
  });

  // API hooks - Updated to use API services instead of localStorage services
  const [clientsState, clientsActions] = useApi(clientApiService.getAllClients);
  const [brandsState, brandsActions] = useApi(brandApiService.getAllBrands);
  const [createClientState, createClientActions] = useApi(
    clientApiService.createClient
  );
  const [createBrandState, createBrandActions] = useApi(
    brandApiService.createBrand
  );

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([clientsActions.execute(), brandsActions.execute()]);
    } catch (error) {
      console.error("Error loading data:", error);
    }
  };

  const handleCreateClient = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newClientData.name.trim() || !newClientData.logo.trim()) {
      alert("Name and logo are required");
      return;
    }

    try {
      await createClientActions.execute(newClientData);
      setNewClientData({ name: "", logo: "", description: "" });
      await clientsActions.execute(); // Reload clients
      alert(API_CONFIG.SUCCESS.CLIENT_CREATED);
    } catch (error) {
      alert(`Error creating client: ${formatApiError(error as any)}`);
    }
  };

  const handleCreateBrand = async (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !newBrandData.name.trim() ||
      !newBrandData.logo.trim() ||
      !newBrandData.clientId
    ) {
      alert("Name, logo, and client are required");
      return;
    }

    try {
      // Generate endpoint from name
      newBrandData.endpoint = brandApiService.nameToEndpoint(newBrandData.name);

      await createBrandActions.execute(newBrandData);
      setNewBrandData({ name: "", logo: "", endpoint: "", clientId: "" });
      await brandsActions.execute(); // Reload brands
      alert(API_CONFIG.SUCCESS.BRAND_CREATED);
    } catch (error) {
      alert(`Error creating brand: ${formatApiError(error as any)}`);
    }
  };

  const handleFileUpload = async (file: File, type: "client" | "brand") => {
    try {
      const uploadResult = await uploadService.uploadLogo(file, type);

      if (type === "client") {
        setNewClientData((prev) => ({ ...prev, logo: uploadResult.url }));
      } else {
        setNewBrandData((prev) => ({ ...prev, logo: uploadResult.url }));
      }

      alert(API_CONFIG.SUCCESS.FILE_UPLOADED);
    } catch (error) {
      alert(`Error uploading file: ${formatApiError(error as any)}`);
    }
  };

  const handleFileChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    type: "client" | "brand"
  ) => {
    const file = e.target.files?.[0];
    if (file) {
      const validation = uploadService.validateFile(file);
      if (validation.isValid) {
        handleFileUpload(file, type);
      } else {
        alert(validation.error);
      }
    }
  };

  return (
    <div className='p-6 space-y-6'>
      <h1 className='text-2xl font-bold mb-6'>API Integration Example</h1>

      {/* Clients Section */}
      <div className='bg-white rounded-lg shadow p-6'>
        <h2 className='text-xl font-semibold mb-4'>Clients</h2>

        {/* Create Client Form */}
        <form onSubmit={handleCreateClient} className='mb-6 space-y-4'>
          <div>
            <label className='block text-sm font-medium mb-2'>
              Client Name
            </label>
            <input
              type='text'
              value={newClientData.name}
              onChange={(e) =>
                setNewClientData((prev) => ({ ...prev, name: e.target.value }))
              }
              className='w-full px-3 py-2 border rounded-md'
              placeholder='Enter client name'
              required
            />
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>Logo</label>
            <input
              type='file'
              accept='image/*'
              onChange={(e) => handleFileChange(e, "client")}
              className='w-full px-3 py-2 border rounded-md'
            />
            {newClientData.logo && (
              <img
                src={newClientData.logo}
                alt='Preview'
                className='mt-2 h-20 w-20 object-contain border rounded'
              />
            )}
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Description
            </label>
            <textarea
              value={newClientData.description}
              onChange={(e) =>
                setNewClientData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              className='w-full px-3 py-2 border rounded-md'
              placeholder='Enter description'
              rows={3}
            />
          </div>

          <button
            type='submit'
            disabled={createClientState.loading}
            className='px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50'
          >
            {createClientState.loading ? "Creating..." : "Create Client"}
          </button>
        </form>

        {/* Clients List */}
        <div>
          <h3 className='text-lg font-medium mb-3'>Existing Clients</h3>
          {clientsState.loading ? (
            <p>Loading clients...</p>
          ) : clientsState.error ? (
            <p className='text-red-500'>
              Error: {formatApiError(clientsState.error)}
            </p>
          ) : (
            <div className='space-y-2'>
              {clientsState.data?.map((client) => (
                <div
                  key={client.id}
                  className='flex items-center space-x-4 p-3 border rounded'
                >
                  <img
                    src={client.logo}
                    alt={client.name}
                    className='h-10 w-10 object-contain'
                  />
                  <div>
                    <h4 className='font-medium'>{client.name}</h4>
                    {client.description && (
                      <p className='text-sm text-gray-600'>
                        {client.description}
                      </p>
                    )}
                  </div>
                  <button
                    onClick={() => setSelectedClient(client)}
                    className='px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600'
                  >
                    Select
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Brands Section */}
      <div className='bg-white rounded-lg shadow p-6'>
        <h2 className='text-xl font-semibold mb-4'>Brands</h2>

        {/* Create Brand Form */}
        <form onSubmit={handleCreateBrand} className='mb-6 space-y-4'>
          <div>
            <label className='block text-sm font-medium mb-2'>Brand Name</label>
            <input
              type='text'
              value={newBrandData.name}
              onChange={(e) =>
                setNewBrandData((prev) => ({ ...prev, name: e.target.value }))
              }
              className='w-full px-3 py-2 border rounded-md'
              placeholder='Enter brand name'
              required
            />
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>Logo</label>
            <input
              type='file'
              accept='image/*'
              onChange={(e) => handleFileChange(e, "brand")}
              className='w-full px-3 py-2 border rounded-md'
            />
            {newBrandData.logo && (
              <img
                src={newBrandData.logo}
                alt='Preview'
                className='mt-2 h-20 w-20 object-contain border rounded'
              />
            )}
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>Client</label>
            <select
              value={newBrandData.clientId}
              onChange={(e) =>
                setNewBrandData((prev) => ({
                  ...prev,
                  clientId: e.target.value,
                }))
              }
              className='w-full px-3 py-2 border rounded-md'
              required
            >
              <option value=''>Select a client</option>
              {clientsState.data?.map((client) => (
                <option key={client.id} value={client.id}>
                  {client.name}
                </option>
              ))}
            </select>
          </div>

          <button
            type='submit'
            disabled={createBrandState.loading}
            className='px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50'
          >
            {createBrandState.loading ? "Creating..." : "Create Brand"}
          </button>
        </form>

        {/* Brands List */}
        <div>
          <h3 className='text-lg font-medium mb-3'>Existing Brands</h3>
          {brandsState.loading ? (
            <p>Loading brands...</p>
          ) : brandsState.error ? (
            <p className='text-red-500'>
              Error: {formatApiError(brandsState.error)}
            </p>
          ) : (
            <div className='space-y-2'>
              {brandsState.data?.map((brand) => (
                <div
                  key={brand.id}
                  className='flex items-center space-x-4 p-3 border rounded'
                >
                  <img
                    src={brand.logo}
                    alt={brand.name}
                    className='h-10 w-10 object-contain'
                  />
                  <div>
                    <h4 className='font-medium'>{brand.name}</h4>
                    <p className='text-sm text-gray-600'>
                      Endpoint: {brand.endpoint}
                    </p>
                    {brand.client && (
                      <p className='text-sm text-gray-500'>
                        Client: {brand.client.name}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Selected Client Info */}
      {selectedClient && (
        <div className='bg-blue-50 rounded-lg p-4'>
          <h3 className='text-lg font-medium mb-2'>Selected Client</h3>
          <div className='flex items-center space-x-4'>
            <img
              src={selectedClient.logo}
              alt={selectedClient.name}
              className='h-16 w-16 object-contain'
            />
            <div>
              <h4 className='font-medium'>{selectedClient.name}</h4>
              {selectedClient.description && (
                <p className='text-sm text-gray-600'>
                  {selectedClient.description}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
