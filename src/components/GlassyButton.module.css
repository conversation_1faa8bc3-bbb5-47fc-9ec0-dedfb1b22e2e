/* GlassyButton Component CSS Module */

.glassyButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.glassyButton:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--color-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.glassyButton:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.glassyButton:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.glassyButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.icon {
  width: 16px;
  height: 16px;
}

/* Size variants */
.small {
  width: 32px;
  height: 32px;
}

.small .icon {
  width: 14px;
  height: 14px;
}

.large {
  width: 48px;
  height: 48px;
}

.large .icon {
  width: 20px;
  height: 20px;
}

/* Color variants */
.primary {
  color: var(--color-primary-600);
  border-color: rgba(59, 130, 246, 0.2);
}

.primary:hover {
  color: var(--color-primary-700);
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.secondary {
  color: var(--color-secondary-600);
  border-color: rgba(168, 85, 247, 0.2);
}

.secondary:hover {
  color: var(--color-secondary-700);
  background: rgba(168, 85, 247, 0.1);
  border-color: rgba(168, 85, 247, 0.3);
}

.success {
  color: var(--color-success-600);
  border-color: rgba(34, 197, 94, 0.2);
}

.success:hover {
  color: var(--color-success-700);
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
}

.warning {
  color: var(--color-warning-600);
  border-color: rgba(245, 158, 11, 0.2);
}

.warning:hover {
  color: var(--color-warning-700);
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

.error {
  color: var(--color-error-600);
  border-color: rgba(239, 68, 68, 0.2);
}

.error:hover {
  color: var(--color-error-700);
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .glassyButton {
    width: 36px;
    height: 36px;
  }
  
  .icon {
    width: 14px;
    height: 14px;
  }
}