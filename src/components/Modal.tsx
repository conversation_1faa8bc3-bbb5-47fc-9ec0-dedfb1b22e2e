import React, { useEffect, useState, useId } from "react";
import { X } from "lucide-react";
import { useOverlayState } from "../utils/overlayState";
import { useScrollManager } from "../utils/scrollManager";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  showCloseButton?: boolean;
  className?: string;
}

export function Modal({
  isOpen,
  onClose,
  title,
  children,
  showCloseButton = true,
  className = "",
}: ModalProps) {
  const [isAnimating, setIsAnimating] = useState(false);
  const modalId = useId(); // Generate unique ID for this modal instance
  
  // Register this modal with the overlay system
  useOverlayState(isOpen);
  
  // Use the scroll manager to preserve scroll position (Modal is detected as regular)
  useScrollManager(modalId, isOpen, 'Modal');

  useEffect(() => {
    if (isOpen) {
      setIsAnimating(true);
    }
  }, [isOpen]);

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleClose = () => {
    setIsAnimating(false);
    // Small delay to allow animation to complete
    setTimeout(() => {
      onClose();
    }, 150);
  };

  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 z-[100] overflow-y-auto transition-opacity duration-200 ${
        isAnimating ? "opacity-100" : "opacity-0"
      }`}
      aria-labelledby='modal-title'
      role='dialog'
      aria-modal='true'
    >
      {/* Backdrop */}
      <div
        className='fixed inset-0 bg-black/75 backdrop-blur-sm transition-opacity duration-200'
        onClick={handleBackdropClick}
      />

      {/* Modal Container */}
      <div className='flex min-h-screen items-center justify-center p-4'>
        <div
          className={`relative w-full max-w-2xl transform overflow-hidden rounded-xl bg-white shadow-2xl transition-all duration-200 ${
            isAnimating ? "scale-100 opacity-100" : "scale-95 opacity-0"
          } ${className}`}
        >
          {/* Header */}
          {(title || showCloseButton) && (
            <div className='flex items-center justify-between p-6 border-b border-gray-100'>
              {title && (
                <h2 className='text-lg font-semibold text-gray-900'>{title}</h2>
              )}
              {showCloseButton && (
                <button
                  onClick={handleClose}
                  className='p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200'
                  aria-label='Close modal'
                >
                  <X className='h-5 w-5 text-gray-500' />
                </button>
              )}
            </div>
          )}

          {/* Content */}
          <div className='p-6'>{children}</div>
        </div>
      </div>
    </div>
  );
}
