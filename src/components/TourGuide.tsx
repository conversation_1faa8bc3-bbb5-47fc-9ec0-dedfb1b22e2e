import React, { useState, useEffect, useRef, useId } from "react";
import { X, ChevronLeft, ChevronRight, SkipForward } from "lucide-react";
import type { Brand } from "../services/brandApiService";
import { useOverlayState } from "../utils/overlayState";
import { useScrollManager } from "../utils/scrollManager";
import { scrollManager } from "../utils/scrollManager";

// Helper function to render highlighted text with markdown-style bold markers
const renderHighlightedText = (text: string) => {
  const parts = text.split(/(\*\*.*?\*\*)/g);
  return parts.map((part, index) => {
    if (part.startsWith('**') && part.endsWith('**')) {
      // Remove the ** markers and apply highlighting
      const highlightedText = part.slice(2, -2);
      return (
        <span 
          key={index} 
          className="font-bold text-blue-300 bg-blue-900/20 px-1 rounded"
        >
          {highlightedText}
        </span>
      );
    }
    return part;
  });
};

export interface TourStep {
  id: string;
  target: string;
  title: string;
  content: string;
  position?: "top" | "bottom" | "left" | "right";
  action?: "click" | "hover" | "none";
  // Add navigation section ID for automatic navigation
  navigateTo?: string;
}

interface TourGuideProps {
  steps: TourStep[];
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
  tourId: string;
  currentBrand?: Brand | null;
  // Add navigation callback prop
  onNavigate?: (sectionId: string) => void;
  // Add sidebar state prop for boundary calculations
  isCollapsed?: boolean;
}

export function TourGuide({
  steps,
  isOpen,
  onClose,
  onComplete,
  tourId,
  currentBrand,
  onNavigate,
  isCollapsed = false,
}: TourGuideProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const modalId = useId(); // Generate unique ID for this modal instance
  
  // Register this tour with the overlay system
  useOverlayState(isOpen);
  
  // Use the scroll manager to preserve scroll position (TourGuide is detected as tooltip)
  useScrollManager(modalId, isOpen, 'TourGuide');

  // Helper function to detect if target is in sidebar navigation
  const isSidebarNavTarget = (target: HTMLElement): boolean => {
    return target.closest('.sidebar') !== null || 
           target.closest('[data-tour="nav"]') !== null ||
           target.closest('.nav-item') !== null;
  };

  useEffect(() => {
    if (!isOpen) return;

    const target = document.querySelector(
      steps[currentStep]?.target
    ) as HTMLElement;
    setTargetElement(target);

    if (target) {
      // Add highlight class to target element, but skip for header to avoid positioning issues
      if (!target.classList.contains("header")) {
        target.classList.add("tour-highlight");
      }

      // Check if target is in sidebar navigation
      const isSidebarTarget = isSidebarNavTarget(target);

      // Scroll target into view if needed
      target.scrollIntoView({
        behavior: "smooth",
        block: "center",
        inline: "center",
      });

      // For sidebar nav targets, temporarily prevent user scrolling
      // but allow programmatic scrolling (scrollIntoView)
      if (isSidebarTarget) {
        // Temporarily prevent user scrolling while allowing programmatic scrolling
        const originalOverflow = document.body.style.overflow;
        document.body.style.overflow = 'hidden';
        
        // Re-enable scrolling after scrollIntoView completes
        setTimeout(() => {
          document.body.style.overflow = originalOverflow;
        }, 1000);
      }

      // Auto-navigate if this step has a navigation target
      const step = steps[currentStep];
      if (step?.navigateTo && onNavigate) {
        // Small delay to ensure the target element is highlighted first
        setTimeout(() => {
          onNavigate(step.navigateTo!);
        }, 300);
      }
    }

    return () => {
      if (target) {
        target.classList.remove("tour-highlight");
      }
    };
  }, [currentStep, steps, isOpen, onNavigate]);

  useEffect(() => {
    if (!isOpen) {
      setCurrentStep(0);
      // Remove all tour highlights
      document.querySelectorAll(".tour-highlight").forEach((el) => {
        el.classList.remove("tour-highlight");
      });
    }
  }, [isOpen]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    onComplete();
  };

  if (!isOpen || !targetElement) return null;

  const step = steps[currentStep];
  const rect = targetElement.getBoundingClientRect();
  const tooltipRect = tooltipRef.current?.getBoundingClientRect();

  // Calculate position
  const getTooltipPosition = () => {
    // Special case for first step - center on page
    if (currentStep === 0) {
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      return {
        top: viewportHeight / 2 - 250, // Move higher on screen
        left: viewportWidth / 2 - 300, // Adjust for wider tooltip
      };
    }

    const position = step.position || "bottom";
    const margin = 12;

    let top = 0;
    let left = 0;

    switch (position) {
      case "top":
        top = rect.top - margin;
        left = rect.left + rect.width / 2;
        break;
      case "bottom":
        top = rect.bottom + margin;
        left = rect.left + rect.width / 2;
        break;
      case "left":
        top = rect.top + rect.height / 2;
        left = rect.left - margin;
        break;
      case "right":
        top = rect.top + rect.height / 2;
        left = rect.right + margin;
        break;
    }

    // Adjust for tooltip size
    if (tooltipRect) {
      switch (position) {
        case "top":
          top -= tooltipRect.height;
          left -= tooltipRect.width / 2;
          break;
        case "bottom":
          left -= tooltipRect.width / 2;
          break;
        case "left":
          top -= tooltipRect.height / 2;
          left -= tooltipRect.width;
          break;
        case "right":
          top -= tooltipRect.height / 2;
          break;
      }
    }

    // Define boundaries
    const headerHeight = 64; // Header height in pixels
    const sidebarWidth = isCollapsed ? 64 : 256; // Sidebar width based on state
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Ensure tooltip stays within viewport and respects header/sidebar boundaries
    const minLeft = sidebarWidth + 20; // Minimum left position (sidebar width + margin)
    const maxLeft = viewportWidth - (tooltipRect?.width || 0) - 20; // Maximum left position
    const minTop = headerHeight + 20; // Minimum top position (header height + margin)
    const maxTop = viewportHeight - (tooltipRect?.height || 0) - 20; // Maximum top position

    // Adjust left position to respect sidebar boundary
    if (left < minLeft) left = minLeft;
    if (left > maxLeft) left = maxLeft;

    // Adjust top position to respect header boundary
    if (top < minTop) {
      // If tooltip would appear above header, try to position it below the target
      if (
        position === "top" &&
        rect.bottom + margin + (tooltipRect?.height || 0) < viewportHeight - 20
      ) {
        top = rect.bottom + margin;
        if (tooltipRect) {
          left = rect.left + rect.width / 2 - tooltipRect.width / 2;
        }
      } else {
        top = minTop;
      }
    }
    if (top > maxTop) top = maxTop;

    return { top, left };
  };

  const position = getTooltipPosition();

  return (
    <>
      {/* Overlay */}
      <div
        className={`fixed inset-0 bg-black z-40 ${
          currentStep === 0 
            ? 'bg-opacity-50 backdrop-blur-xl' 
            : 'bg-opacity-30 backdrop-blur-sm'
        }`}
        onClick={handleNext}
      />

      {/* Tooltip */}
      <div
        ref={tooltipRef}
        className={`fixed z-50 ${currentStep === 0 ? "max-w-2xl" : "max-w-sm"}`}
        style={{
          top: `${position.top}px`,
          left: `${position.left}px`,
        }}
      >
        <div
          className={`neumorphic-container shadow-2xl ${
            currentStep === 0 ? "p-12" : "p-6"
          }`}
        >
          {/* Header */}
          <div className='flex items-center justify-between mb-4'>
            {currentStep === 0 ? (
              // Welcome card - no step indicator
              <div></div>
            ) : (
              // Regular tour steps - show step indicator
              <div className='flex flex-col space-y-2'>
                <div className='flex items-center space-x-2'>
                  <div className='w-2 h-2 bg-blue-500 rounded-full animate-pulse' />
                  <span className='text-sm font-semibold text-gray-300'>
                    Step {currentStep} of {steps.length - 1}
                  </span>
                </div>
                {/* Progress indicator */}
                <div className='flex space-x-1'>
                  {Array.from({ length: steps.length - 1 }, (_, index) => (
                    <div
                      key={index}
                      className={`h-2 w-4 rounded-sm transition-all duration-300 ease-in-out ${
                        index < currentStep
                          ? "bg-green-500 shadow-sm"
                          : "bg-gray-300"
                      }`}
                    />
                  ))}
                </div>
              </div>
            )}
            <button
              onClick={handleSkip}
              className='p-1 hover:bg-gray-100 rounded-full transition-colors'
              title='Skip tour'
            >
              <X className='w-4 h-4 text-gray-500' />
            </button>
          </div>

          {/* Content */}
          <div className='mb-4'>
            {/* Client logo for first step */}
            {currentStep === 0 && currentBrand?.client?.logo && (
              <div className='flex justify-center mb-6'>
                <img
                  src={currentBrand.client.logo}
                  alt={`${currentBrand.client.name} logo`}
                  className='h-20 w-auto max-w-64 object-contain rounded-lg'
                  onError={(e) => {
                    // Hide image if it fails to load
                    e.currentTarget.style.display = "none";
                  }}
                />
              </div>
            )}

            <h3
              className={`font-bold text-gray-300 mb-2 ${
                currentStep === 0 ? "text-2xl text-center mb-6 drop-shadow-sm" : "text-lg"
              }`}
            >
              {currentStep === 0 && currentBrand?.client?.name
                ? `Welcome to ${currentBrand.client.name}`
                : step.title}
            </h3>
            <div
              className={`text-gray-100 leading-relaxed font-medium ${
                currentStep === 0 ? "text-base text-left space-y-4 tracking-wide leading-7" : "text-sm"
              }`}
            >
              {(currentStep === 0 && currentBrand?.client?.firstName
                ? step.content.replace(
                    "{firstName}",
                    currentBrand.client.firstName
                  )
                : step.content.replace("{firstName}", "there")
              )
                .split("\n")
                .map((line, index) => (
                  <p key={index} className={index > 0 ? "mt-4" : ""}>
                    {currentStep === 0 ? renderHighlightedText(line) : line}
                  </p>
                ))}
            </div>
          </div>

          {/* Actions */}
          <div className='flex items-center justify-between'>
            {currentStep === 0 ? (
              // Welcome card - only show Start Tour button centered
              <div className='flex-1'></div>
            ) : (
              // Regular tour steps - show Previous button
              <button
                onClick={handlePrevious}
                disabled={currentStep === 0}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  currentStep === 0
                    ? "text-gray-200 cursor-not-allowed"
                    : "text-gray-200 hover:text-gray-900 hover:bg-gray-100"
                }`}
              >
                <ChevronLeft className='w-4 h-4 mr-1' />
                Previous
              </button>
            )}

            <div className='flex items-center space-x-2'>
              {currentStep === 0 ? (
                // Welcome card - Start Tour button
                <button
                  onClick={handleNext}
                  className='flex items-center px-6 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl'
                >
                  Start Tour
                  <ChevronRight className='w-4 h-4 ml-1' />
                </button>
              ) : currentStep < steps.length - 1 ? (
                // Regular tour steps - Next button
                <button
                  onClick={handleNext}
                  className='flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl'
                >
                  Next
                  <ChevronRight className='w-4 h-4 ml-1' />
                </button>
              ) : (
                // Final step - Complete Tour button
                <button
                  onClick={handleNext}
                  className='flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors shadow-lg hover:shadow-xl'
                >
                  Complete Tour
                  <ChevronRight className='w-4 h-4 ml-1' />
                </button>
              )}
            </div>

            {currentStep === 0 ? (
              // Welcome card - empty div for balance
              <div className='flex-1'></div>
            ) : null}
          </div>
        </div>

        {/* Arrow */}
        <div
          className={`absolute w-3 h-3 bg-white transform rotate-45 shadow-lg ${
            step.position === "top"
              ? "top-full -mt-1.5 left-1/2 -translate-x-1/2"
              : step.position === "bottom"
              ? "bottom-full -mb-1.5 left-1/2 -translate-x-1/2"
              : step.position === "left"
              ? "left-full -ml-1.5 top-1/2 -translate-y-1/2"
              : "right-full -mr-1.5 top-1/2 -translate-y-1/2"
          }`}
        />
      </div>
    </>
  );
}
