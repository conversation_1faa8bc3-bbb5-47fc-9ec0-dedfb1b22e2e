# Scoot Insights

A comprehensive marketing insights platform with persona management, brand/client management, and file storage capabilities.

## 📚 Documentation Index

All project documentation has been centralized in the `/README` folder for better organization. Below is an index of all available documentation:

### 📖 Main Documentation
- **[Main README](./README/main-README.md)** - Complete project overview, features, installation, and usage guide

### 🔧 Backend Documentation
- **[Backend README](./README/backend-README.md)** - Backend-specific setup and development guide
- **[API Documentation](./README/BACKEND_API_DOCUMENTATION.md)** - Complete API reference and endpoints
- **[Deployment Guide](./README/DEPLOYMENT.md)** - Production deployment instructions
- **[S3 Setup Guide](./README/S3_SETUP.md)** - AWS S3 configuration and setup

### 🎨 Frontend Documentation
- **[Styling Guide](./README/STYLING_GUIDE.md)** - UI/UX styling guidelines and design system
- **[Component Guide](./README/COMPONENT_GUIDE.md)** - Frontend component documentation

### 🔐 Security & Authentication
- **[Authentication Implementation](./README/AUTHENTICATION_IMPLEMENTATION.md)** - Authentication system details

### 🚀 Deployment & Operations
- **[Deploy Instructions](./README/DEPLOY_INSTRUCTIONS.md)** - Step-by-step deployment guide

### 🤖 AI & Development
- **[Claude Documentation](./README/CLAUDE.md)** - AI assistant integration and usage

## 🚀 Quick Start

1. **Installation**: See [Main README](./README/main-README.md#-installation)
2. **Backend Setup**: See [Backend README](./README/backend-README.md)
3. **S3 Configuration**: See [S3 Setup Guide](./README/S3_SETUP.md)
4. **Deployment**: See [Deploy Instructions](./README/DEPLOY_INSTRUCTIONS.md)

## 🛠️ Tech Stack

- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: Node.js + Express + TypeScript + Sequelize
- **Database**: PostgreSQL
- **Storage**: AWS S3
- **Authentication**: PIN-based system

## 📦 Project Structure

```
scoot-insights/
├── README/                     # 📚 All documentation files
│   ├── main-README.md         # Main project documentation
│   ├── backend-README.md      # Backend documentation
│   ├── API documentation files...
│   └── Setup guides...
├── src/                       # Frontend source code
├── server/                    # Backend source code
└── README.md                  # This index file
```

## 🤝 Contributing

Please refer to the [Main README](./README/main-README.md#-contributing) for contribution guidelines.

## 📄 License

This project is licensed under the MIT License. See [Main README](./README/main-README.md#-license) for details.

---

**For detailed information about any aspect of the project, please refer to the specific documentation files in the `/README` folder.**
